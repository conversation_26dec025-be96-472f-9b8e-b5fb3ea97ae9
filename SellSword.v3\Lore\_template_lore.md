---
name: # e.g., <PERSON><PERSON><PERSON>, History of the Fallen Kingdom
type: Lore
category: # Wyrd | History | Geography | Creature | etc.
# Tiers define Slot Cost and Benefits
tier_1_slot_cost: 1
tier_1_description: # Basic understanding/scope of knowledge at this tier
tier_1_benefit: "Ignore Minor Difficulty (1-2 Difficulty Dice) on related checks."
tier_2_slot_cost: 2 # Total cost for Tier 2
tier_2_description: # Expanded scope/application at this tier
tier_2_benefit: "Ignore Moderate Difficulty (3-4 Difficulty Dice) OR Reduce Complexity by 1 on related checks."
tier_3_slot_cost: 3 # Total cost for Tier 3
tier_3_description: # Mastery/deep insight at this tier
tier_3_benefit: "Ignore Significant Difficulty (5+ Difficulty Dice) OR Reduce Complexity by 1 (potentially 2 for simple tasks) on related checks."
tags: [lore] # Auto-generated based on category
---
# {{name}}

**(Overall description of the Lore domain.)**

## Tier 1: Basic Understanding ({{tier_1_slot_cost}} Slot)
*   **Scope:** {{tier_1_description}}
*   **Benefit:** {{tier_1_benefit}}
*   **(Example Application/Hint):** *(Add text hinting at what ignoring Minor Difficulty enables for this specific Lore)*

## Tier 2: Advanced Application ({{tier_2_slot_cost}} Slots Total)
*   **Scope:** {{tier_2_description}}
*   **Benefit:** {{tier_2_benefit}}
*   **(Example Application/Hint):** *(Add text hinting at what ignoring Moderate Difficulty or reducing Complexity enables for this specific Lore)*

## Tier 3: Mastery ({{tier_3_slot_cost}} Slots Total)
*   **Scope:** {{tier_3_description}}
*   **Benefit:** {{tier_3_benefit}}
*   **(Example Application/Hint):** *(Add text hinting at what ignoring Significant Difficulty or reducing Complexity enables for this specific Lore)*

**(Optional: Add Associated Magic Skills section for Wyrd Lores)**
