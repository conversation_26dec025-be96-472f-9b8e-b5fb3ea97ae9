---
name: # Monster Name
description_short: # 1-2 sentence summary
description_long: # 3-5 sentences with vivid details about appearance, behavior, and distinctive features
size: # e.g., <PERSON>, Small, Medium, Large, Huge, Gargantuan
type: # e.g., Beast, Humanoid, Fae, Undead, Construct, Elemental, Aberration
category: # Fae, Aetherborn, or Primordial
tags: [monster] # Add type tag, e.g., [monster, beast]

# Core Combat Stats
combat_pool: # Base dice pool for most attacks/combat actions (e.g., 4d6)
action_points: # AP per round (e.g., 3, Player Characters typically have 4)
speed: # Movement speed in hexes/units per AP (e.g., 4, or 4 (Climb 4))
wounds: # Wound Threshold pools (e.g., { minor: 2, major: 1 })
#  minor: # Number of Minor wounds (from 1 pen damage)
#  major: # Number of Major wounds (from 2 pen damage)
#  grievous: # Number of Grievous wounds (from 3 pen damage)
#  deadly: # Number of Deadly wounds (from 4+ pen damage)

# Defense
target_difficulty: # Passive defense. Adds Difficulty Dice to attackers (e.g., "TD 1 [Adds 1 Difficulty Die]"). Traits can modify this or add success thresholds.
dr:
  body: # DR for Body hits (e.g., 1)
  voids: # DR for Voids hits (if different, e.g., 0)
  # Add specific resistances/vulnerabilities if applicable (e.g., resist_fire: true, vulnerable_silver: true)

# Weaknesses & Vulnerabilities (Apply penalties to combat_pool for specific Derived Action checks)
weaknesses: [] # List of Derived Actions rolled at -2 dice, min 1 (e.g., [RES, FRT])
vulnerabilities: [] # List of Derived Actions rolled at -4 dice, min 1 (e.g., [INS])
# Actions
# List all actions the monster can perform that cost Action Points (AP).
# This includes standard attacks and special maneuvers previously under Traits.
# Damage Scale Reference:
# Use the following discrete damage scale when assigning damage values to actions:
# *   Level 1 (Nuisance): Damage 3
# *   Level 2 (Minor): Damage 4
# *   Level 3 (Moderate): Damage 5
# *   Level 4 (Serious): Damage 6
# *   Level 5 (Severe): Damage 7
# *   Level 6 (Extreme): Damage 8
# Refer to Monsters/_Monsters_README.md for detailed guidelines on scale usage, Penetration, and Force.
actions: # Renamed from 'attacks' for clarity
  - name: # e.g., Bite, Claw, Rusty Sword, Dirty Trick
    ap_cost: # AP cost for this action (default 1 if not specified, e.g., 1, 2)
    pool_mod: # Modifier to combat_pool for this action (e.g., +1 die, -1 die, default 0)
    reach: # Melee reach (Base Size Reach + Weapon Reach) or range increments (e.g., "PRC x2 / x4 / x8")
    damage: # Base damage value (optional, default 0)
    pierce: # Pierce value (optional, default 0)
    force: # Force value (optional, default 0)
    notes: # Description of the action and any special effects
    extra_success_effects: # Optional list of effects purchasable with extra successes
#      - cost: 1 # Cost in extra successes
#        effect: "Deal +1 damage (if Pierce >= DR)."
#      - cost: 1
#        effect: "Inflict Bleeding 1."
#      - cost: 1
#        effect: "Knock target prone."
  # Add more actions as needed

# Unique Abilities
# These are passive abilities or effects that do not cost AP to use.
monstrous_traits:
  - name: # [[Monstrous_Traits#Trait Name|Trait Name]] (e.g., [[Monstrous_Traits#Unnatural Toughness|Unnatural Toughness]], [[Monstrous_Traits#Iron Aversion|Iron Aversion]])
    description: # Full mechanical effect description, including any monster-specific variations. (e.g., "The monster ignores the effects of being at a specific Wound level (e.g., ignores the penalties for Minor Wounds).", "Suffers -1 die to Combat Pool if adjacent to a character wielding a cold iron weapon. Cold iron weapons ignore the Goblin's TD and deal +1 damage.")
  # Add more traits as needed


# Behavior & Other
tactics: |
  Describe typical combat behavior, priorities, and decision-making process.
loot: |
  List anatomical parts without quantities (e.g., Teeth, Hide, Claws), note crafting potential,
  and use d6 dice notation only for non-anatomical loot (e.g., 2d6 Silver coins).
environment: |
  Describe typical habitats and environmental preferences.
# Optional: Add simple attributes if needed for rare non-combat checks (GM discretion)
# attributes:
#   INT: 0
#   AWA: 1

# Danger Rating
danger_rating:
  offensive: # Calculated offensive rating
  defensive: # Calculated defensive rating
  overall: # Overall danger rating
  tier: # 1-5 (Nuisance, Hazardous, Formidable, Dangerous, Lethal)
  primary_role: # Striker, Brute, Controller, Ambusher, Swarm, Support, Elite, Titan
  secondary_role: # Optional secondary role
  recommended_encounter: # Guidance for encounter building
---
