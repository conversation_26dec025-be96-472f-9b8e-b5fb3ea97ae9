Absolutely! Here's a structured design document summarizing our discussions so far:

---

# TTRPG Design Document

## Overview
This tabletop role-playing game (TTRPG) combines elements from D&D, Pathfinder, and DC20, set in a medieval fantasy world with a Fae-like ambiance. The game features unique races, a flexible attribute system, and a robust combat and magic system. The primary goal is to create a balanced, immersive, and strategic gaming experience.

## Attributes and Character Creation
### Prime Attributes
1. **Strength**: Physical resilience and power.
2. **Agility**: Physical speed and accuracy.
3. **Intelligence**: Mental speed and accuracy.
4. **Spirit**: Mental resilience and willpower.

### Derived Stats
1. **Physique**: Average of Strength and Agility, affects encumbrance and physical skills.
2. **Focus**: Average of Spirit and Intelligence, used for spellcasting and complex mental tasks.
3. **Finesse**: Average of Agility and Intelligence, for precision tasks.
4. **Fortitude**: Average of Strength and Spirit, used for endurance and injury resistance.

### Racial Traits
- **Humans**: Flexible point allocation in attributes.
- **<PERSON>ves (Álf'kin)**: Bonuses in attributes related to agility and intelligence.
- **<PERSON>warves (Dverg'kin)**: Bonuses in attributes related to strength and resilience.
- **Giants (Jötun'kin)**: Bonuses in attributes related to strength and endurance.
- **Gnomes (Dverg'álf'kin)**: Bonuses in attributes related to intelligence and agility.
- **Trolls (Troll'kin)**: Bonuses in attributes related to resilience and strength.

## Equipment
### Weapons
- **Composite Recurve Bow**: Crafted from wood, horn, and sinew. Recurved shape for smooth draw and efficient energy transfer. Reliable for hunting and defense.
- **Padded Jack Armor**: Made from quilted fabric layers, providing lightweight protection and flexibility.
- **Leather Lamellar Armor**: Overlapping leather plates laced together, typically worn over an arming jacket for additional defense.
- **Chainmail Armor**: Interlinked metal rings offering flexible protection, worn over padded garments for added comfort and impact absorption.
- **Half Plate Armor**: Metal plates covering vital areas, worn over an arming jacket or chainmail for a balance of protection and mobility.
- **Brigandine Armor**: Small metal plates riveted to fabric or leather, providing flexible protection often covered with cloth or leather.

### Arrow Types
1. **Barbed Arrows**: Sharp, backward-facing points for maximum damage. Effective against unarmored or lightly armored foes.
2. **Bodkin Arrows**: Narrow, pointed tips for piercing armor.
3. **Swallow Tail Arrows**: Broad, two-bladed heads causing large lacerations. Shorter range, no armor penetration.
4. **Blunt Arrows**: Rounded tips for impact damage without penetration. Shorter range.

## Combat System
### Action Dice
- Characters have 4d6 to spend each round.
- Actions include Active Offense (2d6), Active Defense (2d6), and Reactions (1d6 each).

### Throwing Mechanics
- **Base Throw Distance**: 10 hexes for an average throw with weight 1.
- **Adjustments**:
  - Weight: Each point above 1 reduces distance by 20%.
  - Physique: Each point adds 2.14 hexes.
- **Example**: Physique 7, Weight 2, Rolling 4d6.
  - Total Distance = Base (10 hexes) + Weight Adjustment (-2 hexes) + Physique (7 * 2.14) + Dice Roll (14 on average) = ~31 hexes.

## Magic System
### Material Schools (Matter)
1. **Fire**: Control and create fire.
2. **Earth**: Manipulate and shape earth.
3. **Water**: Control water and its properties.
4. **Air**: Manipulate air and weather.

### Mental Schools (Mind)
1. **Space**: Teleportation, dimensional shifts.
2. **Time**: Time manipulation, foresight.
3. **Pathos (Emotion)**: Manipulate emotions and moods.
4. **Eros (Desire)**: Influence desires and motivations.
5. **Ethos (Authority)**: Enhance credibility and leadership.
6. **Logos (Logic)**: Manipulate logic, enhance reasoning.

## Backgrounds
Players create their own backgrounds, contributing attributes to form an "uber skill."
### Example: Soldier
- **Description**: Fought in a well-regulated army, tested physical and mental endurance over multiple campaigns.
- **Attributes**:
  - **Primary (Endurance 2x)**
  - **Secondary (Strength 1x)**
  - **Tertiary (Resolve 1x)**
- **Roleplay Effect**: PTSD (stress reactions in combat situations).

## Races
### Drakkin Variants
- **Fire Drakkin**: Eld' Drakkin
  - **Elemental Wyrd**: Eldr
- **Earth Drakkin**: Jor' Drakkin
  - **Elemental Wyrd**: Jörð
- **Water Drakkin**: Vatn' Drakkin
  - **Elemental Wyrd**: Vatn
- **Air Drakkin**: Loft' Drakkin
  - **Elemental Wyrd**: Loft
- **Light Drakkin**: Ljós' Drakkin
  - **Elemental Wyrd**: Ljós
- **Darkness Drakkin**: Myrk' Drakkin
  - **Elemental Wyrd**: Myrkur

---

This design document outlines the key elements we've discussed, providing a comprehensive overview of the game's mechanics, races, and systems. If there are any additional details or sections you'd like to include, feel free to let me know!