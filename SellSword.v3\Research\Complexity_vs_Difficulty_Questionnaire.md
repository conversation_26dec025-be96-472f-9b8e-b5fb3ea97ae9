# Complexity vs. Difficulty Questionnaire

## Purpose

To clearly define when to use Complexity vs. Difficulty in the Sellsword system.

## Core Mechanical Distinction

**Difficulty is granular, Complexity is exponential.**

- **Difficulty** reduces the dice pool linearly, with each Difficulty die slightly decreasing the chance of success.
- **Complexity** requires multiple successes, creating an exponential drop in success probability as Complexity increases.

This distinction gives GMs a powerful tool to allow players to attempt ambitious, "rule of cool" actions that could be very effective but carry significant risk of failure. High Complexity actions represent these ballsy, high-risk/high-reward scenarios.

## Questions for Design Consideration

1. **Core Distinction:**
   - Is Complexity about "how many steps/components" while Difficulty is about "how hard each step is"?
   - Or is there another fundamental distinction?

   Nope that's pretty much it. Difficulty refers to adverse conditions that may make a task harder, while complexity refers to additional steps, intricacies, or components that make a task more involved.

2. **Task Resolution:**
   - Does Complexity determine the number of successes needed?
   - Does Difficulty reduce the dice pool before rolling?
   - Can both apply to the same check?

3. **GM Application:**
   - What specific factors should prompt a GM to increase Complexity vs. Difficulty? Intricacy vs. adversity.
   - Are there clear examples for each? Shooting a small target, difficult. Shooting while running, complex.

4. **Skill Interaction:**
   - Do certain skills naturally involve more Complexity than Difficulty or vice versa?
   - How do Lore benefits interact differently with each?
   - Should certain character archetypes be better at handling one versus the other?

5. **Mitigation:**
   - Can players mitigate Complexity and Difficulty differently? Yes, category skills make you better at general tasks (ranged combat), thus adding dice and lowering difficulty, while style skills make you better at specific tasks, thus lowering complexity.
   - Does preparation primarily reduce Complexity while skill/gear reduces Difficulty? either can apply

6. **Scaling:**
   - What are the practical upper limits for each? minor -2, major -4
   - How do they scale with character advancement?
   - Should there be a conversion rate between them (e.g., "this extremely difficult task could alternatively be treated as a moderately complex one")? No

7. **Narrative Impact:**
   - How should Complexity vs. Difficulty affect the narrative description of actions?
   - When a player fails due to high Complexity vs. high Difficulty, how should the failure be described differently? failure is total. the risk of attempting a complex task is that you may NOT succeed partially.

8. **Edge Cases:**
   - Are there situations where only Complexity or only Difficulty should apply? Sure
   - How do time constraints interact with Complexity vs. Difficulty? You can choose to 'Rush' something and make it more complex.
