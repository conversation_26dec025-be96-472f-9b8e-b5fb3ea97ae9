# Equipment

A concise overview of the purpose and scope of the **Equipment** pillar.

## Prerequisites

- Read the [Project Overview](SellSword_v2_Overview.md).  
- Familiarize yourself with the core rules: [Core Rules](_Rules_README.md).

## Folder Structure

- `/Equipment/` – Main directory for this pillar’s entries.  
- `_template_equipment.md` – Template file for creating new equipment entries.  
- Subdirectories:  
  - `Weapons/`  
  - `Armor/`  
  - `Shields/`  
  - `Ammunition/`  
  - `Utility/`  

## Content Overview

- Melee and ranged weapons (`Weapons/`), armor pieces (`Armor/`), shields (`Shields/`), ammunition (`Ammunition/`), and utility items (`Utility/`).  
- Each entry file uses YAML frontmatter to define metadata and stats.  
- Naming conventions: Title case with underscores (e.g., `Longsword.md`).

## Conventions

- **File Naming:** Title case, underscores for spaces (e.g., `My_Equipment_Item.md`).  
- **Frontmatter Fields:**  
  - `name`: Display name  
  - `type`: Category (Weapon, Armor, etc.)  
  - `summary`: Short description  
  - `description`: A player‑facing narrative, setting‑specific text conveying flavor, tone, and context.  
  - `tags`: List of relevant tags  
  - **Stats fields:** offense, damage, pierce, force, block, parry, hook, reach, range, body_dr, voids_dr, carry, encumbrance, fatigue, durability, etc.  
- **Links:** Use Obsidian-style `[[...]]` cross‑references.

## How to Use

1. Navigate to the `Equipment/` directory.  
2. Browse entries by category or name.  
3. Open an entry file to review detailed stats, descriptions, and lore.

## Key Mechanics (Summary)

* **Ratings:** Stats use a 1–3 scale to provide clear, incremental gear progression without overwhelming granularity.  
* **Offense:** Dice bonus to attack rolls—emphasizes the advantage of superior weapon craftsmanship.  
* **Damage:** Fixed base damage, reduced by DR—highlights the interplay of offense and defense.  
* **Pierce:** Reduces the target’s effective DR—encourages strategic targeting choices.  
* **Force:** Captures momentum for knockback or armor sundering—reinforces gritty, visceral combat.  
* **Block/Parry/Hook:** Dice bonuses for active defense—promotes tactical, player‑driven defense options.  
* **Reach:** Extends melee engagement distance—balances weapon selection between reach and maneuverability.  
* **Range:** Multiplier to Precision for ranged attacks—showcases skill and gear synergy in distance combat.  
* **Cover (Shields):** Adds Difficulty dice to attackers—reflects shield‑bearing defensive tactics.  
* **Damage Reduction:** Armor reduces incoming damage but not hit chance—stresses trade‑offs between mobility and protection.  
* **Carry:** Bulk/weight affects AP cost and Fatigue—reinforces resource management and realism.  
* **Encumbrance:** Penalties to PHY, FIN, PRC, and Speed—simulates realistic mobility constraints of heavy gear.  
* **Fatigue:** Reduces max Stamina and increases wager risk—mirrors the physical toll of sustained exertion.  
* **Two‑Handed Use:** Reduces AP cost by 1 (min 1)—incentivizes tactical decisions between speed and control.  
* **Durability:** Measures item resilience and wear—encourages gear maintenance and long‑term planning.  
* **Ammunition:** Defines projectile stats—maintains consistency in ranged combat mechanics.  
* **Stunts:** Grants specialized maneuvers—demonstrates how equipment enhances combat options.

## Armor Conditions – Sunder

* **Level 1 (Minor Sunder):** –1 DR; repair: 1 AP.  
* **Level 2 (Damaged Sunder):** –2 DR; repair: 1 Exploration AP + supplies.  
* **Level 3 (Broken Sunder):** DR 0; repair: downtime + workshop facilities.

Monsters instead suffer –1 DR per Force automatically.

## Cross‑References to Rules and Related Documents

* [[_Rules_README]] – Core combat & exploration.  
* [[_Wounds_README]] – Wound system details.  
* [[_Stunts_README]] – Stunts list and costs.  
* [[_Attributes_README]] – Attributes & Derived Actions.  
* [[_Skills_README]] – Skills acquisition and tiers.  
* [[_Rules_README#Conditions]] – Conditions: Fatigue, Encumbrance, Strain.  
* [[Magic_Guide]] & [[_Lore_README]] – Magic system overview.

## Design Philosophy

Equipment in Sellsword is designed to be both mechanically meaningful and narratively resonant. Gear choice carries real impact—affecting dice pools, resource pools (Stamina/Fatigue), positioning, and available tactics. Armor and weapons are not mere numbers but extensions of character identity and setting verisimilitude: every piece reflects craftsmanship, weight, risk, and tactical value. Durability and repair tie into long‑term campaign resource management, while stunts and special properties ensure that equipment choices unlock unique in‑play options.

## Equipment Design Guide: Step‑by‑Step Justification

Use this guide when creating new items:

1. **Metadata:** Define `name`, `type`, `summary`, and player‑facing `description`.  
2. **Core Stats:** Set carry, str_req, durability, encumbrance, fatigue—balancing realism and game flow.  
3. **Combat Stats:** Determine offense, damage, pierce, force, block, parry, hook, reach, range, cover, body_dr, voids_dr—align numbers with design philosophy.  
4. **Special Properties:** Assign `stunts`, `notes`, `references`, and `tags` to capture thematic and mechanical nuances.

### Example Item: Longsword

(Refer to individual example in `Weapons/Longsword.md` for full justifications.)

## Revision History

- v2 – YYYY‑MM‑DD – Added `description` frontmatter, expanded mechanics with design rationales, and new Design Philosophy section.
