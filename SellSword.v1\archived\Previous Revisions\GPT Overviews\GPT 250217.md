# Overview
## 1. **Attributes & Character Creation**

- **Attribute Scale**: We’ve settled on a range of **1–4** at Level 1.
    - **1** = poor/weak, **2** = average, **3** = good, **4** = great/rare at level 1.
- **Point-Buy**:
    - Everyone starts at 2 in each attribute (or a baseline array).
    - Players receive a small **budget** (e.g., 4 points) to raise attributes (2→3 costs 1 point, 3→4 costs 2 more, etc.).
    - They can also drop an attribute from 2→1 to gain extra points if desired.
- **Result**: A Level 1 character typically has mostly **2s**, maybe one **4**, or a couple of **3s**.

---

## 2. **Dice Resolution: Dice Pool System**

- **Dice Pool**: Characters roll a number of six-sided dice (d6).
    - **Each 6 = 1 success**
    - **Each 1 can cause fatigue/equipment wear** (depending on the context).
- **Base Dice**: Derived from combining relevant **attributes** (or an archetype sum), **skills**, plus any **gear** or resource dice.
    - We often aim for ~5–10 dice in typical rolls, capping the pool around **12** to keep it manageable in person.
- **Success Thresholds**: Tasks require a certain number of successes (often 1–3).
    - More successes = better outcome.
    - Fewer successes or zero = partial or complete failure.

_(We previously considered 3d6 roll-under, but the current emphasis is on the dice-pool approach.)_

---

## 3. **Skills**

- **Skill Ranks**: Typically **Rank 1–3**, each rank adding to your pool or granting special benefits.
- **Fewer, More Impactful Skills**: We want characters to have **around 5–8 total skill picks** by high level—**not** dozens.
- **Slot-Based or Minimal Point System**:
    - Each skill rank costs a small number of “slots,” e.g. Rank 1 = 1 slot, Rank 2 = total 3, Rank 3 = total 6.
    - At character creation, you get a few slots (e.g., 3 + (INT – 2)), gaining a few more at certain level milestones.
- **Effect**: A skilled character invests in fewer, focused abilities. A high-INT character has slightly more skill slots but still can’t master everything.

---

## 4. **Resources: Stamina & Will**

- **Stamina (Physical)**:
    - Used to **boost physical actions** by adding extra dice to the pool (at risk of rolling 1s and losing stamina).
    - Also spent for **universal “bursts”** (e.g., gain extra movement or ignore a wound penalty) or for special maneuvers unlocked by certain skills.
- **Will (Mental)**:
    - Similar concept for **mental or social feats**.
    - Boost a persuasion attempt, resist fear, maintain concentration on a spell, etc.
- **Caps & Risk**: Typically you can only spend up to 3 Stamina/Will dice on a single action (unless a skill/feat expands it). Rolling 1s can deplete the resource.

---

## 5. **Equipment & Gear Dice**

- **Equipment Dice**:
    - Weapons or tools often grant +1 to +3 dice (swords, daggers, rope, lockpicks, etc.).
    - Heavier or more specialized gear can have “fatigue” or “fragility” drawbacks (rolling 1s can break the item or add extra stamina drain).
- **Armor**:
    - Provides strong defensive benefits but may impose a “fatigue” effect (1s and 2s on stamina dice cause loss).
- **Overall**: The system emphasizes **gear importance** without letting huge weapons simply do “more damage.” Instead, they might allow bigger stamina spends, provide a defensive bonus, or other tactical perks.

---

## 6. **Action Economy**

- We discussed either having “action dice” or a **simple 3–4 actions** per turn.
- **Current Lean**: Drop action dice from the pool and just let each character **perform a set number of actions** each round (e.g., 3–4).
    - Certain skills may reduce an action’s cost or let you do multiple things more efficiently.
    - This avoids ballooning dice pools from extra “action dice.”

---

## 7. **Level Progression**

- **Max Level** ~10.
- Characters receive:
    1. **Small skill slot increases** at certain levels (so final skill total is ~7–9 slots for a high-INT character).
    2. **A few attribute bumps** (maybe +1 at levels 3, 6, 9), so eventually you can reach 4 in multiple stats if you choose.
- The design encourages a mix of **horizontal growth** (new skills) and **light vertical growth** (small attribute improvements).

---

## 8. **Philosophical Goals**

2. **Verisimilitude**: Stats and success rates feel grounded in realistic human ranges (1–4 for normal folks).
3. **No “Dump” Stats**: All attributes matter; multiple attributes can factor into an action, plus stamina/will usage.
4. **Equipment Matters**: Gear confers tangible dice bonuses or special properties, not just damage dice.
5. **Manageable Dice Pools**: Hard caps (like 12 dice) keep physical rolling feasible, with optional rules to convert “excess dice” into partial successes or re-rolls if it exceeds the cap.
6. **Skill Relevance**: Fewer, bigger skills that meaningfully impact actions and resource usage.
7. **Resource Gamble**: Spending stamina/will is exciting—players get an edge but risk fatigue or depletion.

---

### **In Short**

The system is shaping up as a **dice-pool TTRPG** (roll d6, 6 = success) where **attributes (1–4)** form your base, **skills** add further dice or special maneuvers, and you have **stamina/will** resources to push your luck. **Gear** is valuable, and you’ll **cap** total dice to keep it quick at the table. **Leveling** grants limited attribute bumps and skill slots, ensuring that even at higher levels, a character has at most a handful of potent skills.

# 1. **Attributes: Scale, Meaning, and Usage**

### 1.1 Core Attributes

Your system uses a set of **physical** and **mental** attributes, each rated on a **1–4** scale at Level 1. (Later levels may allow raising a stat 
to 5, but that’s considered a rare or elite level.)

A typical set of attributes might include:

- **Endurance (END)** – Physical stamina, ability to sustain effort or resist fatigue.
- **Strength (STR)** – Physical power, capacity to lift or push heavy objects.
- **Agility (AGI)** – Quickness of movement, reflexes, and overall bodily coordination.
- **Dexterity (DEX)** – Fine motor skills, precision, manual control over tools/weapons.
- **Intellect (INT)** – Cognitive ability, reasoning, and capacity for complex information.
- **Acuity (ACU)** – Perceptual sharpness, awareness, ability to observe quickly.
- **Concentration (CON)** – Mental focus, discipline, will to maintain tasks under stress.
- **Presence (PRE)** – Personal magnetism, emotional intelligence, and force of personality.

_(Exact naming and number of attributes can vary, but the key point is that each covers a broad domain of capability.)_

### 1.2 How Attributes Contribute to Actions

When a character attempts something, you typically **combine two relevant attributes** (or use a derived archetype) to form the **base dice** in your dice pool. For example:

- **Physique** = Strength + Agility → climbing a wall, leaping a gap, or grappling.
- **Finesse** = Agility + Dexterity → delicate tasks like lockpicking or precise archery.
- **Resolve** = Endurance + Presence → pushing through fear or pain.

Alternatively, you might directly reference a single attribute (e.g., a purely mental puzzle might lean on Intellect alone), then **add skill dice, resource dice (Stamina/Will), and gear dice** as needed. The exact formulas can vary, but the guiding principle is that **multiple attributes** matter for most significant tasks—no single stat stands alone.

### 1.3 The 1–4 Scale and Real-World Analogy

Early in the design, we wanted each attribute rank to represent a **recognizable tier** of human performance, grounded in real-world parallels:

- **1 (Below Average/Weak)**
    - Roughly bottom 10–20% of a population.
    - Could be someone with a glaring deficiency, someone sickly, or highly untrained in that sphere.
- **2 (Average)**
    - Center mass of the population. Most everyday people fall here in areas they haven’t specialized in.
- **3 (Good/Above Average)**
    - The top ~20% bracket in real-world terms—someone who’s put dedicated effort into building that attribute or naturally excels.
- **4 (Great/Exceptional)**
    - Represents a smaller slice of the population—maybe top 5%. Skilled athletes, specialized professionals, or naturally gifted people.
    - Not superhuman, but definitely notable in day-to-day contexts.

By capping at 4 at Level 1, you preserve the idea that brand-new characters can be “very good” but **not** outright legendary. This also keeps your **dice pools under control** at early levels. As characters progress, they might reach 5 in an attribute (very rare, akin to a world-class athlete or genius), but that’s typically at mid-to-late game.

### 1.4 Why 1–4?

8. **Clarity and Simplicity**: A tight scale of 1–4 is easy to grasp: 2 is normal, 3 is notably better, 4 is approaching the best you’ll typically see in a “realistic” setting.
9. **Grounded Benchmarks**: Each step on the scale corresponds roughly to a percentile bracket of human ability. Players can imagine real people who exhibit these levels of strength, agility, intelligence, etc.
10. **Smooth Progression**: With only three steps above “poor” (1→4), each point gained is **very meaningful**, ensuring players feel each improvement.
11. **Keeps Dice Pools Manageable**: Because each attribute rarely exceeds 4, the **sum** of two attributes is usually around 3–7 at Level 1, which translates to a moderate starting dice pool before adding skill or resource dice.

---

### In Short

- **Attributes** define broad human capabilities (physical, mental, social).
- **1–4** is intentionally small, mirroring real-world variance—from weak/underdeveloped (1) up to strong/experienced (4).
- Combining attributes for actions ensures **no single stat** dominates, reflecting how real tasks are multifaceted.
- This scale keeps character creation approachable and in line with believable human performance, while also preventing runaway dice pools at lower levels.

# **2. Setting Difficulty with Dice Penalties and Multiple Successes**

### **2.1 Subtracting Dice for Environmental / Situational Factors**

**Concept**: You keep the requirement at “roll **1 success**,” but the GM **removes dice** from the pool to reflect tougher conditions.

1. **Typical Difficulty Penalties**
    
    - **–1 die**: Minor inconvenience or mild distraction (slippery floor, light rain, slight rush).
    - **–2 dice**: Noticeably harder situation (heavy rain, darkness, moderate haste, some obstacles).
    - **–3 dice**: Severe disadvantage (storm, chaotic battlefield, severe distraction, intense time crunch).
    - **–4 dice** or more: Extremely punishing environment (pitch-black, raging winds, catastrophic chaos).
2. **Example Probabilities**  
    Assume a character’s normal pool is **8 dice** (without penalties). The table below shows the approximate chance of getting **≥1 success** if we subtract dice.
    
    |**Effective Dice**|**Chance of ≥1 Success**|
    |:-:|:-:|
    |8 (no penalty)|~77%|
    |7 (–1 die)|~72%|
    |6 (–2 dice)|~66%|
    |5 (–3 dice)|~60%|
    |4 (–4 dice)|~52%|
    
    As you can see, each die you remove reduces the success probability by a relatively **gradual** step, which many GMs find more realistic for small changes in environment or timing.
    
3. **When to Use Subtracting Dice**
    
    - **Environment**: Weather, lighting, cramped quarters, loud noise, or other external hindrances.
    - **Time Pressure**: Doing the same action but with a strict countdown or urgency.
    - **Minor & Moderate “Everyday” Hardships**: If the challenge is just “tougher than usual,” subtract a few dice.

### **2.2 Requiring Multiple Successes for Complex or Heroic Feats**

**Concept**: Characters still roll their **full pool**, but the GM demands more than **1 success** to succeed. This represents tasks where you must excel on multiple fronts simultaneously, or hit multiple precise points in one go.

4. **Common Tiers**
    
    - **2 Successes**: A notably tough, complex action (tightrope walking above a chasm, shooting a tiny target at range).
    - **3 Successes**: Very rare feats requiring truly elite performance.
    - **4+ Successes**: Borderline legendary or pivotal moment. Characters might need to combine resources or get help.
5. **Example Probabilities**  
    With a **base pool of 8 dice** (no penalty), the approximate success chances are:
    
    |**Successes Required**|**Chance** of Meeting/Exceeding|
    |:-:|:-:|
    |1 Success|~77%|
    |2 Successes|~39%|
    |3 Successes|~14%|
    
    Notice how the difficulty **jumps significantly** as you demand each additional success.
    
6. **When to Use Multiple Successes**
    
    - **Intrinsically Complex Tasks**: Reaching a high bar, e.g., flipping twice in midair while landing a precise dagger strike.
    - **High-Stakes Showstoppers**: The “impossible shot,” feats of acrobatics that boggle the mind, or final heroic leaps.
    - **Narrative Moments**: If success should feel truly epic, requiring more successes can deliver big drama.

### **2.3 Combining Both Methods**

Often, you’ll **only use one** approach for a single roll: either subtract some dice or require multiple successes. But sometimes you might do **both**:

7. **Subtract Dice** for outside conditions (darkness, pressure).
8. **Require 2 or 3 Successes** because the **task itself** is multifaceted or exceptionally demanding.

**Example**:

- The party rogue tries to **pick a complex dwarven lock** (2 successes needed due to intricate mechanisms) **while being chased** (–2 dice for extreme haste).
- The rogue’s typical 8-die pool drops to 6 dice, and they must roll at least 2 successes. This is tough!

### **2.4 Practical Guidelines for GMs**

9. **Start Small**
    
    - If you’re new, keep it simple: for most tasks, **1 success needed**, maybe –1 or –2 dice if it’s a bit harder.
    - Only jump to requiring 2+ successes if you genuinely want a big difficulty spike.
10. **Communicate Clearly**
    
    - Let players know why you’re subtracting dice (“The rain is pouring and time is tight, so take –2 dice”).
    - If you set a multi-success requirement, tell them up front (“This is really advanced— you’ll need 2 successes!”).
11. **Match the Fiction**
    
    - If it’s a single, straightforward motion complicated by environment, use **dice penalties**.
    - If it’s multiple steps or a skill that must be performed perfectly on multiple levels, use **multiple successes**.
    - Combine both only for big set-piece moments or to highlight a truly extreme challenge.
12. **Balance Tension vs. Fairness**
    
    - Subtracting dice yields incremental changes and helps players “gauge” how tough a situation is.
    - Multiple successes can feel exhilarating or punishing—reserve it for challenges that truly warrant that sense of heightened drama.
13. **Encourage Creativity**
    
    - If players face both a dice penalty and a multi-success requirement, they’ll likely look for ways to **gain advantages**: using gear, magic buffs, or help from allies. Reward that teamwork and problem-solving.

---

### **Takeaways for the GM**

- **Subtracting Dice**: Great for modeling poor conditions, time pressure, or simpler incremental difficulty.
- **Requiring Multiple Successes**: Perfect for tasks that are inherently multi-layered, extremely precise, or critical to the storyline’s climax.
- **Use Both**: Reserve the combo for moments you genuinely want to feel epic, challenging, and potentially game-changing.

By mixing these two difficulty methods and applying them in context, you’ll create a **dynamic, flexible** system that mirrors real-world challenges (dice penalties) and cinematic, high-drama feats (multiple successes)—all while keeping your dice pools intuitive for players.

## **Optional Co-Op / “Off the Ball” Actions**

### 1. Granting Bonus Successes

- **Concept**: Instead of giving dice to the active roller, teammates **spend their action** to attempt a **supporting maneuver** that effectively contributes **1 success** (or even 2, if it’s especially heroic) to the main goal.
    
- **Examples**:
    
    1. **Knock the Cyclops Off-Balance**: A fighter might distract the cyclops or smash its knee, awarding the archer **+1 success** toward the “hit its eye” challenge.
    2. **Pin the Cyclops’s Arm**: A second fighter grapples or restrains it, preventing the monster from shielding its eye, again granting +1 success.
    3. **Distract or Illuminate**: A spellcaster uses a flash of light that briefly highlights the cyclops’s eye, also yielding +1 success to the archer’s shot.

### 2. Potential Constraints

1. **Limit the Number of Aids**:
    - Restrict the total bonus successes to **1** or **2** so it doesn’t trivialize big tasks.
    - Alternatively, let each character’s supporting action require them to **roll** a simpler check (maybe 1 success needed) before awarding their bonus success.
2. **Require a Relevant Action**:
    - The supporting player must describe how they’re assisting. _“I wave a torch to blind the cyclops.”_ or _“I trip the cyclops so it staggers right into the shot.”_
3. **Costs / Risks**:
    - If the helper fails their support check, perhaps it imposes a penalty or triggers a complication. This keeps things dramatic.

### 3. Balancing Benefits and Narrative

- **Reward Creativity**: By letting other players use their own skills (stealth, illusions, raw strength, etc.), you invite creative teamwork.
- **Immersive**: Big group takedowns or heroic, multi-step tasks now feel collaborative instead of a “lone hero” moment.
- **Scales Well**: If you need 3 or 4 successes to accomplish something, seeing the team each chip in a success or two feels more cinematic.

---

**In Short**  
Using **cooperative contributions** to high-threshold tasks (2+ required successes) is an excellent way to encourage teamwork, creativity, and cinematic “group synergy.” You preserve the challenge, while ensuring that **everyone** in the party can meaningfully participate—even if they’re not the “main roller.”

# **3. Skills and Knowledge**

### **3.1 Distinguishing Skills from Lore**

- **Skills**:
    
    - Broader proficiencies (e.g., Archery, Alchemy, Oration, Polearms, Acrobatics) that reflect practical training or specialized techniques.
    - Gained and improved via a **universal slot system** that levels up with the character.
    - Harder to “swap out,” because these are ingrained abilities.
- **Lore (Knowledge)**:
    
    - Represents narrower pieces of information, languages, or fields of study (e.g., “Dark Caverns Lore,” “Arcane Runes,” “Elvish Language”).
    - Tied to a **Knowledge Resource** (1 + INT×2), functioning like an “inventory” for mental space.
    - Easier to rotate or replace with new Lore—though advanced or deeply studied lore may take more effort to swap out.

By differentiating these two, a **low-INT** character can still excel at multiple skills, while a **high-INT** character has more “mental capacity” for deeper or wider sets of knowledge.

---

### **3.2 Skill Slots: Universal Progression**

1. **Base Skill Slots** at Level 1
    
    - Every character starts with a certain number of skill slots—e.g., **3** or **4**, depending on how quickly you want them to specialize.
    - This ensures no one is locked out of the skill system, regardless of their attributes.
2. **Gaining Slots with Levels**
    
    - At each level or major milestone, characters earn **+1 (or more) skill slot**.
    - They can **learn a new Rank 1 skill** or **improve an existing skill’s rank** with these new slots.
3. **Ranks and Slot Costs**
    
    - **Rank 1** costs **1 slot**.
    - **Rank 2** costs a total of **3 slots** (1 + 2).
    - **Rank 3** costs a total of **6 slots** (1 + 2 + 3).
    - Example: If you have 3 slots at Level 1 and spend all 3 on “Archery,” you’d have **Archery Rank 2**.
4. **Why This Matters**
    
    - Players can **freely pick** or improve skills that suit their concept: “Polearms + Athletics,” “Stealth + Mummery,” or “Alchemy + Swordsmanship,” etc.
    - No attribute (like INT) gates these choices, ensuring a truly **classless** design.

---

### **3.3 Lore (Knowledge) as Mental Inventory**

1. **Knowledge Resource Formula**
    
    - **Knowledge = 1 + (INT × 2)**
    - Ranges typically from 3 (INT=1) to 9 (INT=4), providing a **“Lore capacity.”**
2. **Acquiring and Swapping Lore**
    
    - **Each piece of Lore** (or a language) occupies **1 (or more) slot** in that capacity.
    - A character might fill these slots with “Basic Herbalism (1 slot), Elvish Language (1 slot), Bestiary—Swamp Creatures (1 slot),” etc.
    - **Swapping**: Lower-level lore (1 slot) can be replaced or “forgotten” with some downtime or training, while advanced or “deeply studied” lore might be more rigid.
3. **Using Lore in Play**
    
    - Knowledge can provide **small dice bonuses**, let you attempt tasks otherwise impossible (translating runes, identifying rare fungus), or uncover crucial info.
    - If the party expects an upcoming desert expedition, someone might swap out a minor lore for “Desert Survival Lore,” provided they have the time to research.
4. **Balancing Lore**
    
    - High-INT characters can “carry” more topics, shining in investigative or academic situations.
    - Low-INT characters still have at least a few lore slots, so they’re not locked out of specialized knowledge entirely.

---

### **3.4 Skill vs. Lore: Practical Examples**

1. **Barbarian (INT=1) at Level 1**
    
    - **Skill Slots**: 3
        - Might take **Polearms 2** (all 3 slots).
    - **Knowledge Resource**: 3
        - Could fill “Local Beast Lore (1 slot), Basic Mythology (1 slot),” leaving 1 free or used for a language.
    - They rely on raw skill in battle, with minimal but still relevant knowledge sets.
2. **Wizard (INT=4) at Level 1**
    
    - **Skill Slots**: 3
        - Might choose “Spellcasting 2” or spread out among “Spellcasting 1 + Alchemy 1 + Arcane Theory 1.”
    - **Knowledge Resource**: 9
        - Plenty of capacity for “Ancient Runes (1), Arcane Rites (2?), Draconic Language (1), Summoning Lore (2?), Basic Politics (1), etc.”
    - Emphasizes their deep or varied academic background.

---

### **3.5 GM Guidance: Implementation**

1. **Grant Skill Slots** at level-ups (or partial increments) so players steadily improve their core talents.
2. **Encourage** players to find or train new Lore if upcoming adventures demand specialized knowledge (caverns, vampiric crypts, arcane gates, etc.).
3. **Limit** rapid Lore swapping for higher-tier knowledge to maintain realism and reward foresight—maybe a session or downtime requirement to “forget and re-learn.”
4. **Distinguish** skill-based checks (athletic feats, weapon usage) from lore-based checks (identifying runes, recalling historical details). Often both might combine for a final dice pool.

---

### **3.6 Conclusion of the Skills & Knowledge System

- **Skills**:
    
    - Gained via **universal slot progression**.
    - Reflect practical training that’s harder to swap out.
    - Encourages every build to shape their specialties, regardless of attributes.
- **Lore**:
    
    - Governed by a **mental “inventory”** (Knowledge Resource = 1 + INT×2).
    - Occupies “slots” for narrower or more academic knowledge, languages, or arcane theories.
    - **Flexible** but still balanced by downtime and slot limits, letting high-INT characters excel in broad info without blocking low-INT heroes from chosen skill sets.

This design preserves a **classless approach** while ensuring **Attributes** (especially INT) remain impactful for knowledge-based tasks, and letting **all** players have equal opportunity to specialize in the skills that define their character archetypes.

## **Skill Primer (Revisited)**

### **1. Core Philosophy: “Skill = Do, Lore = Know”**

1. **Skills**:
    
    - Represent learned, trainable _actions_ and techniques.
    - E.g., “Survival,” “Alchemy,” “Melee Combat,” “Diplomacy,” “Stealth,” etc.
    - Scaled in **ranks** (1–3), granting bigger dice bonuses or special maneuvers at higher ranks.
2. **Lore**:
    
    - Represents narrower sets of _information_, historical or specialized knowledge.
    - E.g., “Herbalism,” “Woodcraft Lore,” “Ancient Languages,” “Noble Etiquette,” etc.
    - Tied to your **Knowledge Resource** (1 + INT×2). Each Lore item costs 1–2 slots.
    - Doesn’t usually grant a large direct bonus, but it can **remove penalties**, offer synergy, or grant unique insights.

**Key**: If your concept revolves around _performing or producing something_ in an active, skillful way, it’s a **Skill**. If it’s about _possessing specialized knowledge_, it’s **Lore**.

---

### **2. Skill Ranks and Growth**

#### 2.1 Rank Structure

- **Rank 1**: Basic proficiency. +1 die or a modest bonus to relevant actions. You can often spend up to **1 Stamina/Will** on that action (if it normally wouldn’t allow it).
- **Rank 2**: Advanced mastery. Typically +2 dice, plus a **signature maneuver** (e.g., ignoring moderate penalties, spending 1 Stamina to reduce action cost, forging items faster, etc.).
- **Rank 3**: Elite/Expert. +3 dice total, plus a major **advanced perk** (e.g., expanded stamina usage, special critical rules, synergy with other skills).

#### 2.2 Slot System

- Each character has a **universal pool of skill slots** (e.g., 3 at level 1, +1 per level).
- **Rank Costs**:
    - Rank 1 = 1 slot
    - Rank 2 = 3 total slots (1 +2)
    - Rank 3 = 6 total slots (1 +2 +3)
- **Leveling**: At each level, you gain new skill slots—improve existing skills or pick up new rank-1 skills.

#### 2.3 Example Skill Progressions

1. **Archery**
    
    - **Rank 1**: +1 die for bow shots; can add up to 1 Stamina die for a Finesse-based archery roll.
    - **Rank 2**: +2 dice total; special maneuver to ignore moderate range penalties or spend 1 Stamina to reduce action cost.
    - **Rank 3**: +3 dice total; can spend up to 3 Stamina for a single shot, or gains an advanced critical range (roll of 5–6 = critical).
2. **Survival** (a broad “wilderness” skill)
    
    - **Rank 1**: Basic foraging, safe campsite, ignoring mild climate penalties.
    - **Rank 2**: Faster or more effective tracking, able to feed a larger party, can mitigate moderate hazards.
    - **Rank 3**: Resist severe climates with minimal penalty, guide entire caravans effectively, possibly reduce action costs for advanced survival tasks.

---

### **3. Using Lore to Complement Skills**

#### 3.1 Lore as Narrow Knowledge

- **Lore** focuses on _knowing_, not _doing_. If you have “Herbalism (Lore),” you’re adept at identifying plants or knowing their medicinal/poisonous properties.
- This knowledge can **enhance** a relevant Skill roll but is _not_ required to perform the action.

#### 3.2 Distinguishing Overlaps

- **Alchemy (Skill)** vs. **Herbalism (Lore)**:
    - With **Alchemy (Skill)**, you can brew potions, craft recipes, etc. You inherently know some basic theory to do so.
    - **Herbalism (Lore)** can help you gather rare plants or identify new ingredients. It might give you a **small synergy bonus** or remove time/penalties if you also have Alchemy.
- **Survival (Skill)** vs. **Woodcraft (Lore)**:
    - With **Survival** skill, you can track game, build shelter, gather resources.
    - **Woodcraft Lore** might make you specifically knowledgeable about certain forest hazards, unique plants, local fauna legends, etc. You can get minor bonuses or remove situational penalties in a dense forest scenario.

#### 3.3 Avoiding “Skill + Lore” Redundancy

- **No “Alchemy Lore”** if Alchemy is a skill. The skill inherently covers the base knowledge needed to do the job.
- **Lore** is useful if it broadens your knowledge _outside_ what the skill covers, or helps with _other_ skill contexts (e.g., herbal knowledge helps with Physik or cooking).

---

### **4. Fewer, Broader Skills**

#### 4.1 Consolidate Actions

- Instead of many small skills (Tracking, Hunting, Camp Craft, Foraging), unify them into a single skill—**“Survival”**—and rely on Lore for specialized variations (Desert Lore, Woodcraft, Sandstriding).
- For combat, instead of a different skill for every weapon type, try broad categories: “Melee Combat,” “Ranged Combat,” or “Polearms,” “Swordsmanship.”
- This keeps skill lists shorter, letting players see key action categories easily.

#### 4.2 Example Skill List (Not Exhaustive)

- **Melee Combat** (Rank 1–3)
- **Ranged Combat** or subcategories like Archery
- **Survival**
- **Stealth**
- **Alchemy**
- **Engineering**
- **Physik (Medical)**
- **Diplomacy** or **Oration**
- **Spellcasting** (if relevant)

Lore topics (1–2 slots each) can address special knowledge in those same domains: “Herbalism,” “Woodcraft,” “Siege History,” “Court Etiquette,” “Arcane Theory,” etc.

---

### **5. Putting It All Together**

1. **Skill = “Do”**
    
    - Rank system up to 3.
    - Each rank adds dice and grants special maneuvers or resource usage.
    - Fewer, broader skill categories so players can shape a unique build without skill bloat.
2. **Lore = “Know”**
    
    - Tied to INT-based knowledge resource.
    - Smaller, narrower topics (Herbalism, Woodcraft, Local Traditions).
    - Grants synergy or removes penalties but not a massive direct dice bonus.
    - No separate “Alchemy Lore” if Alchemy is a skill—no duplication.
3. **Fewer Skills, More Lore**
    
    - This approach fosters a streamlined system: skills handle action breadth, while lore handles specialized or environmental knowledge.
    - Encourages synergy (e.g., “I have Surival + Woodcraft Lore, so I excel in deep forest conditions”).
4. **Examples**
    
    - **Alchemy (Skill)** + **Herbalism (Lore)**: You craft potions effectively, and also identify exotic plants for advanced recipes.
    - **Survival (Skill)** + **Woodcraft (Lore)**: You can _perform_ wilderness actions anywhere, with extra advantage in forest conditions.

By adopting **fewer, broader Skills** alongside a flexible array of **Lore** topics, you maintain clarity: **Skills** revolve around the _how_ of doing, while **Lore** covers the _what and why_ of specialized knowledge—ensuring minimal redundancy and an easier time for both GMs and players to understand their roles in play.
# **4. Stamina and Will**

### **4.1 Overview and Purpose**

1. **Resources for Effort**
    
    - **Stamina** (physical) and **Will** (mental) represent your character’s reserve of exertion.
    - Spending them can either **add dice** to certain rolls or **unlock extra actions** (short bursts of speed, feats of concentration, special maneuvers).
2. **Base Calculations**
    
    - **Stamina** = 1 + (Endurance × 2). Typically **3–9** at Level 1.
    - **Will** = 1 + (Concentration × 2). Also **3–9** at Level 1.
    - These resources may fluctuate during play (e.g., losing stamina on a critical failure, or recovering it after a rest).
3. **Risk and Reward**
    
    - Adding more dice can spike your chance of success but also your chance of rolling 1s—leading to potential resource loss or fatigue.
    - The GM might allow “spending” a point of stamina/will outright for specific maneuvers (e.g., dashing extra distance, ignoring distractions).

---

### **4.2 Spending Stamina/Will for Dice Pool Boosts**

4. **Wagering Stamina on Physical Actions**
    
    - By default, **only Fortitude (FRT)** or **Physique (PHY)** actions can incorporate stamina dice.
    - A character declares how many stamina dice they wish to add, up to a certain cap.
        - **Cap**: Typically, **3** stamina dice max for a single action unless a skill says otherwise (some might allow 2 or 1 by default).
    - **Rolling 1s** on these stamina dice can reduce your total stamina pool, reflecting exertion or exhaustion.
5. **Wagering Will on Mental/Social Actions**
    
    - By default, only **Focus (FCS)** or **Resolve (RES)** actions can incorporate will dice.
    - A similar cap of **3** will dice typically applies, unless a skill or special ability increases it.
    - Rolling 1s on will dice may lower your will pool, representing stress or mental strain.
6. **Skill-Based Expansion**
    
    - Certain skills allow you to **spend stamina/will** on other types of actions.
    - **Example**: Archery might let you spend up to its skill level in stamina dice on a **Finesse (FIN)** roll, whereas normally stamina wouldn’t apply to Finesse actions.
    - This means a character with **Archery 2** can spend 2 stamina dice on a Finesse-based archery shot to push for higher success chances.
7. **Trade-offs**
    
    - Using these resources can drastically improve success odds **this moment**, but recurring 1s or forced usage can drain them for future rolls.
    - Encourages **strategic usage** of resources: do you blow your stamina early in the fight or save it for a critical finishing move?

---

### **4.3 Spending Stamina/Will for Extra Actions or Maneuvers**

8. **Doing More Than Just Adding Dice**
    
    - Characters can **spend** (rather than “wager”) a point (or die) of stamina or will to perform unique maneuvers:
        - **Physical**: A short sprint adding extra movement, a sudden leap, ignoring a wound penalty.
        - **Mental**: Casting a spell more quickly, resisting intimidation, maintaining concentration under duress, etc.
9. **Skill-Restricted Abilities**
    
    - Many advanced maneuvers require a **minimum skill rank** to reduce or bypass the normal action cost.
    - **Example**: Polearm Mastery (Rank 2) might allow spending 1 stamina to reduce the action cost of a polearm strike from 2 to 1.
    - If you haven’t trained that skill, you can’t apply stamina in that special way—only general usage (Fortitude/Physique) is available.
10. **The Action Economy**
    
    - Each round, you usually have a set number of actions (e.g., 3–4). Spending stamina/will might let you do a **bonus** movement or reduce an action’s cost.
    - This ensures **high-resource** characters can occasionally push beyond normal limits—faster combos, forced marches, or mental endurance.
11. **Narrative and Team Synergy**
    
    - A cunning ally might coordinate with you, opening an opportunity where spending your stamina for a special lunge strike matters more.
    - Reflects those dramatic, heroic bursts—like a warrior fighting on despite wounds, or a mage summoning willpower for one last spell.

---

### **4.4 Limits on Resource Usage and Skill Requirements**

12. **Default Limit: 3 Dice**
    
    - By default, you can only wager up to **3** stamina/will dice on a single action—**unless** your skill or a special ability modifies this.
    - This prevents enormous dice pools from overshadowing regular checks, keeps resource usage within reason.
13. **Spending vs. Wagering**
    
    - **“Wagering”** means rolling those dice (and risking losing them on 1s).
    - **“Spending”** means you **pay** a resource cost outright (e.g., 1 stamina point) to do something automatically (like an extra 2 hexes of movement). No risk of rolling a 1 in this case, but you lose that resource anyway.
14. **Skill-Level Doors**
    
    - If a skill says “spend up to its rank in stamina,” that means a character with **Archery 2** can wager 2 stamina dice on a Finesse-based arrow shot, not 3. A character with Archery 1 can only wager 1.
    - This sets **natural progression**: deeper skill ranks = higher resource usage = bigger heroic feats.
15. **Potential Recovery Mechanics**
    
    - GMs should define how quickly stamina/will recover. E.g., partial returns after short rests, full returns after long rests, or special items. This calibrates how often players can push themselves.

---

### **4.5 Example of Usage**

16. **Novice Archer (Archery 1)** wants to shoot a fleeing bandit.
    
    - **Base**: Finesse (FIN) dice pool, plus 1 die from Archery skill.
    - **Stamina**: By default, you can’t spend stamina on Finesse. But Archery (Rank 1) allows **1** stamina die on that roll.
    - If they roll a 1 on that stamina die, they lose it from their total pool. If they get a 6, it’s an extra success chance.
17. **Seasoned Duelist (Swordsmanship 2)** defends against multiple attackers.
    
    - They can “spend” 1 stamina point (not dice) to block an incoming blow more cheaply, if their skill allows it. Or possibly “wager” up to 2 stamina dice on a Physique-based parry.
18. **Steadfast Cleric (Resolve)** tries to stand unwavering in a fear aura.
    
    - By default, they can wager up to **3** will dice for a Resolve check. If they have a skill that specifically expands this usage to a social or mental action that normally wouldn’t allow will, they can do so.

---

### **4.6 Summary and GM Tips**

- **Stamina** is physically oriented (Fortitude, Physique), **Will** is mentally oriented (Focus, Resolve).
- Both can be **wagered** for extra dice (risking depletion on 1s) or **spent** for special maneuvers.
- **Skills** unlock usage of these resources on other action types (e.g., Archery letting stamina power Finesse).
- A **hard cap** or **skill-based limit** keeps resource spending from exploding into unstoppable feats.
- Encourage players to weigh the **risk** of losing resources vs. the **reward** of a crucial success or heroic flourish.

By distinguishing **wagering dice** (with risk) vs. **spending points** (automatic cost), and by limiting usage through skill ranks, you preserve strategic tension and ensure characters feel distinct based on how they wield Stamina/Will.

