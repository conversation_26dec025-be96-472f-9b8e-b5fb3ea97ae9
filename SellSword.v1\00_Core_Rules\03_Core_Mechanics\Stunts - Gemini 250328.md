**Sellsword System Primer: Using Stunts**

This primer explains Stunts – a way for characters to gain minor, immediate advantages or add narrative flair when they succeed exceptionally well on an action roll. They add tactical depth and reward high performance. We're keeping the name "Stunts" as a nod to inspirations like _Alien RPG_.

**1. Earning Stunts**

- **Extra Successes:** Stunts are purchased using **extra successes**. An extra success is any '6' rolled on your dice pool that is _beyond_ the minimum number required for the action to succeed.
    - For **Simple Actions** (requiring 1 success): Every success after the first is an extra success.
    - For **Complex Actions** (requiring 2+ successes): Every success after meeting the required threshold (e.g., after the 2nd success if 2 were required) is an extra success.
- **Automatic Success:** If the Automatic Success rule triggers (pool >= 11), the first success is granted automatically. Any '6's rolled on the _remaining_ dice (after removing the Action Score dice) count as extra successes available to spend on Stunts (provided any Complexity threshold was also met by these dice).

**2. Spending Stunts**

- **Immediate Choice:** After a successful roll with extra successes, the player immediately declares how they want to spend them by choosing from the list of available Stunts.
- **Cost:** Most Stunts cost **1 extra success** each. Some particularly potent or specialized Stunts (potentially unlocked by Skills) might cost 2 extra successes (GM discretion or as defined by the Stunt/Skill).
- **Multiple Stunts:** If a player rolls multiple extra successes, they can potentially purchase multiple different Stunts from the relevant list, applying their effects simultaneously or sequentially as makes sense.

**3. Stunts by Derived Action**

The available Stunts are generally determined by the **Derived Action** used for the roll. This keeps the options thematic and focused. (Skills can sometimes unlock access to Stunts from other lists in specific situations - see Section 4).

- **PHY (Physique) Stunts (Forceful):**
    - `+1 Damage:` Deal slightly more damage on an attack.
    - `Push/Knockdown:` Shove the target back 1 hex or attempt to knock them prone (might require opposed check?).
    - `Gain Momentum:` Reduce AP cost/Difficulty of your next PHY-based action.
- **FIN (Finesse) Stunts (Agile/Redirect):**
    - `Improve Position:` Move hexes equal to AGI score (or a fraction thereof).
    - `Put Off Balance:` Target suffers a minor defensive penalty (e.g., -1/-2 dice) until their next turn.
    - `Quick Recovery:` Reduce AP cost/Difficulty of your next FIN-based action.
- **PRC (Precision) Stunts (Accurate/Disrupting):**
    - `Target Weakness:` Ignore 1 point of Armor Value for this attack.
    - `Vicious Shot/Strike:` Inflict a minor Condition (e.g., Bleeding).
    - `Steady Aim:` Gain bonus die or reduce AP cost for next Aim/Attack action.
    - `Disrupt:` Minor gear damage (Sunder) or cause target to fumble briefly.
- **FCS (Focus) Stunts (Perceptive/Concentrated):**
    - `Spot Detail:` Notice an additional minor clue, weakness, or environmental feature.
    - `Maintain Focus:` Reduce Difficulty to maintain concentration next round.
    - `Anticipate:` Gain +1 defense die against one attack before your next turn.
    - `Track Swiftly:` Gain ground or reduce time needed when tracking.
- **GRT (Grit) Stunts (Resilient/Influential Presence):**
    - `Shake It Off:` Ignore a minor penalty/condition for one round.
    - `Inspire/Demoralize:` Grant +1 die to an adjacent ally OR impose -1 die on an adjacent enemy for their next action.
    - `Recover Poise:` Regain 1 spent Will or Stamina (limit once per scene?).
    - `Stand Firm:` Increase Difficulty for enemies trying to move past/push you.
- **INS (Insight) Stunts (Understanding/Deductive):**
    - `Read Deeper:` Gain clearer insight into motive/emotional state.
    - `Uncover Lie/Inconsistency:` Identify a specific falsehood or contradiction.
    - `Recall Detail:` Remember a specific, relevant piece of Lore you possess.
    - `Subtle Influence:` Shift target's disposition slightly.
- **FRT (Fortitude) Stunts (Enduring):**
    - `Push Through:` Reduce duration/severity of a negative physical effect (poison, stun).
    - `Ignore Pain:` Negate penalties from a minor wound for one action/round.
    - `Second Wind:` Regain 1 spent Stamina (limit once per scene?).
    - `Steady Body:` Reduce chance of being knocked prone from impact.

**4. Skill Interaction with Stunts**

Skills (particularly Tier 2 Styles and Tier 3 Focuses) interact with Stunts in several ways, enhancing skilled characters:

- **Making Stunts Cheaper:** A skill might allow activating a specific Stunt without spending an extra success (perhaps by spending Stamina/Will instead, or just once per round for free).
- **Improving Stunts:** A skill might enhance the effect of a specific Stunt (e.g., `Improve Position` allows more movement, `+1 Damage` becomes `+2 Damage`).
- **Unlocking New Stunts:** A skill might add entirely new, thematic Stunts to the list available when performing certain actions (e.g., `Bodyguard` adds defensive Stunts when protecting allies, `Grappler` adds control Stunts to PHY checks).
- **Allowing Cross-Action Stunts:** A skill might allow a character to use a Stunt normally associated with a different Derived Action if the situation makes sense thematically (e.g., `Bodyguard` using `Put Off Balance` (FIN) on a successful `GRT` block).

**5. Stunts vs. Complexity**

It's crucial to remember the difference:

- **Complex Actions:** Require multiple successes (2+) _just to achieve the basic intended intricate effect_. The reward is baked into pulling off the difficult maneuver itself. This is declared _before_ rolling.
- **Stunts:** Are _additional minor benefits_ purchased with successes rolled _beyond_ the minimum required (whether that minimum was 1 or more). They enhance an already successful action opportunistically.

Successfully completing a Complex Action is generally more impactful than adding a Stunt to a simple action.

---

This primer outlines how Stunts provide dynamic, moment-to-moment choices based on exceeding the requirements of a roll, further enhanced by character skills.