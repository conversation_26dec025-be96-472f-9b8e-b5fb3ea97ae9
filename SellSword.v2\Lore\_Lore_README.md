# Lore & Wyrds

This folder contains entries for specific knowledge domains characters can possess, represented by **Lore**. This includes both mundane knowledge (History, Geography, Creatures) and magical knowledge (**Wyrd Lore**).

## System Overview

*   **Function:** Lore represents specialized knowledge ("KNOW") that characters acquire through study, experience, or discovery. It primarily functions during Downtime/Preparation phases but can also provide situational benefits during active play (e.g., recalling a monster's weakness, knowing a noble's lineage).
*   **Lore Slots:** Characters have a limited number of `Knowledge/Lore Slots` based on their [[../Attributes/Intellect|Intellect]] (`3 + INT * 2`). Each Lore entry occupies a certain number of these slots based on its depth or rarity.
*   **Wyrd Lore:** A specific category of Lore representing deep understanding and affinity for one of the 15 fundamental domains of magic (Wyrds). See [[SellSword_v2_Overview|Magic System Overview]].
    *   **Interaction:** Wyrd Lore **augments** the four Magic Category Skills ([[../Features/_Features_README|Evoke, Weave, <PERSON>ri<PERSON>, <PERSON><PERSON>]]) when performing actions related to that Wyrd's domain.
    *   **Benefits:** Typically adds bonus dice (+1 to +3 based on Slot Cost) to the magic skill roll, can negate Difficulty dice, or potentially reduce Complexity for related magical effects.

## Wyrd Domains (as Lore Categories)

*   **Physical:** [[Eldr Lore|Eldr (Fire)]], [[Vatn Lore|Vatn (Water)]], [[Jord Lore|Jörð (Earth)]], [[Loftr Lore|Loftr (Air)]], [[Ljos Lore|Ljós (Light)]]
*   **Mental:** [[Hugr Lore|Hugr (Emotion)]], [[Rok Lore|Rök (Logic)]], [[Draumr Lore|Draumr (Dream/Illusion)]], [[Visa Lore|Vísa (Foresight/Truth)]], [[Ekko Lore|Ekkó (Echoes/Impressions)]]
*   **Spiritual:** [[Lifi Lore|Lífi (Life)]], [[Daudi Lore|Dauði (Death)]], [[Ond Lore|Önd (Soul/Spirits)]], [[Skuggi Lore|Skuggi (Shadow)]], [[Aether Lore|Æther (Raw Magic)]]

## File Structure

*   Each distinct Lore entry (including each Wyrd Lore) should have its own Markdown file within this directory (e.g., `Eldr_Lore.md`, `History_of_the_Empire.md`).
*   Files use the `_template_lore.md` structure, detailing:
    *   `name`: The name of the Lore entry.
    *   `type`: Lore.
    *   `category`: The type of knowledge (Wyrd, History, Geography, etc.).
    *   `slot_cost`: The number of Lore Slots this entry occupies (1-3).
    *   `description`: A brief summary of the knowledge domain.
    *   `benefits`: Specific mechanical advantages granted (e.g., bonus dice, information access).
    *   `tags`: Relevant keywords.

Refer to individual Lore files for specific details and benefits.

### Methodology for Creating Wyrd Lore Entries

When creating or updating Wyrd Lore entries, follow these guidelines for consistency:

1.  **`slot_cost`:** Assign a cost of 1, 2, or 3 based on the Wyrd's perceived power, complexity, utility, or rarity. Higher cost generally implies greater depth or impact.
2.  **`description`:** Provide a concise summary capturing the essence of the Wyrd's domain.
3.  **`benefits`:**
    *   Clearly state the bonus dice granted (+1, +2, or +3, typically matching the `slot_cost`).
    *   Specify which Magic Skill(s) (Evoke, Weave, Scribe, Scry) the bonus applies to and the specific circumstances (e.g., "+2 Dice to **Scry** checks involving sensing past events..."). Be precise.
    *   Optionally, mention 1-2 thematic example actions this Lore might enable (e.g., "May allow 'Read Echoes'"), noting GM discretion is required for feasibility and mechanics.
4.  **Body Text:**
    *   Start with a brief, evocative summary sentence in parentheses.
    *   Include a paragraph describing the Wyrd's domain, focus, and perhaps associated risks or philosophies.
    *   Clearly reiterate the mechanical benefits listed in the frontmatter.
    *   *(Recommended):* Add a section listing the associated Magic Skills (Evoke, Weave, Scribe, Scry) with brief examples of how each skill might be used within this Wyrd's theme (see [[Eldr Lore]] for an example format).
