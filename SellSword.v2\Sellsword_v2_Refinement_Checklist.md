# Sellsword v2 Refinement Checklist

This checklist outlines potential areas for refinement and further elaboration in the Sellsword v2 TTRPG system, based on a review of the core mechanics and documentation.

## Wound System Details

* [ ] Define the specific mechanical effects for each tier (Minor, Major, Grievous, Kill/Debilitate) of *all* wound types (Laceration, Muscle Trauma, Blunt Trauma, Bone Trauma, Internal Trauma, Burn Trauma, Piercing Trauma, Ligament Trauma, Joint Strain/Dislocation, Nerve Trauma, Tendon Trauma, Head Trauma, Face Trauma, Eye Trauma, Neck Trauma, Organ Trauma, Spinal Trauma). This involves detailing penalties, bleeding, recovery times, etc., within the individual wound files (e.g., `Wounds/Body/Laceration.md`).
* [ ] Define the specific effects of the common conditions and status effects listed in `_Wound_Statuses.md`.

## Magic System Details

* [ ] Define the specific effects on the Magic Mishap Table for different severity levels.
* [ ] Finalize the mechanic for spending Will to mitigate Mishap severity (cost and effect).

## Combat Mechanics Refinements

* [ ] Clarify the resistance Difficulty for the "Apply Force" stunt (FRT roll vs. Weapon Force). Consider if adjustments are needed or if the current difficulty is an intended design choice.
* [ ] Define how Sunder condition levels escalate (e.g., how many "Apply Force (Sunder)" applications cause escalation, or if critical hits/hazards cause higher levels directly).
* [ ] Define how spending multiple successes on the "Apply Force" stunt works (enhanced effect, multiple saves, multiple effects).
* [ ] Define a clear, consistent outcome for the "Apply Force (Sunder)" stunt when used against Monsters/NPCs who do not track Sunder levels.

## Action Economy & Costs

* [ ] Define typical or specific Action Point (AP) costs for common uses of Skills, Features, and specific Magical effects.

## Character & Equipment Details

* [ ] Clearly state the minimum Tier 1 level and whether a Tier 2 skill is a mandatory prerequisite for purchasing Tier 3 skills.
* [ ] Define the relationship between exceeding Carry capacity and gaining levels of the Encumbrance condition.
