This primer explains how weapons, shields, and armor function mechanically in Sellsword, based on their characteristics and ratings (typically 1-3). These ratings interact with core mechanics like Derived Actions, Skills, Difficulty, Complexity, and Stunts.

_(Note: Some mechanics like Force, Durability details, Carry-to-AP cost, and Range Penalties are still conceptual and marked as TBD - To Be Determined)._

## 1. Weapon Characteristics & Mechanics

Weapons are defined by several ratings that influence their use in combat:

- **`Offense`** (Rating 1-3): Determines the weapon's baseline accuracy and ease of use in an attack.
    
    - **Mechanic:** Adds +X dice (equal to the `Offense` rating) to your dice pool when making an attack roll (using `PHY`, `FIN`, or `PRC` as appropriate). _(Note: This column needs to be added/derived for the gear list)._
        
- **`Damage`** (Rating 1-3): Represents the weapon's base potential to inflict harm on a successful hit.
    
    - **Mechanic:** If an attack roll succeeds (meets the required success threshold), this rating is the **base damage** value dealt _before_ Damage Reduction from armor is applied. This value is fixed per weapon, not rolled.
        
- **`Pierce`** (Rating 1-3): Represents the weapon's ability to penetrate or bypass armor.
    
    - **Mechanic:** Likely interacts with Armor Value (AV). _`(TBD: Does Pierce 2 negate 2 points of AV? Does it work differently? Needs final definition).`_
        
- **`Force`** (Rating 1-3, Conceptual): Represents the weapon's momentum, impact, or raw power beyond basic damage (e.g., a heavy axe vs. a light one).
    
    - **Mechanic Idea (TBD):** Potentially allows spending Stamina (up to the `Force` rating) on a successful hit to increase the final damage dealt or activate specific forceful Stunts (like improved Push/Knockdown/Sunder). Needs refinement.
        
- **`Block`** (Rating 1-3): The weapon's suitability for actively blocking incoming attacks.
    
    - **Mechanic:** Adds +X dice to your pool when using a defensive action to Block.
        
- **`Parry`** (Rating 1-3): The weapon's suitability for actively parrying/deflecting incoming attacks, often implying a potential counter-attack.
    
    - **Mechanic:** Adds +X dice to your pool when using a defensive action to Parry.
        
- **`Hook`** (Rating 1-3): The weapon's capability for hooking maneuvers (common on polearms, axes).
    
    - **Mechanic:** Adds +X dice to your pool when attempting specific maneuvers like Tripping, Disarming, or Pulling an opponent using the weapon.
        
- **`Reach`** (Rating 0+): The effective melee distance of the weapon.
    
    - **Mechanic:** This value is added to the character's base reach (Standard Humanoid = 1 hex, Large = 2 hexes, Small = 0 hexes) to determine their total melee threat range.
        
- **`Range`** (Rating Multiplier): Determines the optimal range for projectile weapons.
    
    - **Mechanic:** Optimal range (no penalty) = `Range Rating * PRC Score` (in hexes). Attacks beyond this range incur penalties. _(TBD: Define specific range penalty increments/Difficulty increases)._ _(Note: Data below shows max range, needs conversion to Range Rating)._
        
- **`STR`** (Attribute Requirement - Optional):
    
    - **Mechanic:** Minimum `STR` attribute potentially required to wield effectively without penalty. _(TBD: Confirm if this requirement will be kept)._
        
- **Weapon Stunts:** Some weapons may list specific Stunt options (like "Upgrade wound severity" or "Disarm opponent").
    
    - **Mechanic:** These are added to the character's available Stunt list _when using that weapon_. They still cost extra successes to activate as normal.
        

## 2. Shield Characteristics & Mechanics

Shields provide defensive benefits through several ratings:

- **`Block`** / **`Parry`** (Rating 1-3): As weapons, these add +X dice when actively using the shield to Block or Parry.
    
- **`Cover`** (Rating 1-?): Represents the passive protection offered by the shield's size/shape.
    
    - **Mechanic:** Adds +X **Difficulty dice** to the pool of attackers targeting the wielder, representing the physical obstruction. This is passive and doesn't require an action. _(Note: Values below seem high for Difficulty dice, may need conversion/rethinking)._
        

## 3. Armor Characteristics & Mechanics

Armor protects by reducing incoming damage.

- **`Body`** / **`Voids`** (AV Rating 1-3): Represents the Armor Value (protection level) for different parts of the body (center mass vs. gaps/joints).
    
    - **Mechanic:** Provides direct **Damage Reduction (DR)**. When a hit lands on an armored location, subtract the location's AV from the incoming base `Damage` rating. (e.g., `Damage 2` hit vs `Body AV 1` = 1 final damage; `Damage 2` hit vs `Body AV 3` = 0 final damage). _(Note: Values below need conversion to the 1-3 scale)._
        
- **Important Note:** Armor **does not** make a character harder to hit (it doesn't add Difficulty to attackers). Its protection comes purely from Damage Reduction.
    
- **Drawbacks:** Heavier armor typically has a higher `Carry` rating, impacting movement and Action Point costs (see below). It may also contribute to fatigue or impose penalties on certain actions (like Stealth or Athletics) - _(TBD: Define specific fatigue/penalty rules)_.
    

## 4. Other Gear Mechanics

- **`Carry`** (Rating 0-3+): Represents an item's combined bulk, weight, and unwieldiness.
    
    - **Mechanic 1 (Encumbrance):** The sum of `Carry` values for all worn/carried gear counts against the character's `Carry` capacity (`3 + STR`). Exceeding capacity likely causes penalties. _(Note: Values below need conversion to the 0-3+ scale)._
        
    - **Mechanic 2 (Action Cost - TBD):** The `Carry` rating (likely ranked 1-3 for weapons/shields based on the converted value) influences the base Action Point (AP) cost required to use the item, especially for attacks. (e.g., Carry 1 weapon = 1 AP attack? Carry 2 = 2 AP? Carry 3 = 3 AP? Needs definition). Carry 0 items are considered 'weightless' for encumbrance and likely don't add to action costs.
        
- **`Durability`** (Rating 1-3+, Conceptual): Represents an item's resilience to damage or failure.
    
    - **Mechanic Idea (TBD):** When rolling Gear Dice (e.g., `Offense`, `Block`, `Parry` dice), rolling a number of '1's equal to or exceeding the item's `Durability` rating might cause the item to become 'Worn' (minor penalty) or 'Broken'. Needs refinement, especially defining the 'Worn' state and exactly which dice trigger checks.
        
- **`Ammunition`**: Projectile weapons (`Ranged Combat`) use ammunition.
    
    - **Mechanic:** The weapon (Bow, Crossbow) typically provides the `Offense` rating (+Dice to hit) and potentially `Force`. The ammunition (Arrow, Bolt) provides the `Damage` and `Pierce` ratings for that specific shot.
        

## Gear Charts
**Light Melee Weapons**

| **Weapon**     | **Carry*** | **STR*** | **Durability*** | **Reach** | **Offense*** | **Damage*** | **Pierce*** | **Force*** | **Block*** | **Parry*** | **Hook*** | **Notes/Stunts**                                 |
| -------------- | ---------- | -------- | --------------- | --------- | ------------ | ----------- | ----------- | ---------- | ---------- | ---------- | --------- | ------------------------------------------------ |
| Stiletto       | 0          | 0        | 2               | 0         | 1            | 2           | 2           | 0          | 0          | 1          | 0         | Attack Stunt: Upgrade wound severity one level.  |
| Quillon Dagger | 0          | 0        | 2               | 0         | 1            | 2           | 1           | 0          | 1          | 1          | 0         | Parry/Block Stunt: Disarm opponent (resistable). |
| Seax           | 0          | 0        | 2               | 0         | 2            | 2           | 1           | 1          | 1          | 0          | 0         | Single edge, chopping capability.                |
| Parry Dagger   | 0          | 0        | 2               | 0         | 1            | 1           | 1           | 0          | 1          | 3          | 1         | Parry Stunt: Trapping                            |
| Rondel Dagger  | 0          | 0        | 2               | 0         | 1            | 1           | 2           | 1          | 1          | 1          | 0         | Attack Stunt: Remove 1 level of armor            |
| Kris           | 0          | 0        | 1               | 0         | 2            | 3           | 1           | 0          | 0          | 1          | 0         | Wavy blade.                                      |
| Punch Dagger   | 0          | 0        | 2               | 0         | 1            | 2           | 2           | 1          | 0          | 1          | 0         | Attack Stunt: Remove 1 level of armor.           |
| Hand Axe       | 0          | 0        | 2               | 0         | 2            | 2           | 0           | 1          | 1          | 0          | 1         | Can be thrown (Range 6 in data).                 |
| Star Mace      | 0          | 0        | 2               | 0         | 2            | 1           | 1           | 2          | 1          | 1          | 0         | Flanged head.                                    |

**One-Handed Melee Weapons (Bladed & Hafted)**

| **Weapon**         | **Carry*** | **STR*** | **Durability*** | **Reach** | **Offense*** | **Damage*** | **Pierce*** | **Force*** | **Block*** | **Parry*** | **Hook*** | **Notes/Stunts**   |
| ------------------ | ---------- | -------- | --------------- | --------- | ------------ | ----------- | ----------- | ---------- | ---------- | ---------- | --------- | ------------------ |
| Spear              | 2          | 1        | 1               | 2         | 2            | 2           | 1           | 1          | 1          | 1          | 0         | Thrown (Range 6).  |
| Staff              | 2          | 1        | 1               | 2         | 1            | 1           | 0           | 2          | 2          | 1          | 1         |                    |
| Bearded Axe        | 2          | 1        | 2               | 1         | 2            | 3           | 0           | 2          | 1          | 1          | 2         |                    |
| Warhammer          | 2          | 1        | 3               | 1         | 2            | 2           | 1           | 3          | 1          | 0          | 1         | Hammer & Beak.     |
| Steel Flanged Mace | 2          | 1        | 2               | 1         | 2            | 2           | 2           | 2          | 1          | 1          | 1         |                    |
| Falchion - Cleaver | 1          | 1        | 2               | 1         | 2            | 2           | 1           | 2          | 1          | 1          | 0         | Single edge.       |
| Gladius            | 1          | 1        | 2               | 1         | 1            | 2           | 1           | 1          | 2          | 1          | 0         | Broad blade.       |
| Arming Sword       | 1          | 1        | 2               | 1         | 1            | 2           | 2           | 1          | 1          | 2          | 0         | Short Sword.       |
| Rapier             | 1          | 1        | 1               | 2         | 2            | 2           | 2           | 0          | 1          | 2          | 0         | Long reach for 1H. |
| Sabre              | 2          | 1        | 2               | 1         | 3            | 3           | 0           | 2          | 1          | 1          | 0         | Curved blade.      |
| Longsword          | 2          | 1        | 2               | 2         | 2            | 2           | 1           | 2          | 2          | 2          | 0         | Versatile.         |
| Falchion - Clip Pt | 2          | 1        | 2               | 2         | 2            | 3           | 2           | 1          | 1          | 1          | 0         | Single edge.       |

**Two-Handed Melee Weapons**

| **Weapon**    | **Carry*** | **STR*** | **Durability*** | **Reach** | **Offense*** | **Damage*** | **Pierce*** | **Force*** | **Block*** | **Parry*** | **Hook*** | **Notes/Stunts**   |
| ------------- | ---------- | -------- | --------------- | --------- | ------------ | ----------- | ----------- | ---------- | ---------- | ---------- | --------- | ------------------ |
| Dane Axe      | 2          | 2        | 2               | 2         | 3            | 2           | 1           | 2          | 1          | 1          | 3         |                    |
| Bec de Corbin | 2          | 2        | 2               | 2         | 3            | 2           | 2           | 2          | 2          | 0          | 2         | Beak/Spike.        |
| Bardiche      | 2          | 2        | 2               | 2         | 3            | 3           | 1           | 2          | 2          | 1          | 1         | Crescent axe.      |
| Poleaxe       | 2          | 2        | 2               | 2         | 2            | 3           | 2           | 3          | 1          | 1          | 1         | Axe/Hammer/Spike.  |
| Glaive        | 2          | 2        | 2               | 2         | 3            | 3           | 1           | 1          | 2          | 3          | 0         | Long blade.        |
| Maul          | 3          | 4        | 3               | 2         | 2            | 1           | 2           | 3          | 1          | 0          | 0         | Heavy hammer/Stave |
| Bill          | 3          | 2        | 2               | 3         | 2            | 2           | 3           | 2          | 0          | 1          | 3         | Long spike/hook.   |
| Halberd       | 3          | 2        | 2               | 3         | 2            | 3           | 2           | 3          | 0          | 2          | 1         | Axe/Spike.         |
| Partisan      | 3          | 2        | 2               | 3         | 2            | 3           | 2           | 3          | 1          | 1          | 0         | Winged spear.      |
| Winged Spear  | 3          | 2        | 2               | 3         | 2            | 2           | 3           | 3          | 0          | 1          | 1         | Lugs for control.  |
| Montante      | 3          | 2        | 2               | 3         | 3            | 3           | 2           | 1          | 2          | 2          | 0         | Greatsword type.   |
| Great Sword   | 3          | 2        | 2               | 3         | 2            | 3           | 1           | 2          | 3          | 2          | 0         | Large blade.       |

**Ranged Weapons**

| **Weapon**         | **Carry*** | **STR*** | **Durability*** | **Range*** | **Offense*** | **Damage*** | **Pierce*** | **Force*** | **Notes/Stunts**  |
| ------------------ | ---------- | -------- | --------------- | ---------- | ------------ | ----------- | ----------- | ---------- | ----------------- |
| Recurve Bow Light  | 2          | 1        | 1               | 6          | 1            | (Ammo)      | (Ammo)      | 1          |                   |
| Recurve Bow Medium | 2          | 2        | 1               | 8          | 2            | (Ammo)      | (Ammo)      | 2          |                   |
| Recurve Bow Heavy  | 2          | 3        | 1               | 10         | 3            | (Ammo)      | (Ammo)      | 3          |                   |
| Long Bow Light     | 3          | 0        | 2               | 8          | 1            | (Ammo)      | (Ammo)      | 1          |                   |
| Long Bow Medium    | 3          | 1        | 2               | 12         | 2            | (Ammo)      | (Ammo)      | 2          |                   |
| Long Bow Heavy     | 3          | 2        | 2               | 16         | 3            | (Ammo)      | (Ammo)      | 3          |                   |
| Sling              | 0          | 0        | 1               | 8          | 2            | (Ammo)      | (Ammo)      | 2          | Uses stones?      |
| Javelin            | 1          | 0        | 1               | 8          | 2            | 2           | 2           | 1          | Primarily thrown. |
| Throwing Knife     | 0          | 0        | 1               | 4          | 2            | 1           | 1           | 0          |                   |

_(Note: Ammunition stats like Damage/Pierce modify the base projectile weapon stats)._

**Shields**

| **Shield**     | **Carry*** | **STR*** | **Durability*** | **Reach** | Offense | Damage | **Cover*** | **Block*** | **Parry*** | **Notes/Stunts** |
| -------------- | ---------- | -------- | --------------- | --------- | ------- | ------ | ---------- | ---------- | ---------- | ---------------- |
| Steel Buckler  | 1          | 0        | 3               | 0         | 1       | 1      | 0          | 1          | 2          | Boss grip.       |
| Targe          | 1          | 0        | 2               | 0         | 1       | 1      | 0          | 2          | 1          |                  |
| Wooden Rotella | 2          | 1        | 1               | 0         | 1       | 1      | 1          | 2          | 1          | Round wood.      |
| Heater Shield  | 2          | 1        | 2               | 0         | 1       | 1      | 1          | 2          | 1          | Pointed wood.    |
| Hand Pavise    | 1          | 1        | 2               | 0         | 2       | 2      | 1          | 1          | 2          | Rectangular.     |
| Steel Rotella  | 2          | 2        | 3               | 0         | 1       | 1      | 1          | 2          | 1          | Round steel.     |
| Wooden Round   | 2          | 2        | 2               | 1         | 1       | 1      | 2          | 3          | 1          | Large wood.      |
| Steel Round    | 3          | 3        | 3               | 1         | 1       | 1      | 2          | 3          | 1          | Large steel.     |
| Wooden Kite    | 3          | 2        | 2               | 1         | 0       | 1      | 3          | 3          | 0          |                  |
| Steel Kite     | 3          | 3        | 3               | 1         | 0       | 1      | 3          | 3          | 0          |                  |
| Scutom         | 4          | 3        | 2               | 1         | 0       | 1      | 4          | 3          | 0          | Large rect.      |
| Pavise         | 4          | 3        | 2               | 1         | 0       | 1      | 4          | 3          | 0          | Deployable?      |

**Armor**

| **Armor**          | **Carry*** | **STR*** | **Durability*** | **Body AV*** | **Voids AV*** | Enc | Fatigue | **Notes**                            |
| ------------------ | ---------- | -------- | --------------- | ------------ | ------------- | --- | ------- | ------------------------------------ |
| Gambeson           | 0          | 0        | 1               | 1            | 1             | 0   | 0       | Basic padding.                       |
| Leather Lamellar   | 1          | 0        | 1               | 2            | 1             | 0   | 0       | Leather plates.                      |
| Chain Mail Shirt   | 2          | 1        | 2               | 2            | 2             | 1   | 0       | Metal rings.                         |
| Brigandine         | 3          | 1        | 3               | 3            | 1             | 0   | 1       | Plates riveted inside cloth/leather. |
| Field Plate        | 4          | 2        | 4               | 3            | 2             | 2   | 2       | Articulated plate for battle.        |
| Full Plate Harness | 5          | 3        | 5               | 3            | 3             | 3   | 3       | Full coverage plate.                 |
