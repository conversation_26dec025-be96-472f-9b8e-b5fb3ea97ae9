# SellSword 3d6 Roll-Under System Proposal

## Introduction

This document proposes a conversion of SellSword from its current d6 dice pool system to a 3d6 roll-under system. The goals of this conversion are to:

1. Return to the 3d6 roll-under mechanics that were part of the original design
2. Maintain the game's core philosophy and feeling
3. Preserve the stamina and will wager mechanics that make the system distinctive
4. Take advantage of the statistical curve of 3d6 for more granular character capability representation
5. Keep the focus on equipment importance and the gritty, tactical nature of the game

## Core Mechanic: 3d6 Roll-Under

### Basic Resolution

The fundamental task resolution system works as follows:

- Roll 3d6 and compare against your **Target Number (TN)**
- **Success**: Roll equal to or under your TN
- **Critical Success**: Roll a natural 3 or 4 OR roll 10+ under your TN
- **Critical Failure**: Roll a natural 18 OR roll 10+ over your TN

### Target Number Calculation

TN = Prime Attribute 1 + Prime Attribute 2 + Skill Value + Equipment Bonus - Difficulty

- **Base TN**: Derived from relevant Prime Attributes (same pairs as current Derived Actions)
- **Skill Value**: Adds directly to TN (+1/+2/+3 for skill levels 1/2/3)
- **Equipment Bonus**: Gear adds directly to TN
- **Difficulty**: Subtracts from TN, representing challenge

### Degree of Success

- **Margin of Success**: How much you succeed by (TN minus roll)
<!-- - **Extra Success Points (ESP)**: Generated when your margin of success is 4+
  - ESP = floor((TN - Roll)/4)
  - Example: TN 14, roll 6 = margin of 8 = 2 ESP (8÷4=2)
  - ESP can be spent on Stunts just like extra successes in the pool system -->

### Probability Considerations

The 3d6 probability curve provides:

- Bell curve centered around 10-11
- Very low chance of extreme results (3 or 18)
- Natural scaling that rewards character investment
- Meaningful differentiation between skill levels

| Target Number | Chance of Success (3d6 ≤ TN) |
|---------------|------------------------------|
| 3             | 0.5%                         |
| 4             | 1.9%                         |
| 5             | 4.6%                         |
| 6             | 9.3%                         |
| 7             | 16.2%                        |
| 8             | 25.9%                        |
| 9             | 37.5%                        |
| 10            | 50.0%                        |
| 11            | 62.5%                        |
| 12            | 74.1%                        |
| 13            | 83.8%                        |
| 14            | 90.7%                        |
| 15            | 95.4%                        |
| 16            | 98.1%                        |
| 17            | 99.5%                        |
| 18            | 100%                         |

## Character Attributes & Skills

### Prime Attributes

Maintain the current 8 Prime Attributes but with an expanded range:

- Standard human range: -3 to +3
- Supernatural range: +4 and above
- Average human: 0

The Prime Attributes are:

- **STR (Strength)**: Physical Power - Ability to exert force, break, lift, carry
- **AGI (Agility)**: Physical Speed - Quickness of movement
- **DEX (Dexterity)**: Physical Control - Coordination of movement
- **AWA (Awareness)**: Mental Speed - Perception, noticing details, situational alertness. Street Smarts
- **INT (Intellect)**: Mental Power - Deep thought and Reasoning
- **PRE (Presence)**: Mental Control - Social influence and force of personality

### Derived Actions

The 7 Derived Actions are now the basis for Target Numbers rather than dice pools:

1. **FRT (Fortitude)**: `8 + PRE + STR` - Resilience and endurance; resisting physical harm and fatigue
2. **PHY (Physique)**: `8 + STR + AGI` - Raw physical power and movement; lifting, pushing, jumping, climbing
3. **FIN (Finesse)**: `8 + AGI + DEX` - Graceful movement and coordination; acrobatics, stealth, melee finesse
4. **PRC (Precision)**: `8 + DEX + AWA` - Fine motor control and accuracy; ranged attacks, lockpicking, detailed work
5. **FCS (Focus)**: `8 + AWA + INT` - Mental alertness and concentration; perception, tracking, maintaining spells
6. **INS (Insight)**: `8 + INT + PRE` - Mental deduction and understanding; reasoning, reading people, solving puzzles

*Note: The baseline is 8 (instead of 2) because 3d6 has an average roll of 10-11, making these TNs appropriate for average difficulty tasks.*

### Skills

Skills add their rating directly to your Target Number:

| Skill Level | TN Bonus | SP Cost |
|-------------|----------|---------|
| Level 1     | +1       | 1 SP    |
| Level 2     | +2       | +2 (3 total) |
| Level 3     | +3       | +3 (6 total) |

#### Example

A highly specialized (master) character with Att1 +3, Att2 +2, and skill Level 2 would have:

- Target Number: 8 + 3 + 2 + 2 = 15
- Chance of success: 95.4% (rolling 3d6 ≤ 15)

A somewhat specialized (journeyman) character with Att1 +2, Att2 +1, and skill Level 1 would have:

- Target Number: 8 + 1 + 2 + 2 = 12
- Chance of success: 74.1% (rolling 3d6 ≤ 12)

A competent (apprentice) character with Att1 +1, Att2 +1, and no skill would have:

- Target Number: 8 + 1 + 1 + 0 = 10
- Chance of success: 50.0% (rolling 3d6 ≤ 10)


A novice character with Att1 +1, Att2 +0, and no skill would have:

- Target Number: 8 + 1 + 0 + 0 = 9
- Chance of success: 37.5% (rolling 3d6 ≤ 9)

### Derived Stats

These calculated resources remain largely unchanged:

- **Speed:** `3 + AGI` (Hexes moved per Action Point)
- **Carry:** `3 + STR` (Item 'slots' carried without penalty)
- **Stamina:** `3 + STR + AGI` (Physical resource pool)
- **Will:** `3 + INT + PRE` (Mental resource pool)
- **Standing:** `3 + PRE` (Social capital/connections)
- **Knowledge/Lore Slots:** `3 + INT` (Capacity for holding Lore entries)

## Wager Mechanic

The stamina and will wager system is adapted to work with roll-under mechanics:

### Pre-Roll Wagering

Players can spend Stamina or Will before rolling to improve their chances:

- Spend 1+ Stamina (for FRT/PHY) or Will (for FRT/FCS) before rolling
- Each point spent grants +2 to your Target Number for that roll
- **Risk**: If the roll fails by 5 or more, you lose additional resources:
  - Extra resource loss: 1 + current Fatigue (if wagering Stamina) or Strain (if wagering Will)
  
**Example**: A character with FRT 12 and Fatigue 2 wagers 2 Stamina for a TN of 16. They roll a 22, failing by 6. They lose the initial 2 Stamina, plus 3 additional Stamina (1 + Fatigue 2), for a total of 5 Stamina lost.

### Post-Roll Wagering (Desperate Push)

After an unsuccessful roll, you may attempt a desperate recovery:

- Spend 2 Stamina/Will to roll an additional d6
- Replace the highest die from your original roll with this new die
- **Risk**: If the new die shows a 6, you lose additional resources as described above

**Example**: A character rolls 16 against a TN of 14. They spend 2 Stamina for a desperate push, rolling a 3. They replace the highest die in their original roll (let's say a 6) with the 3, resulting in a new total of 13, which succeeds.

### Resource Management

- **Stamina Points**: Modifys physical exertion (FRT/PHY actions)
- **Will Points**: Modifys mental/magical exertion (FRT/FCS actions)
- **Action Points**: Modifys precise and controlled actions (FIN/PRC/INS)
- **Fatigue**: Each level reduces TN for FRT and PHY actions by 1
- **Strain**: Each level reduces TN for FCS and INS actions by 1
- **Encumbrance**: Each level reduces TN for FIN and PRC actions by 1

## Difficulty

Difficulty represents adverse circumstances that make an action harder to perform, giving the player a TN penalty.

| Difficulty Level | TN Penalty | Example Circumstances |
|------------------|-----|----------------------|
| Moderate         | -2  | Poor light, slight distraction |
| Major            | -4  | Darkness, unstable footing |
| Extreme          | -6  | Pitch black, heavy rain |

### Automatic Success Threshold

TBD

## Equipment

### Impact on Rolls

Equipment affects the 3d6 roll-under system in several ways:

- **Weapons**: Add their rating directly to your TN, attack, block, parry, etc.
- **Armor**: Damage Reduction (DR) works as in the current system
- **Tools**: Provide TN bonuses for specific tasks (1-3)

### Weapons

| Weapon Stats   | Range    | Description |
|----------------|----------|-------------|
| Offense        | 0-3      | TN Bonus when attacking|
| Damage         | 0-3      | Base damage dealt on hit |
| Pierce         | 0-3      | Ability to bypass armor |
| Block          | 0-3      | TN Bonus when blocking |
| Parry          | 0-3      | TN Bonus when parrying |
| Hook           | 0-3      | TN Bonus when hooking |
| Reach          | 0-3      | Additional melee distance |
| Range          | 0-6      | PRC Range Multiplier |
| Carry          | 0-3      | Item slots taken and AP cost |
| Durability     | 0-5      | Item health |

### Armor System

| Armor Stats    | Range    | Description |
|----------------|----------|-------------|
| DR (Body)      | 0-3      | Damage Reduction for Body hits |
| DR (Voids)     | 0-3      | Damage Reduction for Voids hits |
| Encumbrance    | 0-3      | Penalty to FIN, PRC TNs and Speed |
| Fatigue        | 0-3      | Penalty to FRT, PHY TNs |
| Carry          | 0-3      | Item slots taken |
| Durability     | 0-5      | Item health |

### Durability

Equipment quality affects critical failure range:

- **Standard**: Natural 18 is a critical failure
- **Poor Quality**: Natural 16-18 is a critical failure
- **Superior Quality**: Only natural 18 is a critical failure, and can be rerolled once

## Combat System

### Action Economy

The Action Point (AP) system remains largely unchanged:

- Characters have 4 AP per round
- Action costs remain the same (Move: 1 AP, Attack: weapon's Carry value, etc.)
- Extra AP on FIN/PRC/INS actions provides +4 to TN per AP spent (max +8)

### Attack & Defense

#### Making Attacks

1. Determine the appropriate Derived Action (PHY, FIN, PRC)
2. Calculate attack TN: `Derived Action + Skill + Weapon Offense - Target's Defense Value`
3. Roll 3d6 against your attack TN
4. On success, deal weapon damage reduced by target's DR
5. ESP can be spent on Stunts

#### Hit Locations

- **Body**: Target normally (no TN penalty)
- **Voids**: -6 TN penalty to target
- **Vitals**: -10 TN penalty and Complexity 1 to target

#### Defending

Two options for defense:

1. **Passive Defense**: Imposes penalty to attacker's TN
   - **Target Defense (TD)**: Calculated as `floor(Speed / 2) + Size_Modifier`
   - This value is subtracted from the attacker's TN

2. **Active Defense**: Opposed roll
   - Defender rolls against their defense TN: `Derived Action + Skill + Shield/Weapon Defense`
   - If defender succeeds and attacker fails, or defender gets equal/greater ESP than attacker, attack is negated
   - Defender's extra ESP beyond what's needed can be spent on defensive Stunts

### Damage & Wounds

The wound system remains largely intact:

1. Calculate penetrating damage (Attack Damage - Target's DR)
2. Roll wound dice equal to penetrating damage
3. Sum the result and consult the appropriate wound table
4. Apply specific wound effects

#### Critical Hits

When you roll a critical success on an attack:

- Deal maximum damage, OR
- Target a specific location without penalty, OR
- Apply a minor condition (bleeding, etc.)

## Stunts

Stunts now use Extra Success Points (ESP) instead of extra successes:

- Available Stunts tied to the Derived Action used
- Most Stunts cost 1 ESP
- Effects remain unchanged from current system

### Conditional Stunt Eligibility

When purchasing Stunts for an attack, compare the attack's total **Pierce** to the target's **DR**:

- If **Pierce < DR**: The **+1 Damage** stunt cannot be purchased. ESP may be spent on **Apply Force** (purchasable multiple times).
- If **Pierce ≥ DR**: The **+1 Damage** stunt may be purchased normally. **Apply Force** is unavailable.

## Magic System

Magic adapts to the roll-under system while maintaining the core concepts:

### Casting Process

1. **Evoke (RES)**: Roll 3d6 against your RES TN to gather Aether
   - Success margin determines Wyrd Dice generated
   - 1 Wyrd Die per 4 points of success margin (1 ESP = 1 Wyrd Die)

2. **Weave/Scribe/Scry**: Roll against respective TNs to shape or use magic
   - Wyrd Dice add to effect magnitude (typically 1d6 per Wyrd Die)
   - AP costs remain unchanged (1 AP per Wyrd Die)

### Mishaps

Magic mishaps occur on critical failures when casting:

- If you roll a critical failure on a magic roll with wagered Will, a mishap occurs
- Mishap severity determined by how much the roll failed by:
  - Minor Aetheric Wound: Failed by 1-4
  - Major Aetheric Wound: Failed by 5-9
  - Grievous Aetheric Wound: Failed by 10-14
  - Critical Aetheric Wound: Failed by 15+

## Probability Analysis

### 3d6 vs. d6 Pool

| Character Capability | 3d6 Roll-Under | d6 Pool Equivalent |
|----------------------|----------------|-------------------|
| Novice               | TN 9-11        | 2-3 dice          |
| Competent            | TN 12-14       | 4-5 dice          |
| Expert               | TN 15-17       | 6-7 dice          |
| Master               | TN 18-21       | 8-10 dice         |

### Success Chance Comparison

| Task Difficulty | 3d6 TN 14 | 5d6 Pool |
|----------------|-----------|----------|
| Standard       | 74%       | 60%      |
| Moderate (-4)  | 38%       | 33%      |
| Major (-6)     | 16%       | 17%      |

*Note: The 3d6 system provides more granular probability steps, especially at higher skill levels.*

## Character Creation & Advancement

### Point Buy

The attribute point buy system remains the same, with two options:

- **Broad Competence**: Start with 5 points, maximum +2 in any attribute
- **Specialization**: Start with 4 points, maximum +3 in any attribute

### Skill Progression

Skills continue to use Skill Points (SP) with the same escalating costs, but provide different benefits:

- Level 1: +2 to TN (2 SP)
- Level 2: +4 to TN (5 SP total)
- Level 3: +6 to TN (9 SP total)

## Design Notes

This conversion maintains several key principles:

1. **Probabilities**: The 3d6 bell curve creates a more predictable range of outcomes than the d6 pool, with extremes being appropriately rare.

2. **Resource Management**: The wager mechanics still provide meaningful risk/reward decisions with appropriate consequences for overextending.

3. **Equipment Focus**: Gear remains important, affecting TNs directly while maintaining the current damage and defensive systems.

4. **Tactical Depth**: The ESP system ensures that exceptional skill still rewards tactical options through Stunts.

5. **Scalability**: The system scales well with character advancement, with room for supernatural capabilities at the high end.

6. **Conversion Ease**: Most existing character elements translate directly with minimal recalculation needed.

This system preserves what makes SellSword distinctive—its gritty tone, equipment focus, and tactical combat—while providing a probability curve that better rewards character investment and provides a more granular representation of capabilities.
