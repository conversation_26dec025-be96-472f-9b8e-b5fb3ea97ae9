---
name: # e.g., <PERSON>, Skirmisher, Bows
tier: # Category | Style | Specialization
category: # e.g., Athletics (Parent Category if Tier > 1)
style_of: # e.g., Ranged Combat (Parent Category for Style)
specialization_of: # e.g., Ranged Combat (Parent Category for Specialization) OR e.g., <PERSON>man (Parent Style for Specialization - though current design is Category L2)
cost: # Skill Point cost (Flat cost for Style/Specialization, use cost_l1/l2/l3 for Category)
cost_l1: # Skill Point cost for Level 1 (Only for Category)
cost_l2: # Skill Point cost for Level 2 (Only for Category)
cost_l3: # Skill Point cost for Level 3 (Only for Category)
prerequisite: # Link to required Skill/Attribute/Feature if any (e.g., "[[Ranged_Combat]] Level 1" for Style, "[[Ranged_Combat]] Level 2" for Specialization)
description_short: # Brief one-line description
tags: [skill] # Add [category], [style], or [specialization] as appropriate, and relevant tags like [combat], [social], [magic], etc.
---
# {{name}} ({{tier}})

(Detailed description of the skill, its benefits, and how it's used in gameplay)

## Tier: Category

(Use this section for Category skills with multiple levels)

### Level 1 (Cost: {{cost_l1}} SP)

* Benefit: Add +1 dice to relevant [[../Attributes/_Attributes_README|Derived Action]] rolls.

### Level 2 (Cost: {{cost_l2}} SP, Total: {{cost_l1 + cost_l2}} SP)

* Benefit: Add +1 dice (total +2) to relevant [[../Attributes/_Attributes_README|Derived Action]] rolls.

### Level 3 (Cost: {{cost_l3}} SP, Total: {{cost_l1 + cost_l2 + cost_l3}} SP)

* Benefit: Add +1 dice (total +3) to relevant [[../Attributes/_Attributes_README|Derived Action]] rolls.

## Tier: Style

(Use this section for Style skills - Level-less, flat cost)

* **Cost:** {{cost}} SP
* **Prerequisite:** {{prerequisite}}
* **Benefits:** (Describe the unique benefits of this Style. Focus on *how* actions are performed: granting unique Stunts/abilities, reducing Complexity, offering novel resource use. Generally *do not* add dice directly.)
    * Benefit: [Describe benefit 1]
    * Benefit: [Describe benefit 2]

## Tier: Specialization

(Use this section for Specialization skills - Level-less, flat cost)

* **Cost:** {{cost}} SP
* **Prerequisite:** {{prerequisite}}
* **Benefits:** (Describe the deep mastery benefits of this Specialization. Focus on *mastery* within a niche, enhancing existing actions/Stunts rather than gating new ones. Generally *do not* add dice directly.)
    * **Benefit:** [Benefit Type]: [Specific Effect] (Benefit Types: `Difficulty Reduction`, `Complexity Reduction`, `Stunt Enhancement`, `Effect Enhancement`, `AP Cost Reduction`, `Range Increase`, `Resource Interaction`, `Application`, `Crafting`. e.g., `Stunt Enhancement: Apply Force Stunt can throw grappled target 1 hex per Force effect`, `Complexity Reduction: -1 on checks to throw grappled opponents`, `Difficulty Reduction: -2 for attacks with bows`)
    * **Benefit:** [Benefit Type]: [Specific Effect]

(Remove sections that are not relevant to the specific skill being created - e.g., if creating a Style skill, remove the Category and Specialization sections.)
