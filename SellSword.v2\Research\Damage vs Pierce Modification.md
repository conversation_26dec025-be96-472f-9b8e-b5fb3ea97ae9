## Revision Document: Refining Combat Dynamics in Sellsword TTRPG

**1. Scope and Design Goals:**

This document addresses the design challenge identified in the Sellsword TTRPG system concerning the potential functional equivalency between the `+1 Damage` Stunt (typically PHY-based) and the `Target Weakness` Stunt (typically PRC-based, acting as +1 Pierce). When attacking a target with Damage Reduction (DR) where `Pierce < DR`, both stunts could result in the same numerical increase in Penetrating Damage, potentially diminishing the tactical distinction between brute force and precision attacks.

The primary goals for refining this interaction are:

- **Meaningful Choice:** Ensure that choosing between investing successes in raw damage versus armor penetration (or related effects) presents a distinct and situational tactical decision for the player.
- **Verisimilitude:** Model the effectiveness of heavy armor realistically, making it a significant obstacle that often requires specific tactics (like targeting gaps or applying overwhelming force) rather than simply being negated by moderate increases in damage.
- **Streamlined Resolution:** Maintain relatively quick combat resolution by avoiding excessive calculations or tracking, especially for the GM managing multiple opponents.
- **Leverage Existing Systems:** Integrate the solution smoothly with the existing Wound system, Stamina wager, attribute/skill system, and the currently undefined `Force` weapon stat.

**2. Proposed Solution: Conditional Stunts & "Apply Force"**

The core of the proposed solution is to make the availability of the `+1 Damage` stunt conditional on whether the attack's `Pierce` is sufficient to overcome the target's `DR`. This creates two distinct modes of attack based on the interaction with armor:

- **Scenario A: Armor Holds (`Pierce < DR`)**
    - The `+1 Damage` stunt **cannot** be purchased. The focus shifts from increasing injury to applying disruptive force.
    - Successes can instead be spent on a new, consolidated **"Apply Force"** stunt.
- **Scenario B: Armor Penetrated (`Pierce >= DR`)**
    - The `+1 Damage` stunt **can** be purchased normally, directly increasing the Penetrating Damage dealt.
    - The "Apply Force" stunt is unavailable, as the impact is focused on damaging the target _through_ the negated armor.

**2.1. The "Apply Force" Stunt:**

- **Consolidation:** This new stunt replaces and potentially expands upon existing stunts like `Push/Knockdown` and the Sunder aspect of `Disrupt`.
- **Cost:** 1 Success (purchasable multiple times if desired, potentially enhancing the effect or forcing multiple saves, TBD).
- **Trigger:** Can only be purchased if `Pierce < DR` for the attack.
- **Effect:** The attacker chooses **one** effect to apply, using the weapon's `Force` rating:
    - **Knockback:** Push the target back a number of hexes (e.g., `Force`/2, min 1).
    - **Off-Balance:** Impose a temporary condition (e.g., target cannot use reactions, suffers a dice penalty on their next action, or specific Voids become easier to target until they spend an action point to recover). This represents staggering the opponent or forcing them into a poor defensive posture.
    - **Sunder:** Inflict the Sunder condition (Level 1) on the armor at the hit location (see below).
- **Resistance:** The target makes a FRT roll against a Difficulty equal to the weapon's `Force` value to negate the chosen effect.

**2.2. Sunder as a Condition:**

To model armor degradation without heavy tracking, Sunder is treated as a tiered condition applied to a specific armor location (primarily relevant for PCs).

- **Level 1 (Minor):** DR reduced by 1 at location. Repair: Minor action (e.g., 1 Action Point) to adjust straps, clear debris.
- **Level 2 (Damaged):** DR reduced by 2 (or more) at location. Repair: Requires Exploration Action Point and supplies (`]`).
- **Level 3 (Broken):** Armor provides 0 DR at location. Repair: Requires downtime and Facilities.
- **Application:** Inflicted by the "Apply Force" stunt (Sunder option) on a failed FRT save. Higher levels might be inflicted by specific critical effects, environmental hazards, or repeated Sunder applications (TBD).

**3. Player vs. Monster Application:**

- **Players:** Subject to the full Sunder condition rules on their armor.
- **Monsters/NPCs:** To keep GM overhead low, they generally don't track Sunder levels. A successful "Apply Force" (Sunder) against them might instead impose a temporary DR penalty (-1 DR until end of next turn) or simply be treated as inflicting Off-Balance or Knockback at the GM's discretion. Monsters still use the base Wound thresholds (Minor/Major/Grievous counts) rather than the detailed PC wound system.

**4. Addressing Goals:**

- **Meaningful Choice:** Creates a clear decision point based on Pierce vs. DR. Do you invest in `Target Weakness` to enable `+1 Damage`, or use `Apply Force` for immediate disruption or armor degradation?
- **Verisimilitude:** Models heavy armor effectively stopping blows unless pierced, forcing attackers to use `Force` effects or precision (`Target Weakness`). Sunder reflects wear and tear.
- **Streamlined Resolution:** Adds one conditional check (Pierce vs DR) and potentially one resistance roll (FRT vs Force) per attack using "Apply Force". Sunder tracking is minimal (a level per location for PCs).
- **Leverage Existing Systems:** Uses `Force`, FRT, Successes, and integrates with the action point economy for Sunder repair.

---

## Rules "Diff" - Proposed Changes

_(Based on provided documents: `_Rules_README.md`, `_Stunts_README.md`, `_Equipment_README.md`, `_Wounds_README.md`)_

**1. `_Rules_README.md` (Combat Resolution Section)**

- **Modification:** Within the "Determine Damage" or "Apply Stunts" step, add the conditional logic:
    - **Before:** _(Implicitly, stunts could be bought if successes were available)_
    - **After:** "Compare the attack's total `Pierce` (from weapon + `Target Weakness` stunts) to the target's `DR` at the hit location.
        - If `Pierce < DR`: The `+1 Damage` stunt cannot be purchased for this attack. Successes may be spent on the `Apply Force` stunt instead.
        - If `Pierce >= DR`: The `+1 Damage` stunt may be purchased normally. The `Apply Force` stunt cannot be purchased for this attack."

**2. `_Stunts_README.md`**

- **Modification:** `]`
    - **Before:** "Cost: 1 Success | Attribute: PHY | Effect: +1 Base Damage"
    - **After:** "Cost: 1 Success | Attribute: PHY | Effect: +1 Base Damage. **Condition:** Can only be purchased if the attack's total `Pierce` is greater than or equal to the target's `DR` at the hit location."
- **Removal/Replacement:** `]`
    - **Action:** Remove this stunt. Its effects are incorporated into the new "Apply Force" stunt.
- **Modification:** `]`
    - **Before:** _(Contained Sunder effect)_
    - **After:** Review the `Disrupt` stunt. If its primary function was Sunder, remove it and incorporate Sunder into "Apply Force". If it had other distinct effects (e.g., specifically targeting held items), retain those but remove the armor sundering aspect.
- **Addition:** New Stunt - **`Apply Force`**
    - **Cost:** 1 Success
    - **Attribute:** PHY (or potentially allow Stamina wager to use with other attack attributes?)
    - **Condition:** Can only be purchased if the attack's total `Pierce` is less than the target's `DR` at the hit location.
    - **Effect:** Choose one effect. The target makes a FRT roll (Difficulty = Weapon `Force`) to resist.
        - **Knockback:** Target is pushed back 1 hex per point of `Force` (min 1 hex).
        - **Off-Balance:** Target suffers.
        - **Sunder:** Target's armor at the hit location gains the Sunder (Level 1) condition. (Primarily affects PCs).
    - _(Note: Define if multiple purchases enhance effect, force multiple saves, or allow multiple effects)._

**3. `_Equipment_README.md`**

- **Review:** Ensure all relevant weapons have a `Force` value assigned. Consider typical `Force` values (e.g., 1 for light/fast weapons, 2-3 for medium, 4+ for heavy/impact weapons).
- **Addition:** Add a section defining Armor Conditions (specifically Sunder).
    - **Sunder (Condition):** A tiered condition applied to armor locations.
        - **Level 1 (Minor):** -1 DR at location. Repair: 1 Action Point.
        - **Level 2 (Damaged):** -2 DR at location. Repair: Exploration Action Point + Supplies (`]`).
        - **Level 3 (Broken):** 0 DR at location. Repair: Downtime + Facilities.
    - _(Note: Specify if/how Sunder levels escalate)._

**4. `_Wounds_README.md`**

- **Clarification:** Add guidance for GM Wound Choice (as discussed previously, though not strictly part of the core mechanic change, it complements it):
    - Add a note in the GM section or Wound determination: "When selecting the specific wound row within a severity tier, consider the narrative context. Hits penetrating via high damage or `+1 Damage` stunts often suggest Blunt or Bone Trauma. Hits penetrating via `Target Weakness` or high base `Pierce` often suggest Piercing or Laceration wounds. Hits resisted by armor but triggering `Apply Force` might still cause Blunt Trauma from the impact, even with 0 Penetrating Damage."

---

This revised structure provides clear mechanical differentiation based on the interaction with armor, leverages the `Force` stat, introduces a manageable armor degradation system for PCs, and maintains relative simplicity for the GM, aligning with your stated goals. Remember to playtest these changes thoroughly.