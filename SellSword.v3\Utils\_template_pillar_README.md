# {PILLAR_NAME}

A concise overview of the purpose and scope of the **{PILLAR_NAME}** pillar.

## Prerequisites

- Read the [Project Overview](SellSword_v2_Overview.md).
- Familiarize yourself with the core rules: [Core Rules](_Rules_README.md).

## Folder Structure

- `/{PILLAR_FOLDER}/` – Main directory for this pillar’s entries.  
- `_template_{pillar}_entry.md` – Template file for creating new entries.  
- Other subdirectories as needed (e.g., `/SubfolderName/`).

## Content Overview

Describe the types of files and data in this pillar, including:

- Key concepts or mechanics.  
- Entry types (e.g., items, features, lore entries).  
- Naming conventions (e.g., `Entry_Name.md` with YAML frontmatter).

## Conventions

- **File Naming:** Title case, underscores for spaces (e.g., `My_Entry.md`).  
- **Frontmatter Fields:**  
  - `name`: Display name  
  - `type`: Category or subtype  
  - `tags`: List of relevant tags  
  - `summary`: Short description  
- **Links:** Use Obsidian-style `[[...]]` links for cross-references.

## How to Use

1. Navigate to this pillar’s directory.  
2. Browse existing entries by name or tag.  
3. Open the entry file to view details and mechanics.

## Related Links

- [[SellSword_v2_Overview|Project Overview]]  
- [[_Rules_README|Core Rules]]  
- [[_Attributes_README|Attributes & Actions]]  
- [[_Skills_README|Skills Overview]]  
- [[_Equipment_README|Equipment Overview]]  
- [[_Stunts_README|Stunts List]]  
- [[_Lore_README|Lore & Wyrds]]  
- [[_Standing_README|Standing System]]  
- [[_Monsters_README|Monster Design]]  
- [[_Wounds_README|Wounds System]]  
- [[_World_README|World Overview]]

## Adding New Entries

1. Copy `_template_{pillar}_entry.md` into this folder.  
2. Rename it using the established naming convention.  
3. Fill in the YAML frontmatter and content sections.  
4. Commit your changes with a descriptive message.

## Revision History

- v2 – YYYY‑MM‑DD – Initial template for pillar README files.
