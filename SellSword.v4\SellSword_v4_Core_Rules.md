# SellSword v4 - Core Rules

*A gritty fantasy tabletop RPG where equipment matters, choices have consequences, and survival demands both skill and cunning*

## Table of Contents

1. [Design Philosophy](#design-philosophy)
2. [Core Mechanics](#core-mechanics)
3. [Character Creation](#character-creation)
4. [Skills System](#skills-system)
5. [Combat](#combat)
6. [Magic](#magic)
7. [Equipment](#equipment)
8. [Exploration & Survival](#exploration--survival)
9. [Social & Downtime](#social--downtime)
10. [Quick Reference](#quick-reference)

---

## Design Philosophy

### The Sellsword Life

You are mercenaries, soldiers of fortune, and adventurers-for-hire in a world where survival depends on preparation, skill, and having the right tool for the job. SellSword embraces a **gritty realism** where:

- **Equipment Matters:** Your gear defines your capabilities. A well-equipped sellsword with quality armor and weapons has a decisive advantage over someone relying on natural talent alone.
- **Resources Are Precious:** Torches burn out, arrows run low, and healing takes time. Every choice about what to carry and when to use it matters.
- **Danger Is Real:** Combat is lethal, exploration is perilous, and failure has lasting consequences. Characters advance through experience, not plot armor.
- **Tactics Over Stats:** Success comes from smart positioning, proper preparation, and using the right approach rather than simply having high numbers.

### Core Design Goals

**High Verisimilitude:** The world feels grounded and believable. Magic follows consistent rules, combat reflects real weapon dynamics, and social interactions have meaningful stakes.

**Manageable Crunch:** Depth without complexity paralysis. The system provides tactical options and meaningful choices while keeping play flowing smoothly.

**Meaningful Choices:** Every decision—from character creation to equipment selection to combat tactics—should present interesting trade-offs with clear consequences.

**Collaborative Storytelling:** While the world is dangerous, the focus remains on creating compelling stories of sellswords navigating a complex world through wit, skill, and determination.

---

## Core Mechanics

### The Dice Pool System

SellSword v4 uses pools of six-sided dice (d6) for all task resolution.

**Building Your Pool:**
```
Base Pool = Action Score + Skill Dice + Equipment Dice + Wager Dice - Difficulty Dice
```

**Rolling for Success:**
- Roll your final dice pool
- Each **6** rolled = **1 Success**
- Most actions need **1 Success** to succeed
- Complex actions may require **multiple successes**

**Extra Successes:**
- Successes beyond what's needed can be spent on **Stunts** - immediate beneficial effects like extra damage, tactical advantages, or narrative flourishes

### The Wager System

Before rolling, you may **wager** resources to add bonus dice:

**Stamina Wager** (for Physique/Finesse actions):
- Spend Stamina points to add dice (1 Stamina = +1 die)
- If you roll any **1s** on wagered dice they are lost

**Will Wager** (for Precision/Focus/Insight actions):
- Spend Will points to add dice (1 Will = +1 die)  
- If you roll any **1s** on wagered dice, they are lost

### Difficulty & Complexity

**Difficulty** represents external obstacles:
- Subtracts dice from your pool before rolling
- **Minor:** -2 dice (poor lighting, minor cover)
- **Major:** -4 dice (heavy cover, difficult conditions)
- **Extreme:** -6 dice (near-impossible conditions)

**Complexity** represents intricate tasks:
- Requires multiple successes to fully succeed
- Examples: Masterwork crafting (Complexity 2), Complex spells (Complexity 3)
- Partial success possible if you don't meet full requirement

---

## Character Creation

### Step 1: Prime Attributes

You have **6 Prime Attributes** rated from -3 to +3 (0 = average):

- **Strength (STR)** - Raw physical power
- **Agility (AGI)** - Speed, reflexes, coordination
- **Dexterity (DEX)** - Fine motor control, precision
- **Acuity (ACU)** - Awareness, perception, attention
- **Presence (PRE)** - Force of personality, leadership
- **Intellect (INT)** - Reasoning, memory, knowledge

**Point Buy Method:**
- Distribute attributes so the total equals **+3**
- No single attribute may exceed **+3**
- You may take negative attributes to balance higher positive ones

*Example arrays: [+2,+1,+1,0,0,-1] or [+3,+2,0,0,-1,-1]*

### Step 2: Actions

Calculate your **5 Actions** (these determine your base dice pools):

- **Physique** = 3 + STR + AGI *(forceful, dynamic actions)*
- **Finesse** = 3 + AGI + DEX *(graceful, quick actions)*
- **Precision** = 3 + DEX + ACU *(careful, accurate actions)*
- **Focus** = 3 + ACU + PRE *(sustained attention, willpower)*
- **Insight** = 3 + PRE + INT *(understanding, deduction)*

### Step 3: Derived Stats

Calculate your resources and capabilities:

- **Speed** = 3 + (AGI × 2) + Mobility skill levels
- **Carry** = 3 + (STR × 2) + Athletics skill levels  
- **Stamina** = Physique + Fortitude skill levels
- **Will** = Focus + Resolve skill levels
- **Lore Slots** = 3 + (INT × 2)
- **Standing Slots** = 3 + PRE

### Step 4: Skills

You start with **7 Skill Points (SP)** to spend on skills.

**Skill Costs:**
- Level 1: 1 SP
- Level 2: 2 additional SP (3 total)
- Level 3: 3 additional SP (6 total)

Each skill level adds +1 die to relevant action rolls, plus secondary benefits.

---

## Skills System

### Core Skills (15 Total)

Skills enhance your actions and provide secondary benefits. Each skill can be taken up to Level 3.

#### Physique Skills
- **Fortitude** - Endurance, resisting effects *(+Stamina)*
- **Heavy Melee** - Two-handed weapons, shields, forceful combat
- **Athletics** - Climbing, jumping, swimming, physical feats *(+Carry)*

#### Finesse Skills  
- **Mobility** - Movement, dodging, acrobatics *(+Speed)*
- **Light Melee** - One-handed weapons, finesse combat
- **Stealth** - Moving unseen, hiding

#### Precision Skills
- **Subtlety** - Lockpicking, traps, sleight of hand
- **Ranged** - Bows, crossbows, thrown weapons
- **Weave** - Shaping magical effects *(requires Wyrd Lore)*

#### Focus Skills
- **Resolve** - Mental fortitude, resisting mental effects *(+Will)*
- **Observe** - Searching, tracking, noticing details
- **Evoke** - Gathering magical power *(requires Wyrd Lore)*

#### Insight Skills
- **Reason** - Logic, investigation, problem-solving *(+Lore Slots)*
- **Influence** - Persuasion, intimidation, leadership
- **Scry** - Magical perception, divination *(requires Wyrd Lore)*

### Secondary Skills

Beyond the 15 core skills, characters can learn specialized techniques that provide unique mechanical benefits without adding dice. These represent mastery of specific tools, techniques, or approaches.

*Examples: Dual Wielding, Beast Taming, Specific Weapon Masteries, Crafting Specializations*

*(See individual skill files for detailed mechanics and prerequisites)*

---

## Combat

### Combat Flow

Combat uses a **Declarative/Active** system:

1. **Declarative Phase:** GM declares NPC actions, then players declare their actions
2. **Active Phase:** GM resolves all actions in logical order (typically lowest AP cost first)

### Action Points

Each character has **4 Action Points (AP)** per round to spend on actions:

- **Move:** 1 AP (move up to your Speed)
- **Attack:** AP = weapon's Carry value
- **Defend:** 1 AP (Dodge) or weapon's Carry value (Parry/Block)
- **Other Actions:** Typically 1 AP

### Making Attacks

1. **Declare Target & Hit Location:**
   - **Body:** +0 Difficulty (torso, limbs)
   - **Voids:** +3 Difficulty (gaps in armor, joints)
   - **Vitals:** +5 Difficulty (head, heart)

2. **Build Dice Pool:** Relevant Action + Skill + Weapon Offense - Difficulty

3. **Apply Combat Modifiers:**
   - **Upper Hand:** +2 dice (flanking, assistance, advantageous position)
   - **Compromised:** -2 dice (being flanked, off-balance, disadvantageous position)

4. **Roll & Apply Results:**
   - 1+ Successes = Hit
   - Extra successes = Stunts (extra damage, tactical effects)

### Damage Resolution

1. **Base Damage:** Use weapon's Damage value
2. **Apply Pierce:** Weapon's Pierce reduces target's effective DR
3. **Calculate Penetrating Damage:** Final Damage - (Target DR - Pierce)
4. **Roll Wounds:** If damage penetrates, roll d6s equal to penetrating damage
5. **Apply Force Effects:** Weapon's Force can trigger knockback or Sunder effects via Stunts

**Example:** Longsword (Damage 2, Pierce 1) hits mail armor (Body DR 4)
- Effective DR = 4 - 1 = 3
- Penetrating Damage = 2 - 3 = 0 (no wound)
- Against leather armor (Body DR 2): Penetrating = 2 - (2-1) = 1 (roll 1d6 for wound)

### Damage & Wounds

**Damage Process:**
1. Successful hit deals weapon's base Damage
2. Subtract target's Damage Reduction (DR) for that hit location
3. If damage penetrates (Damage > DR), roll Wound dice
4. Consult appropriate Wound Table for specific effects

**Wound System:**
- No Hit Points - wounds have specific mechanical effects
- **Penetrating Damage** determines number of d6 rolled for wound severity
- Higher wound rolls = more severe injuries with greater penalties

**Armor:**
- Provides Damage Reduction by location (Body DR, Voids DR, Vitals DR)
- Heavy armor may impose Fatigue or Encumbrance conditions

### Defense

**Passive Defense:**
- **Target Difficulty (TD)** = 0 + AGI + Size modifier
- Applied as Difficulty dice to attackers

**Active Defense:**
- **Dodge:** 1 AP, opposed roll using Finesse + Mobility
- **Parry/Block:** Weapon's Carry AP, opposed roll using appropriate skill
- Success negates the attack entirely

---

## Magic

### The Aether and the Veil

Magic in SellSword draws power from the **Aether** - a realm of pure magical energy that exists parallel to the physical world (the **Firmament**). Between these realms lies the **Veil**, a barrier that both separates and connects them.

**The Firmament:** The physical world where mortals live, governed by natural laws.

**The Aether:** A realm of raw magical forces, organized into domains called **Wyrds**.

**The Veil:** The boundary between realms. Mages manipulate the Veil to channel Aetheric power into the Firmament, creating magical effects.

### The Wyrd System

Magic manipulates fundamental forces called **Wyrds** - domains of Aetheric influence that govern specific aspects of reality. These are categorized into Elemental, Mental, and Spiritual Wyrds.

**Elemental Wyrds** *(Physical Forces)*
- **Fyr** (Fire) - Heat, flame, combustion, destruction, passion, change
- **Eorthe** (Earth) - Stone, metal, stability, endurance, permanence
- **Wynd** (Air) - Wind, breath, movement, freedom, communication
- **Waeter** (Water) - Flow, change, healing, emotion, adaptation
- **Leoht** (Light) - Illumination, truth, revelation, purity, clarity

**Mental Wyrds** *(Forces of Mind and Perception)*
- **Mod** (Mind) - Thought, memory, emotion, empathy, communication
- **Raed** (Logic) - Reason, structure, analysis, understanding
- **Swefn** (Dreams) - Subconscious, illusion, imagination, sleep
- **Wyrd** (Foresight) - Prophecy, divination, probability, time
- **Gemynd** (Echoes) - Past resonance, lingering imprints, history

**Spiritual Wyrds** *(Forces of Life and Death)*
- **Lif** (Life) - Growth, vitality, healing, nature, renewal
- **Deaþ** (Death) - Decay, endings, entropy, undeath
- **Sawol** (Soul) - Spirit, communion, essence, identity
- **Sceadu** (Shadow) - Concealment, fear, mystery, the unknown
- **Galdor** (Raw Magic) - Pure magical force, disruption, chaos

**Requirements for Magic:**
- Must know the relevant **Wyrd Lore** to understand and access that domain
- Must have the appropriate **Magic Skill** to manipulate Aetheric forces
- Must be able to perceive or interact with the Veil

### Magic Skills

**Evoke** (Focus-based):
- Gathering raw Aetheric power from a known Wyrd
- Determines the intensity and magnitude of magical effects
- Higher skill = more power drawn, greater potential effects

**Weave** (Precision-based):  
- Shaping gathered Aetheric power into specific, controlled effects
- Determines complexity, duration, precision, and stability
- Higher skill = more intricate effects, longer durations

**Scry** (Insight-based):
- Using Aetheric perception to gather information beyond normal senses
- Determines clarity, range, and detail of magical perception
- Higher skill = clearer visions, greater distances, more subtle information

### Casting Magic

**The Magical Process:**

1. **Declare Intent:** Describe the desired effect and which Wyrd(s) you're accessing
2. **GM Sets Requirements:** Determines which magic skills are needed, Difficulty modifiers, and Complexity requirements
3. **Make Skill Checks:** Roll for each required magic skill
4. **Resolve Effect:** Success creates the intended magical effect

**Common Combinations:**
- **Evoke only:** Raw, uncontrolled bursts of power
- **Evoke + Weave:** Controlled, shaped magical effects
- **Scry only:** Magical perception and divination
- **All three:** Complex, sustained magical workings

**Magic Mishaps:**
When magic fails (especially with wagered Will), the unstable Aetheric forces can cause **Aetheric Wounds**:
- Severity based on the total of Will dice results from the failed casting
- Effects range from temporary magical exhaustion to permanent scarring of the caster's connection to the Aether
- Severe mishaps might tear the Veil locally, causing unpredictable magical phenomena

**Magical Fatigue:** Repeated spellcasting depletes Will and can impose cumulative penalties until the caster rests and recovers their connection to the Aether.

*(See Magic Guide and individual Wyrd Lore files for detailed spells, effects, and magical theory)*

---

## Equipment

### The Right Tool for the Job

In SellSword, **equipment defines capability**. A sellsword's survival depends on having the proper gear for each situation. Quality equipment provides crucial advantages:

- **Weapons** determine your offensive and defensive capabilities through multiple stats
- **Armor** is the difference between a glancing blow and a mortal wound
- **Tools** enable specialized actions that would otherwise be impossible
- **Supplies** sustain you through dangerous expeditions

### Weapons

**Complete Weapon Stats:**
- **Offense:** Adds dice to attack rolls, representing weapon effectiveness
- **Damage:** Base damage dealt on successful hit
- **Pierce:** Reduces target's effective Damage Reduction
- **Force:** Impact momentum for knockback and armor sundering effects
- **Block:** Dice bonus to blocking maneuvers
- **Parry:** Dice bonus to parrying attacks
- **Hook:** Ability to manipulate opponent's weapon or body
- **Reach:** Effective melee distance added to character's base reach
- **Range:** For ranged weapons, optimal distance (multiplier of Precision)
- **Carry:** Bulk/weight determining AP cost and contributing to Fatigue

**Action Point Costs:**
- **Attack AP Cost = Carry value**
- **Block/Parry AP Cost = Carry value**
- **Two-Handed Use:** Reduces AP cost by 1 (minimum 1 AP) for all weapon actions

**Weapon Examples:**

**Dagger:** Offense 1, Damage 2, Pierce 0, Force 0, Block 0, Parry 1, Hook 0, Reach 0, Carry 1
*Fast, concealable, minimal defensive capability*

**Longsword:** Offense 2, Damage 2, Pierce 1, Force 1, Block 1, Parry 2, Hook 0, Reach 1, Carry 2
*Versatile weapon, excellent for both offense and defense*

**Greatsword:** Offense 3, Damage 3, Pierce 1, Force 2, Block 1, Parry 1, Hook 0, Reach 2, Carry 3
*High damage, requires two hands, slower but powerful*

**War Hammer:** Offense 2, Damage 2, Pierce 2, Force 2, Block 0, Parry 0, Hook 1, Reach 1, Carry 3
*Armor-piercing specialist, poor defensive capability*

**Crossbow:** Offense 2, Damage 2, Pierce 2, Force 1, Range 4, Carry 2
*Reliable ranged weapon with armor-piercing capability*

### Armor

**Armor Philosophy:** Armor saves lives. The difference between Body DR 2 and DR 6 often determines whether an attack causes a minor wound or proves fatal.

**Armor Stats:**
- **Body DR:** Damage reduction for torso and limb hits
- **Voids DR:** Damage reduction for gaps, joints, and weak points
- **Encumbrance:** VoidsDR - 1, imposes -1 die penalty per point on Physique, Finesse, and Precision actions; also reduces Speed by same amount
- **Fatigue:** Carry - 1, reduces maximum Stamina by this amount
- **Durability:** Resistance to damage and wear

**Armor Examples:**

**Leather Armor:** Body DR 2, Voids DR 1, Carry 1, Durability 2
*Encumbrance 0, Fatigue 0 - Light protection with no penalties*

**Studded Leather:** Body DR 3, Voids DR 1, Carry 2, Durability 3
*Encumbrance 0, Fatigue 1 - Better protection with minor stamina cost*

**Mail Shirt:** Body DR 4, Voids DR 2, Carry 3, Durability 3
*Encumbrance 1, Fatigue 2 - Good protection with moderate penalties*

**Full Mail:** Body DR 5, Voids DR 3, Carry 4, Durability 4
*Encumbrance 2, Fatigue 3 - Excellent protection with significant penalties*

**Plate Harness:** Body DR 6, Voids DR 4, Carry 5, Durability 5
*Encumbrance 3, Fatigue 4 - Maximum protection with heavy penalties*

### Armor Conditions: Sunder

Armor can suffer damage from concentrated Force effects:

**Level 1 (Minor Sunder):** Reduce DR at affected location by 1
*Repair: 1 AP (tightening straps, clearing debris)*

**Level 2 (Damaged Sunder):** Reduce DR at affected location by 2
*Repair: 1 Exploration AP plus appropriate supplies*

**Level 3 (Broken Sunder):** Armor provides 0 DR at affected location
*Repair: Requires downtime and workshop facilities*

### Shields

**Shield Stats:**
- **Block:** Dice bonus to blocking maneuvers
- **Cover:** Adds Difficulty dice to attackers targeting the shield bearer
- **Carry:** Determines AP cost and contributes to Fatigue

**Shield Examples:**

**Buckler:** Block 2, Cover 1, Carry 1
*Small, maneuverable shield for dueling*

**Round Shield:** Block 3, Cover 2, Carry 2
*Balanced protection and mobility*

**Tower Shield:** Block 2, Cover 4, Carry 4
*Maximum protection, heavy and slow*

### Essential Gear

**Adventuring Equipment:**
- **Torches:** Provide light but burn out over time (diminishing dice pools)
- **Rope:** Enables climbing, securing, and rescue operations
- **Tools:** Lockpicks, crowbars, hammers - each enables specific actions
- **Medical Supplies:** Bandages, herbs, and healing draughts
- **Provisions:** Food and water for extended expeditions

**Quality and Masterwork Equipment:**
Superior equipment provides enhanced capabilities:
- **+1 Offense:** Masterwork weapons add dice to attacks
- **+1 DR:** Superior armor provides better protection
- **Reduced Encumbrance/Fatigue:** Well-crafted gear minimizes penalties
- **Enhanced Durability:** Quality items last longer and resist damage

**Equipment Degradation:**
Weapons and armor suffer wear from use and may require maintenance or replacement. Durability determines how much abuse equipment can sustain before becoming less effective.

*(See Equipment files for complete weapon, armor, and gear lists)*

---

## Exploration & Survival

### The Dangerous World

Exploration in SellSword emphasizes **resource management** and **environmental hazards**. The world beyond civilization is hostile, and preparation often determines survival.

### Travel & Navigation

**Distance Categories:**
- **Close:** Same region, 1-2 days travel
- **Far:** Adjacent regions, 3-7 days travel  
- **Very Far:** Distant lands, weeks of travel

**Journey Events:** Longer travels trigger potential encounters, weather events, or resource challenges. The GM may call for Survival, Observe, or other skill checks to navigate safely.

**Getting Lost:** Failed navigation in wilderness can extend journeys, consume extra supplies, or lead to dangerous encounters.

### Light & Darkness

**Torch Mechanics:** Torches start with 4 dice. At the conclusion of every exploration round (15 minutes), roll the torch's current dice pool:
- **No 1s:** Torch continues burning
- **Any 1s:** Remove 1 die from the torch's pool for each 1 rolled
- **0 dice remaining:** Torch burns out
- **Use Torch roll for Dungeon Events:** The total rolled determines what happens during exploration (see Dungeon Event Tables below)

**Expected Torch Duration:** Understanding average dice rolls helps predict torch burn rates:
- **4 dice:** Average roll 14, typically burns 4-6 rounds (60-90 minutes)
- **3 dice:** Average roll 10.5, typically burns 3-5 rounds (45-75 minutes)
- **2 dice:** Average roll 7, typically burns 2-4 rounds (30-60 minutes)
- **1 die:** Average roll 3.5, typically burns 1-3 rounds (15-45 minutes)

*Note: These are statistical averages - actual burn time varies significantly based on dice rolls.*

**Alternate Method:** For faster-paced exploration, use 10-minute rounds with torches starting at 6 dice:
- **6 dice:** Average roll 21, typically burns 6-8 rounds (60-80 minutes)
- **5 dice:** Average roll 17.5, typically burns 5-7 rounds (50-70 minutes)
- **4 dice:** Average roll 14, typically burns 4-6 rounds (40-60 minutes)
- **3 dice:** Average roll 10.5, typically burns 3-5 rounds (30-50 minutes)
- **2 dice:** Average roll 7, typically burns 2-4 rounds (20-40 minutes)
- **1 die:** Average roll 3.5, typically burns 1-3 rounds (10-30 minutes)

### Dungeon Event Tables

**Event Table Philosophy:** The torch roll total determines exploration events, with outcomes becoming more dangerous as the light dims. Higher rolls (bright torches) may reveal opportunities, while lower rolls (dying flames) attract threats.

**Sample 4d6 Torch Event Table (15-minute rounds):**
- **23-24:** Beneficial - Discover useful item, shortcut, or advantageous position
- **21-22:** Neutral - Uneventful exploration, minor environmental details
- **19-20:** Minor Complication - Strange sounds, unsettling atmosphere, minor obstacle
- **16-18:** Moderate Threat - Wandering creature, environmental hazard, resource strain
- **12-15:** Serious Danger - Hostile encounter, trap activation, significant obstacle
- **8-11:** Major Crisis - Dangerous enemy, severe hazard, equipment failure
- **4-7:** Dire Threat - Overwhelming foe, deadly trap, catastrophic event

**Sample 6d6 Torch Event Table (10-minute rounds):**
- **30-36:** Beneficial - Major discovery, tactical advantage, valuable resource
- **26-29:** Minor Benefit - Useful finding, favorable conditions, small advantage
- **23-25:** Neutral - Quiet exploration, atmospheric details, minor progress
- **20-22:** Minor Complication - Odd noises, unsettling signs, small delays
- **16-19:** Moderate Threat - Potential danger, environmental challenge, resource concern
- **12-15:** Serious Danger - Active threat, hazardous conditions, equipment stress
- **6-11:** Major Crisis - Immediate peril, severe obstacle, critical failure

*Note: GMs should customize event tables to match their dungeon's theme, danger level, and narrative needs. The key principle is that dimming light increases danger.*

**Darkness Penalties:** Operating without light imposes Major (-4) or Extreme (-6) Difficulty on most actions. Stealth may benefit from darkness, but movement and combat become treacherous.

**Light Sources:**
- **Torch:** 4 dice, burns 60-90 minutes average, illuminates Close range
- **Lantern:** 6 dice, burns 2-4 hours with oil, better light quality
- **Candle:** 2 dice, burns 1-2 hours, minimal illumination

### Environmental Hazards

**Weather:** Storms, extreme cold, or heat can impose Difficulty penalties and require shelter or appropriate gear.

**Terrain:** Cliffs, swamps, dense forests, or ruins may require specific skills (Athletics, Survival, Subtlety) to navigate safely.

**Fatigue:** Extended travel without rest can reduce Stamina or impose penalties until characters rest properly.

### Resource Management

**Supplies:** Food, water, ammunition, and medical supplies are finite. Characters must plan for expeditions and manage consumption carefully.

**Equipment Degradation:** Weapons and armor may suffer damage from use, requiring maintenance or replacement.

**Carrying Capacity:** Characters can only carry so much. Exceeding Carry limits imposes Encumbrance penalties.

---

## Social & Downtime

### The Sellsword's Reputation

Between adventures, sellswords must maintain their equipment, seek new contracts, and navigate the complex social networks that determine their livelihood.

### Standing System

**Standing** represents your social capital, connections, and reputation within various groups or organizations. Each Standing provides access to specific resources, information, or opportunities.

**Standing Types:**
- **Guild Affiliation:** Access to specialized services, training, or contracts
- **Noble Favor:** Political protection, court access, or aristocratic resources
- **Criminal Contacts:** Black market goods, information networks, or safe houses
- **Military Connections:** Veteran networks, equipment access, or mercenary contracts

**Using Standing:** During downtime, spend Standing to:
- Acquire equipment or services
- Gather information about potential contracts
- Gain temporary allies or assistance
- Access restricted areas or resources

### Influence & Negotiation

**Social Encounters:** Use Insight + Influence for persuasion, intimidation, or leadership. Modifiers based on:
- **Relationship:** Friendly (+2), Neutral (0), Hostile (-2)
- **Social Context:** Formal settings, cultural differences, power dynamics
- **Request Scope:** Simple favors vs. significant commitments

**Reputation Matters:** Your actions build or damage your reputation. A sellsword known for honor attracts different contracts than one known for ruthlessness.

### Downtime Activities

**Equipment Maintenance:** Repair damaged gear, upgrade equipment, or commission custom items.

**Training:** Spend time and resources to gain new skills or improve existing ones.

**Information Gathering:** Research potential contracts, investigate threats, or learn about new regions.

**Recovery:** Heal wounds, restore resources, and prepare for the next adventure.

**Networking:** Build new Standing relationships or strengthen existing ones.

### Advancement

Characters advance through experience gained during adventures:

**Skill Points:** Earned through successful missions, overcoming significant challenges, or achieving personal goals.

**New Capabilities:**
- Increase existing skill levels (more dice, better secondary benefits)
- Learn new core skills (expand your tactical options)
- Acquire secondary skills (specialized techniques and masteries)
- Expand Lore knowledge (new magical domains or specialized knowledge)
- Develop Standing relationships (new social connections and resources)

**Advancement Philosophy:** Growth comes from experience, not arbitrary leveling. Characters become more capable by facing challenges and learning from them.

---

## Quick Reference

### Action Resolution
1. Build Pool: Action + Skill + Equipment + Wager - Difficulty
2. Apply modifiers: Upper Hand (+2) or Compromised (-2)
3. Roll dice, count 6s as successes
4. Spend extra successes on Stunts

### Attack Resolution
1. Build Pool: Action + Skill + Weapon Offense + Wager - Difficulty
2. Apply combat modifiers and hit location difficulty
3. Roll for hit, spend extra successes on Stunts

### Damage Process
1. Hit → Deal weapon Damage
2. Apply Pierce: Effective DR = Target DR - Weapon Pierce
3. Calculate penetration: Damage - Effective DR
4. If penetrating damage > 0 → Roll wound dice
5. Apply Force effects via Stunts (knockback, Sunder)

### Difficulty Standards
- **Minor:** -2 dice
- **Major:** -4 dice  
- **Extreme:** -6 dice

### Combat Modifiers
- **Upper Hand:** +2 dice (flanking, assistance, advantage)
- **Compromised:** -2 dice (flanked, off-balance, disadvantage)

### Hit Locations
- **Body:** +0 Difficulty (torso, limbs)
- **Voids:** +3 Difficulty (gaps, joints)
- **Vitals:** +5 Difficulty (head, heart)

### Action Point Costs
- **Attack:** Weapon Carry (reduced by 1 if two-handed, minimum 1)
- **Block/Parry:** Weapon Carry (reduced by 1 if two-handed, minimum 1)
- **Dodge:** 1 AP
- **Move:** 1 AP

### Armor Effects
- **Encumbrance:** VoidsDR - 1 (penalty to FIN/PRC actions and Speed)
- **Fatigue:** Carry - 1 (penalty to PHY actions and reduces maximum Stamina)

### Resource Formulas
- **Stamina** = Physique + Fortitude levels - Armor Fatigue
- **Will** = Focus + Resolve levels  
- **Speed** = 3 + (AGI × 2) + Mobility levels - Encumbrance
- **Carry** = 3 + (STR × 2) + Athletics levels

---

*For detailed rules on specific skills, magic systems, equipment, monsters, and world lore, see the individual domain files in their respective directories.*

**Version:** 4.0  
**Last Updated:** [Current Date]
