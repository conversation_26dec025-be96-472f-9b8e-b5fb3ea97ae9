# Overview

Dataview is a live index and query engine over your personal knowledge base. You can [**add metadata**](https://blacksmithgu.github.io/obsidian-dataview/annotation/add-metadata/) to your notes and **query** them with the [**Dataview Query Language**](https://blacksmithgu.github.io/obsidian-dataview/queries/structure/) to list, filter, sort or group your data. Dataview keeps your queries always up to date and makes data aggregation a breeze.

You could

- Track your sleep by recording it in daily notes, and automatically create weekly tables of your sleep schedule.
- Automatically collect links to books in your notes, and render them all sorted by rating.
- Automatically collect pages associated with today's date and show them in your daily note.
- Find pages with no tags for follow-up, or show pretty views of specifically-tagged pages.
- Create dynamic views which show upcoming birthdays or events recorded in your notes

and many more things.

Dataview gives you a fast way to search, display and operate on indexed data in your vault!

Dataview is highly generic and high performance, scaling up to hundreds of thousands of annotated notes without issue.

If the built in [query language](https://blacksmithgu.github.io/obsidian-dataview/queries/structure/) is insufficient for your purpose, you can run arbitrary JavaScript against the [dataview API](https://blacksmithgu.github.io/obsidian-dataview/api/intro/) and build whatever utility you might need yourself, right in your notes.

Dataview is about displaying, not editing

Dataview is meant for displaying and calculating data. It is not meant to edit your notes/metadata and will always leave them untouched (... except if you're checking a [Task](https://blacksmithgu.github.io/obsidian-dataview/queries/query-types/#task) through Dataview.)

## How to Use Dataview

Dataview consists of two big building blocks: **Data Indexing** and **Data Querying**.

More details on the linked documentation pages

The following sections should give you a general overview about what you can do with dataview and how. Be sure to visit the linked pages to find out more about the individual parts.

### Data Indexing

Dataview operates on metadata in your Markdown files. It cannot read everything in your vault, but only specific data. Some of your content, like tags and bullet points (including tasks), are [available automatically](https://blacksmithgu.github.io/obsidian-dataview/annotation/add-metadata/#implicit-fields) in Dataview. You can add other data through **fields**, either on top of your file [per YAML Frontmatter](https://blacksmithgu.github.io/obsidian-dataview/annotation/add-metadata/#frontmatter) or in the middle of your content with [Inline Fields](https://blacksmithgu.github.io/obsidian-dataview/annotation/add-metadata/#inline-fields) via the `[key:: value]` syntax. Dataview _indexes_ these data to make it available for you to query.

Dataview indexes [certain information](https://blacksmithgu.github.io/obsidian-dataview/annotation/add-metadata/#implicit-fields) like tags and list items and the data you add via fields. Only indexed data is available in a Dataview query!

For example, a file might look like this:

`--- author: "Edgar Allan Poe" published: 1845 tags: poems ---  # The Raven  Once upon a midnight dreary, while I pondered, weak and weary, Over many a quaint and curious volume of forgotten lore—`

Or like this:

`#poems  # The Raven  From [author:: Edgar Allan Poe], written in (published:: 1845)  Once upon a midnight dreary, while I pondered, weak and weary, Over many a quaint and curious volume of forgotten lore—`

In terms of indexed metadata (or what you can query), they are identical, and only differ in their annotation style. How you want to [annotate your metadata](https://blacksmithgu.github.io/obsidian-dataview/annotation/add-metadata/) is up to you and your personal preference. With this file, you'd have the **metadata field** `author` available and everything Dataview provides you [automatically as implicit fields](https://blacksmithgu.github.io/obsidian-dataview/annotation/metadata-pages/), like the tag or note title.

Data needs to be indexed

In the above example, you _do not_ have the poem itself available in Dataview: It is a paragraph, not a metadata field and not something Dataview indexes automatically. It is not part of Dataviews index, so you won't be able to query it.

### Data Querying

You can access **indexed data** with the help of **Queries**.

There are **three different ways** you can write a Query: With help of the [Dataview Query Language](https://blacksmithgu.github.io/obsidian-dataview/queries/dql-js-inline/#dataview-query-language-dql), as an [inline statement](https://blacksmithgu.github.io/obsidian-dataview/queries/dql-js-inline/#inline-dql) or in the most flexible but most complex way: as a [Javascript Query](https://blacksmithgu.github.io/obsidian-dataview/queries/dql-js-inline/#dataview-js).

The **Dataview Query Language** (**DQL**) gives you a broad and powerful toolbelt to query, display and operate on your data. An [**inline query**](https://blacksmithgu.github.io/obsidian-dataview/queries/dql-js-inline/#inline-dql) gives you the possibility to display exactly one indexed value anywhere in your note. You can also do calculations this way. With **DQL** at your hands, you'll be probably fine without any Javascript through your data journey.

A DQL Query consists of several parts:

- Exactly one [**Query Type**](https://blacksmithgu.github.io/obsidian-dataview/queries/query-types/) that determines what your Query Output looks like
- None or one [**FROM statement**](https://blacksmithgu.github.io/obsidian-dataview/queries/data-commands/#from) to pick a specific tag or folder (or another [source](https://blacksmithgu.github.io/obsidian-dataview/reference/sources/)) to look at
- None to multiple [**other Data Commands**](https://blacksmithgu.github.io/obsidian-dataview/queries/data-commands/) that help you filter, group and sort your wanted output

For example, a Query can look like this:

` ```dataview LIST ``` `

which list all files in your vault.

Everything but the Query Type is optional

The only thing you need for a valid DQL Query is the Query Type (and on [CALENDAR](https://blacksmithgu.github.io/obsidian-dataview/queries/query-types/#calendar)s, a date field.)

A more restricted Query might look like this:

` ```dataview LIST FROM #poems WHERE author = "Edgar Allan Poe" ``` `

which lists all files in your vault that have the tag `#poems` and a [field](https://blacksmithgu.github.io/obsidian-dataview/annotation/add-metadata/) named `author` with the value `Edgar Allan Poe`. This query would find our example page from above.

`LIST` is only one out of four [Query Types](https://blacksmithgu.github.io/obsidian-dataview/queries/query-types/) you can use. For example, with a `TABLE`, we could add some more information to our output:

` ```dataview TABLE author, published, file.inlinks AS "Mentions" FROM #poems ``` `

This'll give you back a result like:

|File (3)|author|published|Mentions|
|---|---|---|---|
|The Bells|Edgar Allan Poe|1849||
|The New Colossus|Emma Lazarus|1883|- [[Favorite Poems]]|
|The Raven|Edgar Allan Poe|1845|- [[Favorite Poems]]|

That's not where the capabilities of dataview end, though. You can also **operate on your data** with help of [**functions**](https://blacksmithgu.github.io/obsidian-dataview/reference/functions/). Mind that these operations are only made inside your query - your **data in your files stays untouched**.

` ```dataview TABLE author, date(now).year - published AS "Age in Yrs", length(file.inlinks) AS "Counts of Mentions" FROM #poems ``` `

gives you back

|File (3)|author|Age in Yrs|Count of Mentions|
|---|---|---|---|
|The Bells|Edgar Allan Poe|173|0|
|The New Colossus|Emma Lazarus|139|1|
|The Raven|Edgar Allan Poe|177|1|

Find more examples [here](https://blacksmithgu.github.io/obsidian-dataview/resources/examples/).

As you can see, dataview doesn't only allow you to aggregate your data swiftly and always up to date, it also can help you with operations to give you new insights on your dataset. Browse through the documentation to find out more on how to interact with your data.

Have fun exploring your vault in new ways!

## Resources and Help

This documentation is not the only place that can help you out on your data journey. Take a look at [Resources and Support](https://blacksmithgu.github.io/obsidian-dataview/resources/resources-and-support/) for a list of helpful pages and videos.

## Structure of a Query

Dataview offers [multiple ways](https://blacksmithgu.github.io/obsidian-dataview/queries/dql-js-inline/) to write queries and the syntax differs for each.

This page provides information on how to write a **Dataview Query Language** (**DQL**) query. If you're interested in how to write Inline Queries, refer to the [inline section on DQL, JS and Inlines](https://blacksmithgu.github.io/obsidian-dataview/queries/dql-js-inline/#inline-dql). You'll find more information about **Javascript Queries** on the [Javascript Reference](https://blacksmithgu.github.io/obsidian-dataview/api/intro/).

**DQL** is a SQL like query language for creating different views or calculations on your data. It supports:

- Choosing an **output format** of your output (the [Query Type](https://blacksmithgu.github.io/obsidian-dataview/queries/query-types/))
- Fetch pages **from a certain [source](https://blacksmithgu.github.io/obsidian-dataview/reference/sources/)**, i.e. a tag, folder or link
- **Filtering pages/data** by simple operations on fields, like comparison, existence checks, and so on
- **Transforming fields** for displaying, i.e. with calculations or splitting up multi-value fields
- **Sorting** results based on fields
- **Grouping** results based on fields
- **Limiting** your result count

Warning

If you are familiar with SQL, please read [Differences to SQL](https://blacksmithgu.github.io/obsidian-dataview/queries/differences-to-sql/) to avoid confusing DQL with SQL.

Let's have a look at how we can put DQL to use.

## General Format of a DQL Query

Every query follows the same structure and consists of

- exactly one **Query Type** with zero, one or many [fields](https://blacksmithgu.github.io/obsidian-dataview/annotation/add-metadata/), depending on query type
- zero or one **FROM** data commands with one to many [sources](https://blacksmithgu.github.io/obsidian-dataview/reference/sources/)
- zero to many other **data commands** with one to many [expressions](https://blacksmithgu.github.io/obsidian-dataview/reference/expressions/) and/or other infos depending on the data command

At a high level, a query conforms to the following pattern:

` ```dataview <QUERY-TYPE> <fields> FROM <source> <DATA-COMMAND> <expression> <DATA-COMMAND> <expression>           ... ``` `

Only the Query Type is mandatory.

The following sections will explain the theory in further detail.

## Choose a Output Format

The output format of a query is determined by its **Query Type**. There are four available:

1. **TABLE**: A table of results with one row per result and one or many columns of field data.
2. **LIST**: A bullet point list of pages which match the query. You can output one field for each page alongside their file links.
3. **TASK**: An interactive task list of tasks that match the given query.
4. **CALENDAR**: A calendar view displaying each hit via a dot on its referred date.

The Query Type is the **only mandatory command in a query**. Everything else is optional.

Possibly memory intense examples

Depending on the size of your vault, executing the following examples can take long and even freeze Obsidian in extreme cases. It's recommended that you specify a `FROM` to restrict the query execution to a specific subset of your vaults' files. See next section.

` Lists all pages in your vault as a bullet point list ```dataview LIST ```  Lists all tasks (completed or not) in your vault ```dataview TASK ```  Renders a Calendar view where each page is represented as a dot on its creation date. ```dataview CALENDAR file.cday ```  Shows a table with all pages of your vault, their field value of due, the files' tags and an average of the values of multi-value field working-hours ```dataview TABLE due, file.tags AS "tags", average(working-hours) ``` `

Read more about the available Query Types and how to use them [here](https://blacksmithgu.github.io/obsidian-dataview/queries/query-types/).

## Choose your source

Additionally to the Query Types, you have several **Data Commands** available that help you restrict, refine, sort or group your query. One of these query commands is the **FROM** statement. `FROM` takes a [source](https://blacksmithgu.github.io/obsidian-dataview/reference/sources/) or a combination of [sources](https://blacksmithgu.github.io/obsidian-dataview/reference/sources/) as an argument and restricts the query to a set of pages that match your source.

It behaves differently from the other Data Commands: You can add **zero or one** `FROM` data command to your query, right after your Query Type. You cannot add multiple FROM statements and you cannot add it after other Data Commands.

` Lists all pages inside the folder Books and its sub folders ```dataview LIST FROM "Books" ```  Lists all pages that include the tag #status/open or #status/wip ```dataview LIST FROM #status/open OR #status/wip ```  Lists all pages that have either the tag #assignment and are inside folder "30 School" (or its sub folders), or are inside folder "30 School/32 Homeworks" and are linked on the page School Dashboard Current To Dos ```dataview LIST FROM (#assignment AND "30 School") OR ("30 School/32 Homeworks" AND outgoing([[School Dashboard Current To Dos]])) ``` `

Read more about `FROM` [here](https://blacksmithgu.github.io/obsidian-dataview/queries/data-commands/#from).

## Filter, sort, group or limit results

In addition to the Query Types and the **Data command** `FROM` that's explained above, you have several other **Data Commands** available that help you restrict, refine, sort or group your query results.

All data commands except the `FROM` command can be used **multiple times in any order** (as long as they come after the Query Type and `FROM`, if `FROM` is used at all). They'll be executed in the order they are written.

Available are:

1. **FROM** like explained [above](https://blacksmithgu.github.io/obsidian-dataview/queries/structure/#choose-your-source).
2. **WHERE**: Filter notes based on information **inside** notes, the meta data fields.
3. **SORT**: Sorts your results depending on a field and a direction.
4. **GROUP BY**: Bundles up several results into one result row per group.
5. **LIMIT**: Limits the result count of your query to the given number.
6. **FLATTEN**: Splits up one result into multiple results based on a field or calculation.

`` Lists all pages that have a metadata field `due` and where `due` is before today ```dataview LIST WHERE due AND due < date(today) ```  Lists the 10 most recently created pages in your vault that have the tag #status/open ```dataview LIST FROM #status/open SORT file.ctime DESC LIMIT 10 ```  Lists the 10 oldest and incomplete tasks of your vault as an interactive task list, grouped by their containing file and sorted from oldest to newest file. ```dataview TASK WHERE !completed SORT created ASC LIMIT 10 GROUP BY file.link SORT rows.file.ctime ASC ``` ``

Find out more about available [data commands](https://blacksmithgu.github.io/obsidian-dataview/queries/data-commands/).

## Examples

Following are some example queries. Find more examples [here](https://blacksmithgu.github.io/obsidian-dataview/resources/examples/).

` ```dataview TASK ``` `

` ```dataview TABLE recipe-type AS "type", portions, length FROM #recipes ``` `

` ```dataview LIST FROM #assignments WHERE status = "open" ``` `

` ```dataview TABLE file.ctime, appointment.type, appointment.time, follow-ups FROM "30 Protocols/32 Management" WHERE follow-ups SORT appointment.time ``` `

` ```dataview TABLE L.text AS "My lists" FROM "dailys" FLATTEN file.lists AS L WHERE contains(L.author, "Surname") ``` `

` ```dataview LIST rows.c WHERE typeof(contacts) = "array" AND contains(contacts, [[Mr. L]]) SORT length(contacts) FLATTEN contacts as c SORT link(c).age ASC ``` `