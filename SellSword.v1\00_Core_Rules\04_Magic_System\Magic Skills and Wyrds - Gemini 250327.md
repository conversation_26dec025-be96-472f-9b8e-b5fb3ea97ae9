Okay, here is a primer for the magic system in Sellsword, focusing on the interplay between the magic Category Skills and Wyrd Lore, along with a list of the Wyrds.

---

**Sellsword Magic Primer: Skills & Wyrd Lore**

This primer outlines how magic functions in Sells<PERSON>. Instead of specific spells, magic is wielded through broad **Category Skills** representing fundamental methods of magical manipulation. Specialization comes from **Wyrd Lore**, representing deep knowledge and affinity for specific domains of power.

**1. Magic Category Skills (Tier 1 - "DO")**

These skills represent the trained ability to interact with magical energies using different methodologies. They are purchased with Skill Points (SP) and have escalating costs per level, like other Category Skills. They primarily grant +Dice per level to relevant magic-related rolls.

- **Evoke:** (Enhances `GRT = RES + PRE` for magic)
    - _Function:_ Bringing forth raw magical effects, manifesting energy, or channeling power directly into the world. Used for overt, powerful displays (e.g., creating flames, gusts of wind, bursts of life/decay).
- **Weave:** (Enhances `FCS = AWA + RES` for magic)
    - _Function:_ Controlling, shaping, maintaining, or subtly manipulating existing magical energies or effects. Used for finesse, duration, or complex patterns (e.g., shaping evoked fire into a shield, maintaining a light source, crafting illusions).
- **Scribe:** (Enhances `PRC = DEX + AWA` for magic)
    - _Function:_ Imbuing objects with magical power, writing potent runes or symbols, creating enchanted items or wards. Used for careful, precise work.
- **Scry:** (Enhances `INS = PRE + INT` for magic)
    - _Function:_ Using magic to gather information, perceive distant events or hidden truths, divine the future, or sense magical energies. Used for detection and divination.

**2. Wyrd Lore (The Source - "KNOW")**

Wyrds are not skills themselves but specific domains or essences of magical power. Knowledge and affinity for these domains are represented by **Wyrd Lore**.

- **Acquisition:** Wyrd Lore entries (e.g., "Eldr Lore," "Hugr Lore") are learned like other Lore, filling available `Knowledge/Lore Slots` (based on `3 + INT*2`). They are acquired through study, mentorship, or discovery, often during Downtime.
- **Slot Cost:** Each Wyrd Lore has a Slot Cost (1-3) reflecting the depth of understanding or power associated with it.
- **Function:** Wyrd Lore **augments** the Magic Category Skills when performing actions related to that specific Wyrd's domain. It provides benefits such as:
    - **Bonus Dice:** Add dice (+1 to +3, likely scaling with Slot Cost) to the magic skill roll.
    - **Negate Difficulty:** Reduce or negate Difficulty dice related to unfavorable circumstances for using that Wyrd.
    - **Reduce Complexity (Potentially):** High-cost Wyrd Lore (Cost 3) might reduce the extra successes needed for highly complex effects within its domain.

**3. Interplay Example**

A character wants to Evoke a barrier of stone to block an incoming charge. This uses the `Evoke` Category Skill (and the underlying `GRT` Derived Action).

- **Base Pool:** `GRT Score` + `Evoke Skill Dice`
- **Wyrd Lore:** If the character has `Jörð Lore (Earth)` with Slot Cost 2, they might add +2 bonus dice to the pool because they are manipulating earth.
- **Other Factors:** Add Gear bonuses (e.g., focus item?), wager Will (+ dice), subtract Difficulty dice (e.g., if the ground is frozen).
- **Final Pool:** `GRT Score` + `Evoke Dice` + `Jörð Lore Dice` + `Wager Dice` + `Gear Dice` - `Difficulty Dice`.
- **Resolution:** Roll the pool. One success creates a basic barrier. More successes might make it larger or sturdier (Stunts), or might be required if the GM deemed creating a _large_ barrier quickly to be Complex.

**4. List of Wyrds (as Lore Domains)**

This list outlines the fundamental domains of magic characters can gain Lore in, adapted from the `Magic (Wyrds).md` document. The specific effects achieved depend on the Magic Category Skill used (Evoke, Weave, Scribe, Scry) in conjunction with the relevant Wyrd Lore.

- **Physical Wyrds** (Manipulation of the Natural World)
    
    - **Eldr Lore:** Knowledge of fire, heat, destruction, and rapid change.
    - **Vatn Lore:** Knowledge of water, flow, fluidity, adaptation, and cleansing.
    - **Jörð Lore:** Knowledge of earth, stone, stability, endurance, and gravity.
    - **Loftr Lore:** Knowledge of air, wind, movement, sound, and freedom.
    - **Ljós Lore:** Knowledge of light, illumination, vision, perception, and shadow manipulation.
- **Mental Wyrds** (Influencing Thought, Memory, and Perception)
    
    - **Hugr Lore:** Knowledge of emotion, empathy, desire, passion, and influencing moods.
    - **Rök Lore:** Knowledge of logic, reason, clarity, structure, and enhancing or suppressing rational thought.
    - **Draumr Lore:** Knowledge of dreams, the subconscious, illusions, hallucinations, and manipulating perceptions.
    - **Vísa Lore:** Knowledge of truth, foresight, probability, revealing lies, and glimpses of potential futures.
    - **Ekkó Lore:** Knowledge of aetheric echoes, sensing past events or emotional resonance lingering on places or objects.
- **Spiritual Wyrds** (Forces of Life, Death, and the Soul)
    
    - **Lífi Lore:** Knowledge of life, vitality, growth, healing, and renewal.
    - **Dauði Lore:** Knowledge of death, decay, entropy, ending, and manipulating life force negatively.
    - **Önd Lore:** Knowledge of the soul, spirits (both living and dead), communion, and summoning or binding spiritual entities.
    - **Skuggi Lore:** Knowledge of shadows, darkness, concealment, hidden paths, and potentially manipulating fear associated with the unseen.
    - **Æther Lore:** Knowledge of raw magical energy itself, the 'Veil', sensing magic, counter-magic, stabilization, or disruption of magical effects.

---

This framework integrates magic into your system using the established Skill and Lore mechanics, providing a foundation for spellcasting based on broad methods enhanced by specific knowledge domains.