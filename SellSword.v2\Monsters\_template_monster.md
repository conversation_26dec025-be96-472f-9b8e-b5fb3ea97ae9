---
name: # Monster Name
description_short: # Brief one-line description
description_long: # Monster Descriptions for GMs:
# Effective monster descriptions evoke atmosphere, mood, and thematic identity.
# Use vivid, sensory language to paint a picture of the monster's appearance, behavior, and presence.
# Highlight unique or unsettling features that make the monster memorable and distinct.
# Convey the monster's role and tactics through descriptive cues (e.g., "lurks in shadows," "moves with unnatural grace," "hunts in packs").
# Balance detail with brevity; provide enough to inspire but leave room for GM improvisation.
# Consider the monster's environment and lore to enrich the description.
# Example: The Huldrekall is described as a gaunt, silent aberration haunting high, dark places, with spider-like limbs and a skin that absorbs light, evoking dread and mystery.
# Use the Goblin Kinder description as a model for small, cunning foes: emphasizing their malice, numbers, and stealthy menace.
# Encourage GMs to adapt descriptions to their campaign tone and player group.
size: # e.g., Tiny, Small, Medium, Large, Huge, Gargantuan
type: # e.g., Beast, Humanoid, Fae, Undead, Construct, Elemental, Aberration
tags: [monster] # Add type tag, e.g., [monster, beast]

# Core Combat Stats
combat_pool: # Base dice pool for most attacks/combat actions (e.g., 4d6)
action_points: # AP per round (e.g., 3, Player Characters typically have 4)
speed: # Movement speed in hexes/units per AP (e.g., 4, or 4 (Climb 4))
wounds: # Wound Threshold pools (e.g., { minor: 2, major: 1 })
#  minor: # Number of Minor wounds (from 1 pen damage)
#  major: # Number of Major wounds (from 2 pen damage)
#  grievous: # Number of Grievous wounds (from 3 pen damage)
#  deadly: # Number of Deadly wounds (from 4+ pen damage)

# Defense
target_difficulty: # Passive defense. Adds Difficulty Dice to attackers (e.g., "TD 1 [Adds 1 Difficulty Die]"). Traits can modify this or add success thresholds.
dr:
  body: # DR for Body hits (e.g., 1)
  voids: # DR for Voids hits (if different, e.g., 0)
  # Add specific resistances/vulnerabilities if applicable (e.g., resist_fire: true, vulnerable_silver: true)

# Actions
# List all actions the monster can perform that cost Action Points (AP).
# This includes standard attacks and special maneuvers previously under Traits.
actions: # Renamed from 'attacks' for clarity
  - name: # e.g., Bite, Claw, Rusty Sword, Dirty Trick
    ap_cost: # AP cost for this action (default 1 if not specified, e.g., 1, 2)
    pool_mod: # Modifier to combat_pool for this action (e.g., +1 die, -1 die, default 0)
    reach: # Melee reach (e.g., 1) or range increments (e.g., "PRC x2 / x4 / x8")
    damage: # Base damage value (optional, default 0)
    pierce: # Pierce value (optional, default 0)
    force: # Force value (optional, default 0)
    notes: # Description of the action and any special effects (e.g., "Opposed check vs Target FIN/PHY. On success: Target suffers [[Put_Off_Balance]].")
    extra_success_effects: # Optional list of effects purchasable with extra successes
#      - cost: 1 # Cost in extra successes
#        effect: "Deal +1 damage (if Pierce >= DR)."
#      - cost: 1
#        effect: "Inflict Bleeding 1."
#      - cost: 2
#        effect: "Knock target prone."
  # Add more actions as needed

# Unique Abilities
# These are passive abilities or effects that do not cost AP to use.
monstrous_traits:
  - name: # Trait Name (e.g., Unnatural Strength, Phase Shift, Swarm Tactics, Iron Aversion)
    description: # Mechanical effect. Be specific. (e.g., "Always gains Upper Hand when attacking flanked targets.", "Immune to physical damage while incorporeal.", "+1 Combat Pool die for each allied [Monster Type] within 2 hexes.")
  # Add more traits as needed


# Behavior & Other
tactics: # Brief description of typical combat behavior and strategy
loot: # Potential items dropped on defeat (e.g., "1d3 Goblin Ears", "Fae Dust (Utility Item)")
environment: # Typical habitats or locations (e.g., "Dark forests, underground caves")
# Optional: Add simple attributes if needed for rare non-combat checks (GM discretion)
# attributes:
#   INT: 0
#   AWA: 1
---
