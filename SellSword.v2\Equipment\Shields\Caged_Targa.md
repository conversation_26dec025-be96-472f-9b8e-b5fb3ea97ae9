---
name: Caged Targa
type: Shield
category: Small Shield
# Core Stats (Raw values from CSV, may need conversion/interpretation)
carry: null # Data missing in CSV
str_req: null # Data missing in CSV
durability: null # Data missing in CSV
# Weapon/Shield Specific
offense: null # N/A
damage: null # N/A
pierce: null # N/A
force: null # N/A
block: null # Data missing in CSV
parry: null # Data missing in CSV
hook: null # N/A
reach: null # Data missing in CSV
range: null # N/A
# Shield Specific
cover: null # Data missing in CSV
# Armor Specific
body_av: null # N/A
voids_av: null # N/A
# General
stunts: [] # None listed
notes: Small arm pavise with steel straps meant to 'cage' the opponents blade to disarm or move them
references: Weird Weapons: Caged Buckler! - Sword trapper.
tags: [equipment, shield, small_shield]
---
# Caged Targa

(Flavor text, detailed description, usage notes go here)
