## Prime Attributes
These are the primary bodily characteristics that comprise your characters capabilities. These are largely set at character creation and can be improved upon on level up but at a slower rate. The reasoning being a level 1 Sellsword will likely already be close to peak physical shape based on their ancestry and background to be representative of whatever archetype they are playing.

These will be ranked on a scale negative to positive with 0 being completely average. I am still trying to determine what this range will be but I am currently working with the idea of -3 to +3 to represent the normal distribution. Meaning the strongest 'normal' people in the world would have a STR of +3. This leaves room for extraordinary or supernatural beings to surpass that.

### Physical Attributes - Point buy budget of 2
 - Endurance - Capacity for physical activity
 - Strength - prowess
 - Agility - speed/coordination
 - Dexterity - fine motor control
### Mental Attributes - Point buy budget of 2
 - Concentration - Capacity for mental activity
 - Intellect - mental prowess
 - Acuity - mental speed and awareness
 - Presence - Charisma and emotional intelligence

## Derived Attributes
 - Speed - hexes you can move spending 1 AD (3 + AGI * 2)
 - Carry - Weight you can carry without being burdened (3 + STR * 2)
 - Stamina - resource for boosting physical actions, physical attacks degrade this. Typically refreshes after combat to full (3 + FRT * 2 - Weight)
 - Will - resource for boosting mental actions (spells), mental attacks degrade this. Only refreshes under special circumstances (3 + RES * 2 - Strain)

## Derived Skills
These are the skill modifiers that are derived from your prime physical characteristics and represent your training in the use of your body and mind to complete complex tasks. These are the modifiers used in combat, exploration, etc.

The goal here is to anchor combat and exploration around these 8 skills and funnel all actions to one of them to make arbitration by the Game Master as streamlined as possible.

### Main
 - FRT - Fortitude(END * 2 + STR) - Resist a poison, hold a door closed (HODOR)
 - PHY - Physique (STR * 2 + AGI) - Climb a wall, Jump a ravine, Throw a rock hard and far
 - FIN - Finesse (AGI * 2 + DEX) - Dive through a window, Balance on a beam, Throw a rock fast and accurate
 - PRC - Precision (DEX * 2 + ACU) - Pick a lock, disarm a trap, Distance at which you can throw a rock accurately
 - RES - Resolve (CON * 2 + PRE) - Maintain a spell, resist a fear
 - RSN - Reason (INT * 2 + ACU) - Solve a puzzle, discern meaning from a clue
 - FCS - Focus (ACU * 2 + CON) - Find a trap or clue, hear an assassin behind you
 - INS - Insight (PRE * 2 + INT) - Read a room, tell if someone's lying

At the moment the idea is to keep 'skills' limited in variety but wide in scope, and give players variability in play by providing circumstantial bonuses. There will be other 'skills' granted by background or archetype that the player will either be able to pick from a pool or make up their own.