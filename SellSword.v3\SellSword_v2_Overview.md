# Sellsword TTRPG: Concept Overview (v2 Structure)

This document provides a high-level overview of the Sellsword TTRPG project, organized within this v2 structure.

## **I. Core Design Goals:**

* **High Verisimilitude:** Create a grounded, believable world and mechanics, especially regarding combat and character capabilities.
* **Equipment Matters:** Emphasize the importance of gear and using the "right tool for the job" for success.
* **Manageable Crunch:** Offer depth and tactical choices without overly complex or slow rules.
* **G<PERSON>ty <PERSON>ne:** Reflect the challenging life of a sellsword, where resources are managed carefully and danger is real. Exploration, especially in darkness, is inherently dangerous.

## **II. Core Mechanic: d6 Dice Pool**

* **System:** Based on rolling pools of six-sided dice (d6s).
* **Building the Pool:** `(Relevant Derived Action Score) + Skill Dice + Gear Dice + Resource Dice - Difficulty Dice`
* **Rolling & Success:** Roll the pool. Each '6' rolled counts as one success. The number of successes typically determines the outcome or degree of success. Extra successes beyond the minimum required can be spent on [[Stunts]].
* **Wager/Push Mechanic:** Players can spend **Stamina** (physical) or **Will** (mental) to add dice _before rolling_. Rolling '1's carries risk (details TBD).
* **Difficulty:** Represented by dice subtracted from the pool before rolling.

## **III. Character Structure:**

* **Attributes:** 8 Prime Attributes (STR, AGI, DEX, END, INT, AWA, CON, PRE) rated -3 to +3 (or higher for supernatural). See [[_Attributes_README]].
* **Derived Stats:** Calculated resources/capabilities (Speed, Carry, Stamina, Will, Standing, Lore Slots). See [[_Attributes_README]].
* **Derived Actions:** 7 core actions (FRT, PHY, FIN, PRC, FCS, RES, INS) derived from attribute pairs, forming the base dice pool for most rolls. See [[_Attributes_README]].
* **Skills:** Tiered system (Category, Style, Focus) representing trained abilities. Add dice or grant special maneuvers/benefits. See [[_Features_README]].
* **Features:** Unique character abilities, often tiered, providing specific mechanical advantages or resource interactions. See [[_Features_README]].
* **Standing:** Represents social capital, connections, favors. Used mainly in downtime. See [[_Standing_README]].
* **Lore:** Represents specific knowledge domains (including Wyrd Lore for magic). Uses Lore Slots. See [[_Lore_README]].

## **IV. Equipment & Combat:**

* **Gear Matters:** Equipment provides crucial bonuses (dice, damage reduction, special effects). See [[_Equipment_README]].
* **Combat:** Designed to be lethal, using a Wound System instead of Hit Points. Armor provides Damage Reduction. Attacks target Body or Voids. _(Note: The Wound System structure and files were reviewed and updated to a 4-tier system in April 2025)_. See [[_Rules_README]] (placeholder) and [[_Wounds_README]].

## **V. Magic System:**

* **Source:** Manipulation of the Aether through the Veil separating it from the Firmament (physical world). See [[World/_World_README]] (placeholder) and [[_Lore_README]].
* **Method:** Uses 4 Magic Skills (`Evoke`, `Weave`, `Scribe`, `Scry`) augmented by specific `Wyrd Lore`. See [[_Lore_README]].

## **VI. Other Systems:**

* **Stunts:** Spend extra successes for minor benefits. See [[_Stunts_README]].
* **Exploration:** Emphasizes danger, resource management (e.g., Torches). Travel uses abstract distances and event triggers. See [[_Rules_README]] (placeholder).
* **Monsters:** Use player-like stats but focus on unique, rule-bending traits. See [[_Monsters_README]].

## **VII. Key Principles:**

* **Structure:** Individual files for distinct game elements (Attributes, Features, Equipment, Stunts, Lore, Standing, Monsters) using YAML frontmatter for structured data, enabling linking and potential programmatic access (e.g., via Obsidian Dataview).
* **Consistency:** Aim for clear definitions and consistent application of mechanics.
* **Tone:** Maintain a gritty, realistic feel where choices and resources matter.
