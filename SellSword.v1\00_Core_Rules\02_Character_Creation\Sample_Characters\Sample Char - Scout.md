Okay, let's create a Level 1 character using the Sellsword system we've outlined. This process will also help highlight potential areas for refinement.

**Character Concept:** <PERSON><PERSON><PERSON>, a pragmatic and nimble scout, more comfortable in the wilds or shadows than in open combat or conversation. Focuses on ranged attacks, stealth, and awareness.

**1. Attributes (8-Attribute System: INT, AWA, RES, PRE)**

- **Point Buy Method:** Broad (5 points available, max starting attribute +2). Allows negatives.
- **Allocation (5 points used):**
    - STR: 0 (Average strength)
    - AGI: +2 (Very quick and mobile)
    - DEX: +1 (Good hand-eye coordination)
    - END: 0 (Average resilience)
    - INT: 0 (Average intellect, relies more on observation)
    - AWA: +2 (Highly perceptive and alert)
    - RES: 0 (Average willpower)
    - PRE: 0 (Neither charismatic nor imposing)
- _Rationale:_ High Agility and Awareness fit the scout concept. Good Dexterity helps with ranged attacks and fine tasks. Average elsewhere reflects a focus on core competencies.

**2. Derived Stats**

*Calculated using base 3, proposed INT/AWA/RES/PRE attributes, and assuming _2 multiplier for END/INT derived stats, _1 for RES derived stat for now.__

- **Speed:** `3 + AGI` = 3 + 2 = **5**
- **Carry:** `3 + STR` = 3 + 0 = **3**
- **Stamina:** `3 + END*2` = 3 + (0*2) = **3**
- **Will:** `3 + RES*1` = 3 + (0*1) = **3**
- **Influence Slots:** `3 + PRE` = 3 + 0 = **3**
- **Knowledge/Lore Slots:** `3 + INT*2` = 3 + (0*2) = **3**

**3. Derived Actions**

_Calculated using `Action Score = 2 + Att1 + Att2`, incorporating the GRT action name and the `*2` multiplier directly in FRT and INS formulas._

- **FRT (Fortitude):** `2 + (END*2) + STR` = 2 + (0*2) + 0 = **2**
- **PHY (Physique):** `2 + STR + AGI` = 2 + 0 + 2 = **4**
- **FIN (Finesse):** `2 + AGI + DEX` = 2 + 2 + 1 = **5**
- **PRC (Precision):** `2 + DEX + AWA` = 2 + 1 + 2 = **5**
- **FCS (Focus):** `2 + AWA + RES` = 2 + 2 + 0 = **4**
- **GRT (Grit):** `2 + RES + PRE` = 2 + 0 + 0 = **2**
- **INS (Insight):** `2 + PRE + (INT*2)` = 2 + 0 + (0*2) = **2**

**4. Skills (3-Tier System)**

- **Starting Skill Points (SP):** Let's assume **5 SP** at Level 1.
- **Skill Cost:** Escalating: Level 1 = 2 SP, Level 2 = 3 SP, Level 3 = 4 SP.
- **Selections (4 SP Spent):**
    - `Ranged Combat (Category) - Level 1` (Cost 2 SP): Grants +1 die to Ranged Combat rolls (PRC/FIN). Prerequisite for Bows/Marksman etc.
    - `Stealth (Category) - Level 1` (Cost 2 SP): Grants +1 die to Stealth rolls (FIN/PRC). Prerequisite for Infiltrator etc.
- **Remaining SP:** 1 SP (saved for Level 2).
- _Rationale:_ Covers the core scout/skirmisher functions: shooting and sneaking.

**5. Lore**

- **Lore Slots:** 3 (from INT 0).
- **Selections (3 Slots Used):**
    - `Woodcraft Lore (1)` (Cost 1 Slot): Knowledge of forests, plants, animals, tracking signs. Augments Survival/Awareness rolls in forests.
    - `Bestiary Lore (1)` (Cost 1 Slot): Knowledge of common creatures, their habits, and basic weaknesses. Augments combat rolls situationally.
    - `Local Area Lore [Specify Region] (1)` (Cost 1 Slot): Knowledge of the starting region's geography, settlements, dangers. Augments navigation/social rolls locally.
- _Rationale:_ Provides thematic knowledge fitting a scout who operates in the wilds and needs practical knowledge.

**6. Influence (Optional)**

- **Slots:** 3 (from PRE 0).
- **Selection:** Could take `Faction Ties (Scouts Guild) (1)` or leave blank to develop through play.

**7. Gear (Conceptual)**

- Shortbow (Gear dice +2? Range? AP cost?)
- Quiver & Arrows
- Dagger (Gear dice +1?)
- Leather Armor (AV Body 1 / Voids 1?)
- Scout's Kit (Rope, basic tools, maybe grants +1 Gear die to Survival/Stealth situationally?)

**Level 1 Character Summary: Kaelen the Scout**

- **Attributes:** STR 0, AGI +2, DEX +1, END 0, INT 0, AWA +2, RES 0, PRE 0
- **Derived Stats:** Speed 5, Carry 3, Stamina 3, Will 3, Influence Slots 3, Lore Slots 3
- **Derived Actions:** FRT 2, PHY 4, FIN 5, PRC 5, FCS 4, GRT 2, INS 2
- **Skills:** Ranged Combat 1, Stealth 1 (1 SP saved)
- **Lore:** Woodcraft (1), Bestiary (1), Local Area (1)
- **Gear:** Bow, Dagger, Leather Armor, Scout's Kit

**Brainstorming Level Progression:**

- **SP Gain:** Assume **+3 SP per Level**.
- **Attribute Gain:** Assume **+1 Attribute Point every 4 Levels**.
- **Level 2:** (Total SP: 1+3=4)
    - Buy `Survival (Category) - Lvl 1` (Cost 2 SP) -> +1 die Survival rolls.
    - Buy `Focus: Bows (Focus) - Lvl 1` (Cost 2 SP) -> +1 die when using Bows.
    - _Result:_ Broader utility (Survival), weapon specialization starts.
- **Level 3:** (Total SP: 0+3=3)
    - Buy `Ranged Combat - Lvl 2` (Cost 3 SP) -> Total +2 dice Ranged Combat.
    - _Result:_ Increases core ranged effectiveness.
- **Level 4:** (Total SP: 0+3=3. Gain +1 Attribute Point).
    - Put Attribute Point into AGI (AGI +3). Recalculate Speed (+6), PHY (+5), FIN (+6).
    - Buy `Stealth - Lvl 2` (Cost 3 SP) -> Total +2 dice Stealth.
    - _Result:_ Enhanced core attributes and skills.
- **Level 5:** (Total SP: 0+3=3)
    - Buy `Style: Skirmisher - Lvl 1` (Cost 2 SP - Assuming Req. Ranged Combat 1 or Athletics 1). Grants ability X related to move/shoot.
    - 1 SP saved.
    - _Result:_ Adds stylistic combat ability.

**Potential Pitfalls & Areas for Improvement Noted:**

1. **SP Economy:** Does 5 start / +3 per level feel right? It allows steady progress but specialization takes time. Needs testing. A slightly higher start (6-7 SP?) might allow a Tier 2/3 skill from the beginning, establishing the niche earlier.
2. **Attribute Impact (END/INT):** Even with the `*2` in FRT/INS, END and INT only influence one Action roll directly. Kaelen has little incentive to raise them unless specific Skills/Talents make them compelling or the derived stats (Stamina/Lore Slots) prove extremely valuable in play. Reinforcing their use via Survival, Medicine, Crafting, Tactics skills is important.
3. **Skill Synergy/Prerequisites:** The relationship between Category -> Style/Focus needs clear definition. Is Lvl 1 Category always enough? Are there Attribute minimums for advanced skills (e.g., Skirmisher needing AGI 1+)?
4. **Complexity Application:** The GM needs clear guidelines on when to apply Complexity (extra successes) vs. just Difficulty. If Complexity is applied too often or too heavily, skills that negate it (likely Tier 2 Styles) become almost mandatory, potentially overshadowing other choices.
5. **Wyrd Lore vs Skills:** While Kaelen isn't magical, ensuring the power/utility curve of Wyrd Lore + Magic Category Skills feels comparable to mundane Skill investments is crucial for overall balance. Does INT providing Lore slots make magic users inherently more versatile than non-magic users need SP for active skills?

This character creation exercise highlights the core mechanics working together but emphasizes the need for playtesting to fine-tune the numbers (SP costs/gain, thresholds) and the practical application of Difficulty vs. Complexity.

## Reply
**Potential Pitfalls & Areas for Improvement Noted:**

1. **SP Economy:** Does 5 start / +3 per level feel right? It allows steady progress but specialization takes time. Needs testing. A slightly higher start (6-7 SP?) might allow a Tier 2/3 skill from the beginning, establishing the niche earlier.

I think sp's at level 1 should either be higher, or initial costs lower. I think in keeping with the idea that a lvl 1 sellsword is still a competent adventurer, I think it should likely be permissable to have at least 1 skill at tier 2 and still have a couple level 1's, or even choose to be broadly skilled in a few skills. I think its ok to compare to other rpg's like dnd for this.

2. **Attribute Impact (END/INT):** Even with the `*2` in FRT/INS, END and INT only influence one Action roll directly. Kaelen has little incentive to raise them unless specific Skills/Talents make them compelling or the derived stats (Stamina/Lore Slots) prove extremely valuable in play. Reinforcing their use via Survival, Medicine, Crafting, Tactics skills is important.

Yea i am also still not sold on this. I do think Stamina and will should prove to be very valuable resources when used in play. I imagine a high END fighter could likely over perform a high skilled one at least at first. I also think in order for this to work, interesting skills are required that allow players to leverage these resources in interesting ways. Along with interesting Lores that are useful for combat, like tactics or something like that.

3. **Skill Synergy/Prerequisites:** The relationship between Category -> Style/Focus needs clear definition. Is Lvl 1 Category always enough? Are there Attribute minimums for advanced skills (e.g., Skirmisher needing AGI 1+)?

I think it makes a lot of sense to have varied pre reqs for skills. Rather than be too dogmatic about consistency, prerequisites should follow common sense. A skirmishing skill that enables better movement during combat makes sense to have an AGI requirement.

4. **Complexity Application:** The GM needs clear guidelines on when to apply Complexity (extra successes) vs. just Difficulty. If Complexity is applied too often or too heavily, skills that negate it (likely Tier 2 Styles) become almost mandatory, potentially overshadowing other choices.

I think showing complexity as a hurdle to players will encourage them towards those tier 2 skills yes. But this can be good. If accomplishing complex tasks leads to more interesting and dynamic play it will be done more. I think the issue will be if it becomes 'necessary' to do complex things in order to feel like your playing your character well. Min maxing too much. Dont want players to feel like if they aren't doing some special maneuver each round they are not fulfilling their character identity.

5. **Wyrd Lore vs Skills:** While Kaelen isn't magical, ensuring the power/utility curve of Wyrd Lore + Magic Category Skills feels comparable to mundane Skill investments is crucial for overall balance. Does INT providing Lore slots make magic users inherently more versatile than non-magic users need SP for active skills?

I'm not sure i understand this question.