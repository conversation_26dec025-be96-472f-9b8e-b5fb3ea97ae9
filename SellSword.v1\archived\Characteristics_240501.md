## Prime Attributes
These are the primary bodily characteristics that comprise your characters capabilities. These are largely set at character creation and can be improved upon on level up but at a slower rate. The reasoning being a level 1 Sellsword will likely already be as close to peak physical shape as possible based on their heritage and background to be representative of whatever archetype they are playing.

These will be ranked on a scale negative to positive with 0 being completely average. I am still trying to determine what this range will be but I am currently working with the idea of -3 to +3 to represent the normal distribution. Meaning the strongest 'normal' people in the world would have a STR of +3. This leaves room for extraordinary or supernatural beings to surpass that.

This also means that increases or decreases to these primary attributes will come with outsized implications to player outcomes

Possible distribution with budget of 5 and max of 3 in any attribute.
+3, +2, +1, 0, 0, -1
### Physical Attributes
 - Strength - Physical prowess, +2 PHY +1 FRT - TOTAL: 3
 - Agility - Physical speed, +2 FIN +1 PHY - TOTAL: 3
 - Dexterity - Physical control, +2 PRC +1 FIN - TOTAL: 3
### Mental Attributes
 - Intellect - Mental prowess, +2 RSN +1 INS +1 RES - TOTAL: 4
 - Acuity - Mental speed and awareness, +2 FCS +1 PRC +1 RSN - TOTAL: 4
 - Presence - Mental control and emotional intelligence, +2 INS +1 FRT +1 RES - TOTAL: 4

## Derived Attributes
 - Move - hexes you can move spending 1 AD (3 + AGI * 2)
 - Carry - Weight you can carry without being burdened (3 + STR * 2)

## Derived Skills
These are the skill modifiers that are derived from your prime physical characteristics and represent your training in the use of your body and mind to complete complex tasks. These are the modifiers used in combat, exploration, etc.

The goal here is to anchor combat and exploration around these 8 skills and funnel all actions to one of them to make arbitration by the Game Master as streamlined as possible.

### Main
 - Physique (STR * 2 + AGI) - Climb a wall, Jump a ravine, Throw a rock hard and far - Weapon Damage
 - Finesse (AGI * 2 + DEX) - Dive through a window, Balance on a beam, Throw a rock fast and accurate - Weapon Accuracy
 - Precision (DEX * 2 + ACU) - Pick a lock, disarm a trap, Distance at which you can throw a rock accurately - Weapon Range
 - Reason (INT * 2 + ACU) - Solve a puzzle - Spell power
 - Focus (ACU * 2 + PRE) - Find a trap, hear an assassin behind you - Spell accuracy
 - Insight (PRE * 2 + INT) - Read a room, tell if someone's lying - Spell range/modality
### Defensive
- Fortitude (STR + PRE) - Resist poison, Hold your ground against a shove, Recover from a wound, Physical endurance
- Resolve (INT + PRE) - Resist a mental attack, Maintain a spell when hit, Recover from stress, Mental endurance

At the moment the idea is to keep 'skills' limited in variety but wide in scope, and give players variability in play by providing circumstantial bonuses.

For example rather than having a 'lockpicking' skill, various backgrounds or archetypes could have ADV on Precision TESTs when lockpicking. Or rather than ADV the other consideration is to allow Stamina use when doing certain tasks to reflect the extra effort. Another option is to have a Proficiency modifier based on lvl that can be added to certain things if you are good at them. Regardless of which way I go want to make the bonus consistent and scalable