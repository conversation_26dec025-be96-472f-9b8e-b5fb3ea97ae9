

 **Summary of the Game Mechanics and Design**

**Design Goals:**

1. **Verisimilitude:** The game aims for realism by basing character abilities and statistics on real-world examples, such as athletes and professionals. This ensures believable representations of what is humanly possible.

2. **No Wasted Stats:** Every attribute is meaningful and contributes to a character's abilities. Skills and actions often require combinations of multiple attributes, reflecting the multifaceted nature of real-world skills.

3. **Equipment Importance and Logic:** Armor and weapons play a significant role in gameplay. Armor provides substantial protection, and weapons are designed to have realistic effects, with an emphasis on factors like reach and utility rather than just damage output.

**Attributes:**

Characters are defined by seven core attributes, each rated on a scale from 0 to 3, with higher levels representing exceptional ability:

- **0 (Poor):** Top 80%
- **1 (Average):** Top 50%
- **2 (Above Average):** Top 20%
- **3 (Professional Level):** Top 1%
- **4:** Exceptionally gifted individuals (e.g., elite athletes)
- **5:** Supernatural beings
- **6:** The most dangerous entities

**List of Attributes:**

1. **Endurance (END):** Capacity to exert effort over time.
2. **Strength (STR):** Physical power and ability to move objects.
3. **Agility (AGI):** Physical speed and movement efficiency.
4. **Dexterity (DEX):** Precision in manipulating objects.
5. **Acuity (ACU):** Speed of perception and information processing.
6. **Intellect (INT):** Analytical ability and problem-solving.
7. **Presence (PRE):** Emotional intelligence and mental control.

---

**Archetypes:**

Archetypes are combinations of attributes that represent common skill sets or roles. Each archetype is calculated using specific formulas:

1. **Fortitude (FRT):** `2 × END + STR`
   - Used for enduring hardship, resisting poisons, and handling wounds.

2. **Physique (PHY):** `2 × STR + AGI`
   - Used for physical feats like climbing, jumping, and throwing.

3. **Finesse (FIN):** `2 × AGI + DEX`
   - Used for agile movements, balancing, and accurate throwing.

4. **Precision (PRC):** `2 × DEX + ACU`
   - Used for tasks requiring fine motor skills, like picking locks.

5. **Focus (FCS):** `2 × ACU + INT`
   - Used for perception tasks, such as detecting traps or tracking.

6. **Insight (INS):** `2 × INT + PRE`
   - Used for understanding motives, solving puzzles, and social cues.

7. **Resolve (RES):** `2 × PRE + END`
   - Used for resisting fear, maintaining concentration, and leadership.

---

**Character Examples:**

- **Acrobat:**
  - **Attributes:** END 1, STR 1, AGI 3, DEX 2, ACU 1, INT 0, PRE 1
  - **Archetypes:** FRT 4, PHY 5, FIN 8, PRC 5, FCS 2, INS 1, RES 3

- **Strongman:**
  - **Attributes:** END 2, STR 3, AGI 1, DEX 0, ACU 1, INT 0, PRE 2
  - **Archetypes:** FRT 7, PHY 7, FIN 2, PRC 1, FCS 2, INS 2, RES 6

- **Detective:**
  - **Attributes:** END 0, STR 0, AGI 1, DEX 1, ACU 3, INT 2, PRE 2
  - **Archetypes:** FRT 0, PHY 1, FIN 3, PRC 5, FCS 8, INS 6, RES 4

---

**Skills:**

- **Skill Levels:** Players can choose skills that enhance their archetype rolls.
- **Usage:** Skills add their level to the dice pool when performing relevant actions.
  - Example: A character with **Focus (FCS) 7** and **Tracking 2** would roll a total of **9 dice** when tracking.

---

**Roll Mechanics:**

- **Dice Pools:** Actions involve rolling a number of six-sided dice (d6) based on various factors.
- **Successes:** Each die that shows a **6** counts as one success.
- **Action Outcome:** The number of successes determines whether the action succeeds and how well.

**Components of a Dice Roll:**

1. **Action Dice:**
   - **Purpose:** Determine the number of actions per round and can be allocated to tasks.
   - **Calculation:** `2 + round((AGI + ACU) / 2)`
   - **Typical Range:** 3 to 5 dice.

2. **Archetype Dice:**
   - Based on the relevant archetype for the action.
   - Added directly to the dice pool.

3. **Skill Dice:**
   - Based on the relevant skill level.
   - Added to the dice pool when applicable.

4. **Stamina Dice:**
   - **Purpose:** Represent physical effort exerted in actions.
   - **Calculation:** `2 + FRT`
   - **Usage:** Players can choose how many to spend, within limits.
   - **Risk:** Rolling a **1** on a Stamina Die results in its loss from the pool (fatigue).

5. **Equipment Dice:**
   - **Purpose:** Reflect the advantage provided by equipment.
   - **Standard Bonus:** Typically adds +2 dice.
   - **Variations:** Off-hand weapons or lesser equipment may add +1 die; superior equipment may add more.

---

**Action Resolution Examples:**

1. **Novice Climbing a Wall:**
   - **PHY:** 4
   - **No Climbing Skill**
   - **Dice Rolled:** 1 Action Die + 4 Archetype Dice + 1 Stamina Die = **6 Dice**

2. **Skilled Climber on a Difficult Climb:**
   - **PHY:** 6
   - **Climbing Skill:** 2
   - **Action Dice Spent:** 2 (taking extra time)
   - **Stamina Dice Spent:** 3 (exerting more effort)
   - **Dice Rolled:** 2 Action Dice + 6 Archetype Dice + 2 Skill Dice + 3 Stamina Dice = **13 Dice**

---

**Equipment Mechanics:**

- **Weapons:**
  - **Standard Bonus:** Typically add +2 dice when attacking.
  - **Off-hand Weapons:** May provide a lesser bonus (+1 die).
  - **Attributes:** May have properties like "Power" (allowing more Stamina Dice to be spent) or "Reach."

- **Armor:**
  - **Protection:** Provides significant defense benefits.
  - **Fatigue:** Heavy armor may have a "Fatigue" rating, causing Stamina Dice to be lost on rolling low numbers (e.g., 1s and 2s).

- **Durability:**
  - **Fragility:** Equipment may break if a certain number of **1s** are rolled, representing wear and tear.

---

**Stamina and Fatigue:**

- **Stamina Dice Loss:**
  - Rolling a **1** on a Stamina Die results in losing that die from the Stamina pool.
- **Recovery:**
  - Stamina Dice can be recovered through rest or other means within the game mechanics.

---

**Task Difficulty:**

- **Easy Tasks:** Require **1 success**.
  - **Novice Success Rate:** Around 50%.
  - **Journeyman Success Rate:** Approximately 75%.
  - **Master Success Rate:** Over 90%.

- **Difficult Tasks:** Require **2 successes**.
  - **Novice Success Rate:** Around 25%.
  - **Journeyman Success Rate:** Approximately 50%.
  - **Master Success Rate:** Around 75%.

- **Very Difficult Tasks:** May require **3 or more successes**.
  - Designed to challenge players and encourage teamwork.

---

**Teamwork and Strategy:**

- **Cooperative Actions:**
  - Players can assist each other to achieve higher success rates on difficult tasks.
- **Strategic Use of Resources:**
  - Managing Action Dice, Stamina Dice, and equipment effectively is crucial for success.
- **Example Scenario:**
  - **Taking Down an Ogre:**
    - Targeting a vulnerable spot (e.g., the eye) may require **4 successes**.
    - Players may need to combine efforts, use abilities to lower difficulty, or enhance the attacker's dice pool.

---

**Summary of Recommendations**

1. **Probability Calibration:**
   - Analyze the probabilities associated with different dice pools to ensure outcomes align with real-world expectations and desired success rates.

2. **Skill and Attribute Synergy:**
   - Consider allowing skills to enhance multiple archetypes where appropriate to reflect the multifaceted nature of certain abilities.

3. **Equipment Differentiation:**
   - Introduce properties like "Reach," "Defense Bonus," or "Armor Penetration" to differentiate equipment beyond dice bonuses.

4. **Streamline Dice Mechanics:**
   - Simplify dice pools by consolidating dice types or using fixed modifiers to reduce complexity and speed up gameplay.

5. **Adjust Stamina Mechanics:**
   - Reevaluate the fatigue system to balance the risk and encourage players to exert effort without excessive penalties.

6. **Introduce a Durability System:**
   - Implement a more forgiving equipment durability mechanic to reduce frustration from frequent breakages.

7. **Define Action Types:**
   - Categorize actions to manage the action economy effectively and maintain game pacing.

8. **Enhance Teamwork Mechanics:**
   - Develop clear rules for assisting and cooperative actions to encourage teamwork and collaborative problem-solving.

9. **Playtesting:**
   - Conduct thorough playtesting to observe mechanics in action and make adjustments based on player feedback and observed outcomes.

10. **Simplification Opportunities:**
    - Look for areas to simplify rules and mechanics without sacrificing depth to enhance accessibility for players.

---

By incorporating these recommendations, the game can better achieve its design goals, providing a realistic, engaging, and strategic experience for players.