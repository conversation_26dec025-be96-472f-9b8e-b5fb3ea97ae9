# Sellsword Core Rules

This document outlines the fundamental rules for playing Sellsword.

## Core Mechanic: d6 Dice Pool

*   **System:** Based on rolling pools of six-sided dice (d6s).
*   **Building the Pool:** `(Relevant Derived Action Score) + Skill Dice + Gear Dice + Resource Dice - Difficulty Dice`
    *   See [[_Attributes_README]] for Derived Action Scores.
    *   See [[_Skills_README]] for Skill Dice.
    *   See [[_Equipment_README]] for Gear Dice.
*   **Rolling & Success:** Roll the final dice pool. Each '6' rolled counts as **one success**.
    *   **Basic Success:** For standard actions without Complexity, **one success** is typically required to achieve the intended outcome.
    *   **Complex Actions:** Some intricate actions may have a **Complexity** requirement (see below), needing **multiple successes** (e.g., "Requires 2 successes") to succeed fully.
    *   **Extra Successes (Stunts):** If you roll _more_ successes than required (more than 1 for simple actions, or more than the required number for complex ones), these extra successes can usually be spent immediately on minor beneficial effects called [[Stunts]]. See the [[_Stunts_README]] for details.
*   **Wager/Push Mechanic:**
    *   Players can choose to spend **Stamina** (for physical actions) or **Will** (for mental actions) to add dice equal to the amount spent to their pool _before rolling_.
    *   **Risk:** If any '1' is rolled on the **wagered dice** themselves, there is a cost. For each '1' rolled on a wagered die:
        *   Lose 1 point of the wagered resource (Stamina or Will).
        *   Lose additional points of that same resource equal to your current **Fatigue** (if wagering Stamina) or **Strain** (if wagering Will). See Conditions section below.
        *   *Example:* A character with Fatigue 2 wagers 3 Stamina dice. They roll one '1' on those wagered dice. They lose 1 (for the '1') + 2 (for Fatigue) = 3 Stamina total.
*   **Difficulty:** Represents adverse conditions (poor light, cover, range) or simple targeting adjustments (hitting specific locations).
    *   **Mechanic:** Subtracts dice (`-X` Difficulty Dice) directly from the player's dice pool _before_ rolling, making success less likely.
*   **Complexity:** Represents inherently intricate actions (masterwork locks, complex spells, multi-tasking maneuvers).
    *   **Mechanic:** Requires **additional successes** (e.g., "Requires 2 successes") beyond the standard single success needed for simple actions. This must be achieved on the roll to gain the _full intended effect_. Failing to meet the threshold often results in partial success or failure of the complex part. Skills can sometimes reduce or negate Complexity requirements.
*   **Automatic Success Threshold:** To streamline rolls with very large dice pools (though groups who enjoy rolling large pools may choose to ignore this):
    *   **Trigger:** If a character's **final dice pool** (after all modifiers, including Difficulty) is **11 dice or more**.
    *   **Procedure:**
        1.  **Grant 1 Automatic Success:** Counts towards the action's requirement (1 for simple, or the first needed for Complex actions).
        2.  **Reduce Pool:** Remove dice equal to the character's base **Derived Action score** being used.
        3.  **Roll Remainder:** Roll the remaining dice. Any '6's count as additional successes (potentially fulfilling remaining Complexity requirements or becoming available for Stunts).

## Character Structure

Characters are defined by:

*   **Attributes:** 8 Prime Attributes. See [[_Attributes_README]].
*   **Derived Stats:** Resources like Speed, Carry, Stamina, Will, Standing Slots, Lore Slots. See [[_Attributes_README]].
*   **Derived Actions:** 7 core actions (FRT, PHY, FIN, PRC, FCS, RES, INS) used for task resolution. See [[_Attributes_README]].
*   **Skills:** Trained abilities. See [[_Skills_README]].
*   **Features:** Unique character abilities. See [[_Features_README]].
*   **Standing:** Social capital. See [[_Standing_README]].
*   **Lore:** Specific knowledge. See [[_Lore_README]].

## Character Creation

Creating a Level 1 Sellsword involves the following steps:

### 1. Choose Point Buy Method

Select **one** method to determine your starting Prime Attribute scores:

*   **Broad Competence:** Start with **5 points** to spend. Maximum starting score for any attribute is **+2**.
*   **Specialization:** Start with **4 points** to spend. Maximum starting score for any attribute is **+3**.

_Spending Points:_ Each +1 increase costs 1 point (base 0). You can take negative scores (-1, -2) to gain points. The final sum of all attribute scores must equal your method's total (5 or 4). See [[_Attributes_README]] for attribute details.

### 2. Calculate Derived Stats

Based on your final Attribute scores, calculate your Derived Stats: Speed, Carry, Stamina, Will, Standing, and Lore Slots. See [[_Attributes_README]] for formulas.

### 3. Spend Starting Skill Points (SP)

Skills represent trained abilities.

*   **Starting SP:** Gain **7 Skill Points** at Level 1.
*   **Spending SP:** Buy Skill levels. Costs are cumulative: Level 1 costs 2 SP; Level 2 costs 3 additional SP (5 total); Level 3 costs 4 additional SP (9 total). See [[_Skills_README]].
    *   Your 7 SP allows flexibility (e.g., three L1 skills, or one L2 + one L1).

### 4. Final Steps

*   Choose specific **Lore Entries** to fill your `Lore Slots`. See [[_Lore_README]].
*   Select starting **Standing** entries (representing connections, background). See [[_Standing_README]].
*   Determine starting **Gear** (weapons, armor, tools). See [[_Equipment_README]].

## Combat

*   **Lethality:** Combat is designed to be dangerous and potentially quick.
*   **Health & Damage:** Uses a **Wound System** instead of Hit Points. Damage inflicts wounds with specific mechanical effects.
*   **Armor:** Provides **Damage Reduction (DR)** against incoming damage. Armor effectiveness varies by coverage zone (e.g., Body vs. Voids).
*   **Attacks:** Often target specific **Hit Locations** (Body, Voids, Vitals) interacting with armor coverage. The choice of location affects the Difficulty and potential severity of the hit.
*   **Weapons:** Provide bonuses (dice, damage, special qualities). See [[_Equipment_README]].

Combat in Sellsword is intended to be tactical, dangerous, and potentially quick.

### Combat Flow: Declarative & Active Phases

Sellsword uses an initiativeless system focused on simultaneous declaration and GM-guided resolution. Each combat round consists of two phases:

1.  **Declarative Phase:**
    *   The GM first declares the intended actions for all NPCs and monsters involved in the combat (e.g., "Bandit 1 shoots at Player A," "Ogre moves towards Player B").
    *   Then, each player declares their character's intended actions for the round. Players are encouraged to coordinate their actions during this phase.

2.  **Active Phase:**
    *   Once all actions are declared, the GM resolves them in an order they determine makes the most sense narratively and mechanically.
    *   **Suggested Resolution Order:** To maintain consistency, the GM should generally resolve actions starting with the lowest Action Point (AP) cost first. Within each AP cost tier, the suggested order is:
        1.  Ranged Attacks
        2.  Melee Attacks
        3.  Movement Actions
        4.  Spellcasting / Complex Skill Uses
    *   This process repeats for actions with higher AP costs (e.g., resolve all 1 AP actions, then all 2 AP actions, etc.).
    *   The GM has the final say on resolution order but should consider player input if the timing of interdependent actions is crucial.

### Action Economy
*   **Action Points (AP):** Characters typically have **4 Action Points** to spend each combat round.
*   **Action Costs:** Most actions require spending AP. Unless specified otherwise by a skill or feature, the minimum cost for any action is **1 AP**. Specific costs include:
    *   **Move:** Moving up to your Speed costs **1 AP**.
    *   **Attack (Weapon):** Attacking with a weapon costs AP equal to that weapon's **Carry** value (see [[_Equipment_README]]). Heavier weapons require a greater commitment to wield effectively.
    *   **Interact:** Simple interactions (drawing a dagger, opening an unlocked door, retrieving a potion) typically cost **1 AP**.
    *   **Active Defense:** (Cost TBD - see Defense section).
    *   **Skill/Feature Use:** Costs vary depending on the skill/feature (many simple uses may cost 1 AP, complex ones may cost more).
    *   **Magic Use:** AP cost depends on the complexity and magnitude of the magical effect being created (Evoked, Woven, Scribed). Simple effects might cost 1-2 AP, while powerful or intricate ones cost more. Spending Will may potentially reduce the AP cost. (Specifics TBD under Magic System).
*   **Common Actions:**
    *   **Attack:** Make a melee or ranged attack using the appropriate Derived Action (PHY, FIN, PRC). See below.
    *   **Move:** Change position on the battlefield.
    *   **Defend:** Actively attempt to block, parry, or dodge an incoming attack.
    *   **Use Skill/Feature:** Employ a specific skill or feature.
    *   **Interact:** Manipulate an object, open a door, etc.

### Making Attacks
*   **Declare Target & Location:** State who you are attacking and which **Hit Location** you are targeting (Body, Voids, or Vitals).
*   **Determine Action:** Choose the Derived Action for your attack (PHY, FIN, PRC). Note that targeting Voids requires at least FIN, and targeting Vitals requires at least PRC, though higher precision actions can target easier locations (e.g., PRC can target Body).
*   **Determine Difficulty/Complexity:** The GM determines Complexity (for intricate maneuvers) and adds any situational Difficulty Dice (due to range, cover, defender's TD). Crucially, targeting specific Hit Locations also adds inherent Difficulty:
    *   **Body:** +0 Difficulty Dice (Baseline)
    *   **Voids:** +3 Difficulty Dice
    *   **Vitals:** +5 Difficulty Dice
*   **Build Dice Pool:** Assemble your pool: `(Derived Action Score) + Skill Dice + Gear Dice + Wagered Stamina/Will - Difficulty Dice`.
*   **Roll & Determine Success:** Roll the pool. One '6' is a basic success. Meet Complexity requirements if applicable. Extra successes can be spent on [[Stunts]].
*   **Targeting Defense:** Attacks generally target the defender's passive **Target Difficulty (TD)**. This value (derived from the defender's Speed and Size - details TBD) is treated as **Difficulty Dice** subtracted from the attacker's pool before rolling. However, defenders may choose to actively defend (see Defense section).

### Defense

Characters primarily rely on their passive **Target Difficulty (TD)** to avoid being hit, but can choose to actively defend.

*   **Passive Defense (Target Difficulty - TD):** Represents how inherently difficult a target is to hit based on its speed and physical size profile.
    *   **Mechanic:** TD imposes **Difficulty Dice** on attackers (subtracted from their pool before rolling).
    *   **Calculation:** `TD = max(0, floor(Speed / 2) + Size_Profile_Modifier)`
        *   `Speed`: The target's current Speed derived stat.
        *   `Size_Profile_Modifier`: Based on the target's size category:
            *   Tiny: +3
            *   Small: +1
            *   Medium: 0 (Baseline for most Player Characters)
            *   Large: -1
            *   Huge: -2
            *   Gargantuan: -3
        *   `max(0, ...)`: Ensures TD is never negative.
*   **Active Defense:** A character can declare an active defense maneuver (e.g., Dodge, Parry, Block) during the Declarative Phase.
    *   **Cost:** Performing an active defense costs **Action Points (AP)**:
        *   **Dodge:** Costs **1 AP**.
        *   **Parry/Block (with item):** Costs AP equal to the **Carry** value of the item used (weapon, shield).
        *   **Stamina for Speed:** A character can spend **1 Stamina** to reduce the AP cost of a specific Parry/Block action by 1 AP (to a minimum cost of 1 AP). This allows a character to use a higher Carry item (e.g., Carry 2 shield) to defend against a faster incoming attack (e.g., 1 AP cost attack) by spending Stamina to match the timing. This Stamina must be spent when the defense is declared. A maximum of 1 AP cost can be reduced per action this way.
    *   **Mechanic:** Instead of the attacker rolling against the defender's TD, both attacker and defender make an **opposed roll**.
        *   The attacker rolls their normal attack pool (Action + Skill + Gear + Wager - Difficulty).
        *   The defender rolls a pool based on the defense type (e.g., FIN + Skill/Gear for Dodge, PHY/FIN + Skill/Gear for Parry/Block) + Wagered Stamina/Will.
        *   **Compare Successes:** If the defender achieves **equal or more successes** than the attacker:
            *   **Dodge:** The attack is entirely negated.
            *   **Parry/Block:** The attack is negated. Any successes rolled by the defender *beyond* the number needed to match the attacker are **extra successes** that can be spent on appropriate [[Stunts]], mirroring offensive Stunts where applicable (e.g., [[Put_Off_Balance|Put Off Balance]] against the attacker, [[Gain_Momentum|Gain Momentum]] for the defender's next action). See the [[_Stunts_README]] for the full list and costs.

### Damage, Armor & Wounds
*   **Damage:** After a successful hit, the base damage dealt is equal to the weapon's **Damage** stat (see [[_Equipment_README]]). Extra successes generated on the attack roll do not increase damage; they are used for [[Stunts]].
*   **Armor:** Provides **Damage Reduction (DR)**. The DR value listed for an armor piece (see [[_Equipment_README]]) is subtracted directly from incoming damage before determining Wound Severity. The specific DR value applied depends on the **Hit Location** targeted and the armor worn (e.g., Body DR, Voids DR, Vitals DR from helmets).
*   **Wound System:** Instead of HP, characters take Wounds when damage penetrates armor (i.e., when incoming Damage exceeds the armor's DR for the specific **Hit Location** targeted). The process is:
    1.  **Calculate Penetrating Damage:** Determine how much damage exceeded the location's DR. If 0 or less, no wound occurs.
    2.  **Determine Wound Roll Dice:** The number of d6 rolled equals the Penetrating Damage (minimum 1d6).
    3.  **Make Wound Roll:** Roll the Wound Dice and sum the total result.
    4.  **Mitigation (Optional Stamina Spend):** Before consulting the table, the character *may* spend **1 Stamina** to reduce the Wound Roll total by their **FRT Score** (minimum 1 reduction). This can be done multiple times, but the final total cannot be reduced below 1. This represents using stamina to lessen the impact or severity of the injury received.
    5.  **Consult Wound Table:** Compare the **final Wound Roll total** to the Wound Table corresponding to the **Hit Location** (Body, Voids, or Vitals). Higher results indicate more severe specific wound types.
    6.  **Apply Specific Wound Effect:** Apply the mechanical effects listed for the resulting wound type on the table. These effects inherently scale based on the roll result and are detailed in the [[Wounds/]] directory.
    7.  **Armor Sundering:** Some high results on the Wound Tables may indicate that the armor's DR for the hit location is reduced (sundered) as part of the wound's effect. *(Armor repair rules TBD)*.

### Movement & Positioning

*   **Movement Rate:** Spending 1 AP allows movement up to the character's **Speed** derived stat (in hexes or appropriate units).
*   **Difficult Terrain:** Moving through difficult terrain halves the distance covered for 1 AP (e.g., Speed 4 becomes 2 hexes). A character may attempt a PHY or FIN check (GM sets Difficulty) to try and move their full Speed through difficult terrain for 1 AP, but failure may result in losing the AP with no movement or another consequence (GM discretion).
*   **Cover:** Obstacles can provide cover against attacks.
    *   **Minor Cover:** Imposes **2 Difficulty Dice** on attacks targeting the character.
    *   **Major Cover:** Imposes **4 Difficulty Dice** on attacks targeting the character.
*   **Upper Hand:** Certain advantageous situations (like flanking an opponent, attacking from higher ground, etc.) can grant the **Upper Hand**, providing **+2 bonus dice** to the relevant action roll (typically attacks). Flanking is a common way to gain the Upper Hand in melee.
*   **Reach:** Determines the maximum distance (in hexes) a character can make a melee attack from.
    *   **Calculation:** Total Reach = Base Reach (from Size) + Weapon Reach (from equipment).
    *   **Base Reach (Size):** Small=0, Medium=1, Large=2 (adjust as needed for other sizes).
    *   **Weapon Reach:** The value listed for a weapon in [[_Equipment_README]] assumes one-handed use.
    *   **Two-Handed Grip:** Using a weapon designed for one or two hands with *both* hands reduces its listed Weapon Reach by 1, but also reduces its **Carry** value by 1 (making attacks with it cost 1 less AP). This does not apply to weapons explicitly designed only for two-handed use.
*   **Engagement/Disengagement:** There are no specific "Opportunity Attacks" triggered by movement. However, due to the Declarative/Active phase system, declaring a move away from an opponent who has declared an attack against you is still inherently risky, as their attack may resolve before your movement occurs based on Action Point costs and the resolution order (Melee Attacks typically resolve before Movement within the same AP cost tier).

## Conditions

Certain negative effects can impose conditions on characters, primarily Fatigue, Encumbrance, and Strain. These conditions often stack in levels (e.g., Fatigue 1, Fatigue 2).

*   **Fatigue:** Represents physical exhaustion.
    *   **Sources:** Wearing heavy armor (may grant inherent Fatigue levels), Serious/Critical Wounds, failed FRT checks against exertion, environmental effects, specific spells/abilities.
    *   **Effects:**
*   <!-- OLD: Each level imposes a **-1 die penalty** on **FRT** and **PHY** Derived Action rolls. -->
*   Each level reduces Max **Stamina** by 1.
        *   Each level increases the **Stamina** lost when rolling a '1' on wagered Stamina dice by 1 (see Wager/Push Mechanic).
*   **Encumbrance:** Represents being physically burdened or hindered by gear or weight.
    *   **Sources:** Carrying items exceeding Carry capacity (details TBD), specific armor types, environmental effects (mud, deep snow), grappling/restraints.
    *   **Effects:**
*   <!-- OLD: Each level imposes a **-1 die penalty** on **FIN** and **PRC** Derived Action rolls. -->
*   <!-- OLD: Each level reduces the character's **Speed** derived stat by 1. -->
*   Each level imposes a **-1 die penalty** on **PHY**, **FIN**, and **PRC** Derived Action rolls.
*   Each level reduces the character's **Speed** derived stat by 1.
*   **Strain:** Represents mental exhaustion or stress.
    *   **Sources:** Maintaining complex spells, Serious/Critical Wounds affecting the mind, failed RES checks against mental stress, specific spells/abilities.
    *   **Effects:**
        *   Each level imposes a **-1 die penalty** on **FCS** and **RES** Derived Action rolls.
        *   Each level increases the **Will** lost when rolling a '1' on wagered Will dice by 1 (see Wager/Push Mechanic).

*   **Recovery:** Fatigue, Encumbrance, and Strain levels are generally only reduced or removed by addressing the source of the condition (e.g., removing heavy armor causing Fatigue/Encumbrance, healing a wound causing Fatigue, ending a spell causing Strain, dropping gear causing Encumbrance). Rest alone does not typically alleviate these conditions unless the source is also resolved.

## Resources

*   **Stamina:** Represents short-term physical energy. Used for the Wager mechanic and potentially some Features/Skills. Recovers relatively easily (e.g., after combat, short rest). See [[_Attributes_README]] for calculation.
*   **Will:** Represents mental fortitude, focus, and magical potential. Used for the Wager mechanic, magic, and potentially some Features/Skills. A scarce resource that recovers slowly or under specific conditions (significant success, GM reward, rest). See [[_Attributes_README]] for calculation.
*   **Standing & Lore Slots:** Abstract resources representing social connections and knowledge areas, primarily used during downtime or preparation phases. See [[_Standing_README]] and [[_Lore_README]].

## Exploration & Downtime

### Travel (TESS Method)

Travel between locations is handled abstractly using the TESS (Travel Events Social Skill) method:

*   **Distances:** Journeys are categorized by abstract distance:
    *   **Close:** 1 Travel Event likely.
    *   **Far:** 2 Travel Events likely.
    *   **Very Far:** 3 Travel Events likely.
*   **Travel Events:** Between departure and arrival, the GM introduces events based on the distance. These can be pre-planned or determined randomly, relevant to the terrain and situation. Event types include:
    *   **Blue (Social):** Encounters with NPCs (merchants, travelers, rival parties, etc.).
    *   **Red (Combat):** Encounters involving hostile creatures or people.
    *   **Yellow (Exploration/Skill):** Discoveries (ruins, landmarks) or environmental challenges (obstacles, hazards) requiring skill checks.
*   **Event Flexibility:** Events can be hybrids (e.g., a social encounter that turns into combat) or change based on player actions.

### Light Sources (Torches)

Exploring dark places is inherently dangerous. Light sources like torches are crucial resources and are tracked using a diminishing dice pool mechanic:

*   **Torch Dice Pool:** A standard torch starts with a pool of dice (e.g., 4d6). The specific number of dice depends on the torch type (see [[_Equipment_README]]).
*   **Checking the Torch:** At regular intervals determined by the GM (e.g., every hour, after a significant event, when entering a new area), the player carrying the torch rolls its current dice pool.
*   **Diminishing Resource:** If **any '1's** are rolled, the torch's dice pool is **reduced by one die**. When the pool reaches zero dice, the torch gutters out.
*   **(Optional) Dungeon Events:** The GM might use the sum of the torch roll to trigger random dungeon events, with lower rolls potentially indicating worse outcomes (e.g., attracting wandering monsters, revealing hazards).

*(Rules for other light sources, managing food/water, environmental hazards, and specific downtime activities to be added)*

## Magic

Magic in Sellsword involves manipulating the fundamental forces of reality, known as **Wyrds**, through focused application of will and knowledge.

### Wyrds of Power
Wyrds represent distinct domains of magical influence. Knowing a Wyrd (typically represented by a [[Lore]] entry) allows a character to understand and potentially interact with its domain. The known Wyrds are broadly categorized:

*   **Physical Wyrds** _(Manipulation of the Natural World)_
    *   [[Eldr_Lore|Eldr]] – Fire, destruction, heat, change.
    *   [[Vatn_Lore|Vatn]] – Water, flow, adaptation, renewal.
    *   [[Jord_Lore|Jörð]] – Earth, stability, endurance.
    *   [[Loftr_Lore|Loftr]] – Air, movement, freedom.
    *   [[Ljos_Lore|Ljós]] – Light, illumination, visibility, shadow manipulation.
*   **Mental Wyrds** _(Influencing Thought, Memory, and Perception)_
    *   [[Hugr_Lore|Hugr]] – Emotion, empathy, desire, passion.
    *   [[Rok_Lore|Rök]] – Logic, reason, clarity, structured thought.
    *   [[Draumr_Lore|Draumr]] – Dreams, subconscious influence, mental illusions.
    *   [[Visa_Lore|Vísa]] – Foresight, prophecy, truth-seeing.
    *   **Ekkó** – Aetheric echoes, past resonance, reading lingering imprints (Lore TBD).
*   **Spiritual Wyrds** _(Forces of Life, Death, and the Soul)_
    *   **Lífi** – Life, vitality, growth, renewal (Lore TBD).
    *   **Dauði** – Death, decay, entropy (Lore TBD).
    *   **Önd** – The soul, communion with spirits (Lore TBD).
    *   **Skuggi** – Shadows, veils, unseen forces (Lore TBD).
    *   **Æther** – Raw magic, celestial force, disruption/stabilization (Lore TBD).

### Casting Magic
Actually performing magic requires combining knowledge of a Wyrd ([[Lore]]) with one or more of the four **Magic Skills**, each representing a different aspect of magical manipulation:

*   **[[Evoke|Evoke]] (RES - Resolve):** The act of drawing raw Aether through the Veil, gathering the fundamental magical potential associated with a known Wyrd. This skill governs the **amount** or **intensity** of power gathered. More successes might yield more potent energy for subsequent shaping or imbuing. Failure can result in insufficient power or potential mishaps.
*   **[[Weave|Weave]] (FCS - Focus):** The act of shaping, controlling, and maintaining the flow of gathered Aether into a specific, often dynamic or ongoing, form. This skill governs the **complexity, duration, or precision** of the shaped effect (e.g., forming a sustained barrier, creating a complex illusion, subtly influencing emotions). It requires concentration and fine control.
*   **[[Scribe|Scribe]] (PRC - Precision):** The act of binding gathered Aether into a physical medium (an object, rune, location) to create a stored, delayed, or persistent magical effect. This skill governs the **potency, duration, or trigger conditions** of the imbued magic (e.g., enchanting a weapon, setting a magical ward, brewing a potion). It requires careful, precise work.
*   **[[Scry|Scry]] (INS - Insight):** The act of using Aether to perceive information or energies beyond the mundane senses, often filtered through the lens of a specific Wyrd. This skill governs the **clarity, range, or detail** of the information perceived (e.g., divining possible futures, sensing hidden magical auras, reading emotional states, communicating over distances).

**The Process of Magic:** Creating significant magical effects often involves a sequence of actions using these skills:
1.  **Declare Intent:** The player describes the desired magical effect and the Wyrd(s) involved.
2.  **GM Determines Requirements:** The GM assesses the effect's Difficulty, Complexity, and which Magic Skills are necessary (e.g., a simple fire bolt might just need Evoke, while a complex illusion needs Evoke then Weave). Multiple checks might be required for powerful effects.
3.  **Perform Magic Skill Check(s):** The player makes checks for the required Magic Skills.
    *   **Build Pool:** `(Relevant Derived Action Score) + Magic Skill Dice + [Wyrd Dice if Weaving/Scribing] + Wagered Will - Difficulty Dice`. (Note: Lore reduces Difficulty/Complexity, doesn't add dice).
    *   **Roll & Resolve:** Determine successes based on the core mechanic, comparing against Complexity if applicable (Complexity likely requires successes from the *entire* pool - TBC). Extra successes from *non-Wyrd dice* fuel [[Stunts]]. Failure triggers Mishaps (see below).
    *   **AP Cost:** Evoke and Weave/Scribe actions cost **1 AP per Wyrd Die** being generated or manipulated (minimum 1 AP). *(Rushing mechanic TBD)*.
    *   **Damage/Magnitude:** The base damage or magnitude of an effect created via Weave/Scribe is typically **1d6 per Wyrd Die** used in the check. Specific results on the Wyrd Dice may determine parameters like area, duration, etc., as adjudicated by the GM or defined by a "Learned" effect.
    *   *Example Sequence:* A character might first make an **Evoke** check to gather fire Aether, then immediately make a **Weave** check to shape that Aether into a controlled jet targeting an enemy.

#### Freeform Magic & Defining Effects

Sellsword utilizes a freeform magic system rather than predefined spell lists. Players leverage their knowledge of Wyrds ([[Lore]]) and their proficiency in the four Magic Skills to create desired magical effects.

*   **Player Intent:** The player describes the effect they wish to achieve based on their character's understanding (Lore) and capabilities (Skills).
*   **GM Adjudication:** The GM determines the feasibility, necessary Skill checks (potentially multiple), **Difficulty**, **Complexity**, **AP cost (1 per Wyrd Die)**, and potential consequences based on the effect's scale, the character's relevant Lore Tiers, and the situation. **Will** is typically only spent voluntarily via the Wager mechanic to add dice. Higher Lore Tiers make more ambitious effects possible or reduce the Difficulty/Complexity of related checks.
*   **"Learning" Effects:** Through successful use, experimentation during downtime, or specific training, a player and GM can collaboratively define specific, named effects that the character becomes adept at creating. These could be recorded by the player, potentially granting a minor reduction in Complexity or Difficulty for future uses of that specific, practiced effect. This represents the character mastering a particular application of their knowledge.

#### Example Magical Effects

These examples illustrate the process but are not exhaustive. Players and GMs should collaborate to define effects based on Lore, Skills, and intent.

1.  **Effect:** Create a small, hand-held flame for light (lasts a scene or until dismissed).
    *   **Wyrd:** Eldr (Tier 1+)
    *   **Skill(s):** Evoke (RES) check to initially conjure. Weave (FCS) check might be needed to sustain it reliably for a scene (GM discretion, could be Complexity 1 on Evoke instead).
    *   **GM Adjudication:** Simple effect.
        *   *Evoke:* Difficulty 0, Complexity 0 (or 1 if sustaining). AP Cost: 1.
        *   *Weave (if needed):* Difficulty 0, Complexity 0. AP Cost: 0 (part of sustain).
    *   **Check:** Player rolls RES + Evoke Skill dice. 1 (or 2 if sustaining) success needed. Eldr Lore T1 ignores Minor Difficulty (not applicable here).

2.  **Effect:** Briefly make a metal sword blade glow cherry-red hot (lasts 1 round).
    *   **Wyrd:** Eldr (Tier 2+)
    *   **Skill(s):** Weave (FCS) check (assumes ambient heat or requires prior Evoke check to generate heat).
    *   **GM Adjudication:** Moderate effect, requires control over heat transfer.
        *   *Weave:* Difficulty 2 (resisting dissipation), Complexity 1 (shaping heat precisely onto blade). AP Cost: 2.
    *   **Check:** Player rolls FCS + Weave Skill dice - 2 Difficulty dice. 2 successes needed (1 base + 1 Complexity). Eldr Lore T2 could reduce Difficulty to 0 OR Complexity to 0. Player may Wager Will.

3.  **Effect:** Sense the general emotional state (calm, agitated, fearful) of people in the next room.
    *   **Wyrd:** Hugr (Tier 1+)
    *   **Skill(s):** Scry (INS) check.
    *   **GM Adjudication:** Simple sensory effect through barrier.
        *   *Scry:* Difficulty 1 (sensing through wall). Complexity 0. AP Cost: 1.
    *   **Check:** Player rolls INS + Scry Skill dice - 1 Difficulty die. 1 success needed. Hugr Lore T1 could reduce Difficulty to 0. Player may Wager Will.

4.  **Effect:** Create a convincing illusion of a barking dog to distract a guard.
    *   **Wyrd:** Draumr (Tier 2+)
    *   **Skill(s):** Weave (FCS) check.
    *   **GM Adjudication:** Moderate complexity illusion targeting one sense.
        *   *Weave:* Difficulty 1 (guard's passive awareness/skepticism). Complexity 1 (creating specific sound). AP Cost: 2.
    *   **Check:** Player rolls FCS + Weave Skill dice - 1 Difficulty die. 2 successes needed. Draumr Lore T2 could reduce Difficulty to 0 OR Complexity to 0. Player may Wager Will.

#### Magic Mishaps

Manipulating the Aether is inherently dangerous, especially when pushing beyond one's limits. Failure carries risks beyond simply wasting effort.

1.  **Casting Requirement:** Attempting any magical effect using Evoke, Weave, Scribe, or Scry requires wagering **at least 1 Will** point (added to the dice pool).
2.  **Trigger:** A Mishap occurs if the initial Magic Skill check results in **0 successes**.
3.  **Desperate Push (Optional):** Immediately upon failing the initial check, the caster *may* attempt a Desperate Push before determining the Mishap outcome:
    *   Declare how much *additional* Will they are spending (up to their remaining total).
    *   Roll dice equal to the *additional* Will spent.
    *   **If Success (any '6' rolled):** The magical effect succeeds with 1 success (plus any additional '6's rolled on the Desperate Push dice for Stunts). The additional Will is spent. Apply standard Wager Risk (lose Will + Strain) for any '1's rolled on these Desperate Push dice. No Mishap occurs.
    *   **If Failure (still 0 successes):** The magical effect fails definitively. The additional Will is spent. Apply standard Wager Risk for any '1's rolled on the Desperate Push dice. Proceed to Step 4, adding the **sum of the dice rolled during the failed Desperate Push** to the initial wagered Will dice sum for determining Mishap severity.
4.  **Determine Mishap Severity:** Sum the result shown on the **initial Will dice** wagered for the failed check (plus the sum of any **failed Desperate Push dice** from Step 3, if applicable).
5.  **Mitigation (Optional Will Spend):** Before consulting the Mishap Table, the caster *may* spend *further* additional Will points to reduce the Mishap Severity total. (Cost/Effect TBD - e.g., 1 Will reduces total by 2?).
6.  **Consult Mishap Table:** Compare the final Mishap Severity total to the relevant **Magic Mishap Table**. Higher results generally lead to more severe consequences. (Specific tables TBD - potentially one general table or separate tables for Evoke, Weave, Scribe, Scry). Mishaps can range from minor Strain gain or wasted resources to uncontrolled magical surges, environmental effects, temporary penalties, or even summoning unwanted attention.

*(Specific Mishap Tables and Mitigation cost/effect TBD)*