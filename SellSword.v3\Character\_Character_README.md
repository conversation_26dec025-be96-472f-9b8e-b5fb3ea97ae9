# Character

A concise overview of the purpose and scope of the **Character** pillar.

## Prerequisites

- Read the [Project Overview](SellSword_v2_Overview.md).  
- Familiarize yourself with the core rules: [Core Rules](_Rules_README.md).

## Folder Structure

- `/Character/` – Main directory for this pillar’s entries.  
- `_template_character_entry.md` – Template file for creating new entries.  
- Other subdirectories as needed (e.g., `/Ancestries/`, `/Attributes/`, `/Features/`).

## Content Overview

Describe the types of files and data in this pillar, including:

- Key concepts or mechanics (Ancestries, Attributes, Derived Actions, Features).  
- Entry types (e.g., `Ancestry_Name.md`, `Attribute_Name.md`, `Feature_Name.md`).  
- Naming conventions (Title case with underscores, YAML frontmatter for metadata).

## Conventions

- **File Naming:** Title case, underscores for spaces (e.g., `My_Entry.md`).  
- **Frontmatter Fields:**  
  - `name`: Display name  
  - `type`: Category or subtype  
  - `tags`: List of relevant tags  
  - `summary`: Short description  
- **Links:** Use Obsidian-style `[[...]]` links for cross-references.

## How to Use

1. Navigate to the `Character/` directory.  
2. Browse existing entries by name or tag.  
3. Open an entry file to view details and mechanics (e.g., Ancestry, Attribute, Feature).

## Related Links

- [[SellSword_v2_Overview|Project Overview]]  
- [[_Rules_README|Core Rules]]  
- [[_Attributes_README|Attributes & Actions]]  
- [[_Skills_README|Skills Overview]]  
- [[_Equipment_README|Equipment Overview]]  
- [[_Stunts_README|Stunts List]]  
- [[_Lore_README|Lore & Wyrds]]  
- [[_Standing_README|Standing System]]  
- [[_Monsters_README|Monster Design]]  
- [[_Wounds_README|Wounds System]]  
- [[_World_README|World Overview]]

## Adding New Entries

1. Copy `_template_character_entry.md` into this folder.  
2. Rename it using the established naming convention.  
3. Fill in the YAML frontmatter and content sections.  
4. Commit your changes with a descriptive message.

## Revision History

- v2 – YYYY‑MM‑DD – Initial template for Character pillar README.
