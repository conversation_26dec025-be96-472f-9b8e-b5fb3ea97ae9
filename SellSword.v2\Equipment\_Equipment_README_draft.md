# Equipment

A concise overview of the purpose and scope of the **Equipment** pillar.

## Prerequisites

- Read the [Project Overview](SellSword_v2_Overview.md).  
- Familiarize yourself with the core rules: [Core Rules](_Rules_README.md).

## Folder Structure

- `/Equipment/` – Main directory for this pillar’s entries.  
- `_template_equipment.md` – Template file for creating new equipment entries.  
- Subdirectories:  
  - `Weapons/`  
  - `Armor/`  
  - `Shields/`  
  - `Ammunition/`  
  - `Utility/`  

## Content Overview

- Melee and ranged weapons (`Weapons/`), armor pieces (`Armor/`), shields (`Shields/`), ammunition (`Ammunition/`), and utility items (`Utility/`).  
- Each entry file uses YAML frontmatter to define stats and metadata (`name`, `type`, `carry`, `offense`, `damage`, `pierce`, `force`, `block`, `parry`, `hook`, `reach`, `range`, `dr`, etc.).  
- Naming conventions: Title case with underscores (e.g., `Longsword.md`).

## Conventions

- **File Naming:** Title case, underscores for spaces (e.g., `My_Equipment_Item.md`).  
- **Frontmatter Fields:**  
  - `name`: Display name  
  - `type`: Category (Weapon, Armor, etc.)  
  - `tags`: List of tags  
  - `summary`: Short description  
  - **Stats fields:** offense, damage, pierce, force, block, parry, hook, reach, range, body_dr, voids_dr, carry, encumbrance, fatigue, durability, etc.  
- **Links:** Use Obsidian-style `[[...]]` cross‑references.

## How to Use

1. Navigate to the `Equipment/` directory.  
2. Browse entries by category or name.  
3. Open an entry file to review stats and descriptions.

## Key Mechanics (Summary)

* **Ratings:** Stats (Offense, Damage, Pierce, Force, Block, Parry, Hook, Cover, DR) use a 1–3 scale.  
* **Offense:** Dice bonus to attack rolls.  
* **Damage:** Fixed base damage, reduced by DR.  
* **Pierce:** Reduces effective DR on hit.  
* **Force:** Momentum, used for knockback or armor sundering.  
* **Block/Parry/Hook:** Dice bonuses for active defense.  
* **Reach:** Added melee distance.  
* **Range:** Multiplier to Precision for ranged attacks.  
* **Cover (Shields):** Adds Difficulty dice to attackers.  
* **Damage Reduction:** Armor reduces incoming damage, not hit chance.  
* **Carry:** Bulk/weight affects AP cost and Fatigue.  
* **Encumbrance:** Penalties to PHY/FIN/PRC and Speed.  
* **Fatigue:** Reduces max Stamina; increases push risk.  
* **Two‑Handed Use:** Reduces AP cost by 1 (min 1).  
* **Durability:** Item resilience; wear tracking.  
* **Ammunition:** Stats for ranged projectiles.  
* **Stunts:** Equipment‑enabled combat maneuvers.

## Armor Conditions – Sunder

* **Level 1 (Minor Sunder):** –1 DR; repair: 1 AP.  
* **Level 2 (Damaged Sunder):** –2 DR; repair: 1 Exploration AP + supplies.  
* **Level 3 (Broken Sunder):** DR 0; repair: downtime + workshop.

Monsters instead suffer –1 DR per Force automatically.

## Cross‑References to Rules and Related Documents

* [[_Rules_README]] – Core combat & exploration.  
* [[_Wounds_README]] – Wound system.  
* [[_Stunts_README]] – Stunts list.  
* [[_Attributes_README]] – Attributes & Actions.  
* [[_Skills_README]] – Skills overview.  
* [[_Rules_README#Conditions]] – Conditions: Fatigue, Encumbrance, Strain.  
* [[Magic_Guide]] & [[_Lore_README]] – Magic.

## Equipment Design Guide: Step‑by‑Step Justification

Walkthrough of creating a standard item:

### Example Item: Longsword
1. **Metadata:**  
   - `name`: Longsword  
   - `type`: Weapon  
   - `category`: Bladed  
   - `summary` & `description`  
2. **Core Stats:** carry, str_req, durability, encumbrance, fatigue  
3. **Combat Stats:** offense, damage, pierce, force, block, parry, hook, reach, range, cover, body_dr, voids_dr  
4. **Special Properties:** stunts, notes, references, tags  

(Include full detailed justifications as in original guide.)

## Revision History

- v2 – YYYY‑MM‑DD – Draft merged organizational template with mechanics.
