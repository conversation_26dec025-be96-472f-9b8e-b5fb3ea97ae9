# Equipment

*In SellSword, your gear defines your capabilities. A well-equipped sellsword with quality armor and weapons has a decisive advantage over someone relying on natural talent alone.*

## Table of Contents

1. [Equipment Philosophy](#equipment-philosophy)
2. [Core Mechanics](#core-mechanics)
3. [Weapon Tables](#weapon-tables)
4. [Armor Tables](#armor-tables)
5. [Shield Tables](#shield-tables)
6. [Ammunition & Utility](#ammunition--utility)
7. [Equipment Conditions](#equipment-conditions)
8. [Light Sources](#light-sources)
9. [Two-Handed Use](#two-handed-use)
10. [Cross-References](#cross-references)

---

## Equipment Philosophy

### The Right Tool for the Job

In SellSword, **equipment defines capability**. A sellsword's survival depends on having the proper gear for each situation. Quality equipment provides crucial advantages:

- **Weapons** determine your offensive and defensive capabilities through multiple stats
- **Armor** is the difference between a glancing blow and a mortal wound
- **Tools** enable specialized actions that would otherwise be impossible
- **Supplies** sustain you through dangerous expeditions

### Gear Matters

The difference between success and failure often comes down to preparation. A torch can mean the difference between navigating safely and stumbling into danger. The right armor can turn a killing blow into a minor wound. A well-balanced weapon can provide both offensive power and defensive capability.

---

## Core Mechanics

### Equipment Stats

**Universal Stats:**
- **Carry:** Bulk and weight, determines AP cost and contributes to Fatigue (0-5+ scale)
- **Str Req:** Minimum Strength attribute needed to use effectively
- **Durability:** Resistance to damage and wear (0-5+ scale)

**Weapon Stats (0-3 scale):**
- **Offense:** Adds dice to attack rolls
- **Damage:** Base damage dealt on successful hit
- **Pierce:** Reduces target's effective Damage Reduction
- **Force:** Impact momentum for knockback and Sunder effects
- **Block:** Dice bonus to blocking maneuvers
- **Parry:** Dice bonus to parrying attacks
- **Hook:** Ability to manipulate opponent's weapon or body
- **Reach:** Effective melee distance added to character's base reach
- **Range:** For ranged weapons, optimal distance (multiplier of Precision)

**Armor Stats:**
- **Body DR:** Damage reduction for torso and limb hits (0-3 scale)
- **Voids DR:** Damage reduction for gaps, joints, and weak points (0-3 scale)
- **Encumbrance:** VoidsDR - 1, imposes -1 die penalty per point on Physique, Finesse, and Precision actions; also reduces Speed
- **Fatigue:** Carry - 1, reduces maximum Stamina

**Shield Stats:**
- **Cover:** Adds Difficulty dice to attackers targeting the shield bearer (0-3 scale)

*Note: Shields also have Block and Parry stats like weapons, but Cover is unique to shields.*

### Action Point Costs

- **Attack AP Cost = Weapon Carry value**
- **Block/Parry AP Cost = Weapon/Shield Carry value**
- **Two-Handed Use:** Reduces AP cost by 1 (minimum 1 AP) for all weapon actions

---

## Weapon Tables

### Light Melee Weapons

| Name | Carry | Str | Dur | Off | Dmg | Prc | Frc | Blk | Par | Hk | Rch | Notes |
|------|-------|-----|-----|-----|-----|-----|-----|-----|-----|----|----- |-------|
| Quillon Dagger | 0 | 0 | 2 | 2 | 2 | 1 | 0 | 1 | 1 | 0 | 0 | Crossguard provides improved defense |
| Rondel Dagger | 0 | 0 | 2 | 2 | 1 | 3 | 0 | 1 | 1 | 0 | 0 | Highly effective vs armor |
| Stiletto | 0 | 0 | 2 | 2 | 2 | 3 | 0 | 0 | 1 | 0 | 0 | Armor-piercing specialist |
| Arming Sword | 1 | 1 | 2 | 2 | 2 | 2 | 1 | 1 | 2 | 0 | 1 | Versatile one-handed sword |

### Heavy Melee Weapons

| Name | Carry | Str | Dur | Off | Dmg | Prc | Frc | Blk | Par | Hk | Rch | Notes |
|------|-------|-----|-----|-----|-----|-----|-----|-----|-----|----|----- |-------|
| Longsword | 2 | 1 | 3 | 2 | 2 | 1 | 1 | 1 | 2 | 0 | 1 | Two-handed use reduces AP cost |
| Great Sword | 3 | 3 | 3 | 3 | 3 | 2 | 3 | 3 | 2 | 0 | 2 | Requires two hands to wield |

### Polearms

| Name | Carry | Str | Dur | Off | Dmg | Prc | Frc | Blk | Par | Hk | Rch | Notes |
|------|-------|-----|-----|-----|-----|-----|-----|-----|-----|----|----- |-------|
| Halberd | 3 | 3 | 2 | 3 | 3 | 2 | 3 | 0 | 2 | 1 | 3 | Highly versatile weapon |

### Ranged Weapons

| Name | Carry | Str | Dur | Off | Dmg | Prc | Frc | Rng | Notes |
|------|-------|-----|-----|-----|-----|-----|-----|-----|-------|
| Long Bow (Medium) | 3 | 2 | 3 | 2 | 0 | 0 | 2 | 5 | Damage/Pierce from ammunition |

---

## Armor Tables

### Light Armor

| Name | Carry | Str | Dur | Body DR | Voids DR | Enc | Fat | Notes |
|------|-------|-----|-----|---------|----------|-----|-----|-------|
| Gambeson | 0 | 0 | 1 | 1 | 1 | 0 | 0 | Basic protection, often worn under armor |

### Medium Armor

| Name | Carry | Str | Dur | Body DR | Voids DR | Enc | Fat | Notes |
|------|-------|-----|-----|---------|----------|-----|-----|-------|
| Chainmail Hauberk | 2 | 1 | 2 | 2 | 2 | 1 | 1 | Good protection with moderate penalties |

### Heavy Armor

| Name | Carry | Str | Dur | Body DR | Voids DR | Enc | Fat | Notes |
|------|-------|-----|-----|---------|----------|-----|-----|-------|
| Full Plate Harness | 5 | 3 | 5 | 3 | 3 | 2 | 4 | Maximum protection with heavy penalties |

---

## Shield Tables

### Small Shields

| Name | Carry | Str | Dur | Blk | Par | Cov | Notes |
|------|-------|-----|-----|-----|-----|-----|-------|
| Steel Buckler | 1 | 1 | 3 | 2 | 3 | 1 | Fast, excellent for parrying |

### Medium Shields

| Name | Carry | Str | Dur | Blk | Par | Cov | Notes |
|------|-------|-----|-----|-----|-----|-----|-------|
| *[To be populated with v3 data]* |

### Large Shields

| Name | Carry | Str | Dur | Blk | Par | Cov | Notes |
|------|-------|-----|-----|-----|-----|-----|-------|
| Steel Round | 3 | 3 | 3 | 3 | 2 | 3 | Maximum blocking capability |

---

## Ammunition & Utility

### Ammunition

| Name | Type | Dmg | Prc | Notes |
|------|------|-----|-----|-------|
| Arrow/Bolt - Bodkin | Arrow/Bolt | 1 | 2 | Specialized for defeating armor |

### Utility Gear

| Name | Carry | Dur | Function | Notes |
|------|-------|-----|----------|-------|
| Wooden Torch | 0 | 4 | Light Source | Durability = starting dice pool for torch mechanics |

---

## Equipment Conditions

### Sunder

Armor can suffer damage from concentrated Force effects:

**Level 1 (Minor Sunder):** Reduce DR at affected location by 1
- *Repair: 1 AP (tightening straps, clearing debris)*

**Level 2 (Damaged Sunder):** Reduce DR at affected location by 2
- *Repair: 1 Exploration AP plus appropriate supplies*

**Level 3 (Broken Sunder):** Armor provides 0 DR at affected location
- *Repair: Requires downtime and workshop facilities*

**Monster Simplification:** Monsters suffer a flat -1 DR penalty per Force applied (no tracking required). Players track Sunder levels per armor location.

---

## Light Sources

### Torch Mechanics

Torches use a degradation system to represent burning out over time:

**Starting Dice Pool:** Torches begin with 4 dice

**Degradation Check:** Periodically (every 10-15 minutes of game time), roll the torch's current dice pool:
- **No 1s rolled:** Torch continues burning normally
- **Any 1s rolled:** Remove 1 die from the torch's pool for each 1 rolled
- **0 dice remaining:** Torch burns out completely

**Light Quality:** The number of dice remaining determines illumination quality and range.

**Darkness Penalties:** Operating without light imposes Major (-4) or Extreme (-6) Difficulty on most actions.

### Light Source Types

| Source | Starting Dice | Burn Time | Range | Notes |
|--------|---------------|-----------|-------|-------|
| Torch | 4 | 30-60 min | Close | Basic illumination |
| Lantern | 6 | 2-4 hours | Close | Requires oil, better quality |
| Candle | 2 | 1-2 hours | Personal | Minimal illumination |

---

## Two-Handed Use

### Mechanical Benefits

**AP Cost Reduction:** Wielding a weapon with both hands reduces its AP cost by 1 (minimum 1 AP) for:
- Attack actions
- Block maneuvers
- Parry maneuvers
- Other weapon-based actions

**Reach Reduction:** Using a weapon with both hands reduces its effective Reach by 1 (minimum 0)

**Trade-offs:**
- **Advantage:** Faster, more controlled weapon use
- **Disadvantage:** Cannot use a shield or second weapon, reduced reach
- **Tactical Choice:** Speed and control vs. additional defensive options and reach

---

## Cross-References

### Related Systems
- **Combat Mechanics:** See [Core Rules - Combat](#) for attack resolution and damage
- **Skills:** See [Skills README](Skills_README.md) for weapon skill interactions
- **Stunts:** See [Stunts README](Stunts_README.md) for equipment-enabled special maneuvers
- **Wounds:** See [Wounds System](#) for damage resolution and armor interaction
- **Character Creation:** See [Core Rules - Character Creation](#) for Carry capacity and Strength requirements

### Equipment Integration
- **Carry Capacity:** Strength and Athletics determine maximum equipment load
- **Fatigue Effects:** Heavy armor reduces maximum Stamina
- **Encumbrance Penalties:** Restrictive armor affects action dice pools and Speed
- **Maintenance:** Durability determines repair needs and equipment longevity

---

*Equipment tables populated from SellSword v3 equipment database. For individual equipment details and expanded descriptions, see the Equipment directory files.*

**Version:** 4.0  
**Last Updated:** [Current Date]
