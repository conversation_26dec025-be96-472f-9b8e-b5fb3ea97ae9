# Sellsword Monsters

This document outlines the design philosophy, structure, and guidelines for creating monsters in the Sellsword TTRPG.

## I. Design Philosophy: Truly Monstrous

The core goal is that monsters should feel distinct from player characters and genuinely **monstrous**. This means:

1. **Superhuman Capabilities:** Often exceed normal limits in strength, speed, resilience, senses, etc.
2. **Rule Interaction:** Possess unique traits or abilities that interact with or bypass standard game mechanics in interesting ways, making encounters less predictable.
3. **Focus on Theme & Role:** Have a clear identity (e.g., ambusher, brute, controller) reinforced by mechanics.
4. **Manageable Complexity:** Prioritize impactful, unique mechanics over numerous minor ones for ease of GM use.

## II. Monster Structure & Template (`_template_monster.md`)

All monsters use the `_template_monster.md` structure. Key components:

* **Metadata:** `name`, `description_short`, `size`, `type`, `tags`.
* **Core Stats:** Simplified combat values (detailed in Section III).
  * `size`: Physical scale (Tiny to Gargantuan).
  * `combat_pool`: Base dice pool for actions.
  * `action_points`: AP per round.
  * `speed`: Movement per AP (including special types).
  * `wounds`: Tiered wound threshold pools.
* **Defense:**
  * `target_difficulty` (TD): Adds Difficulty Dice to attackers.
  * `dr`: Damage Reduction (Body/Voids).
  * Resistances/Vulnerabilities.
* **Actions:** A list defining all actions the monster can perform that cost Action Points (AP). This includes standard attacks and special maneuvers. (`name`, `ap_cost`, `pool_mod`, `reach`, `damage`, `pierce`, `force`, `notes`, `extra_success_effects`).
  * `ap_cost`: The number of Action Points required to perform the action (default 1 if unspecified).
  * `extra_success_effects`: An optional list defining effects that can be purchased by spending extra successes generated on the action's roll. Each effect has a `cost` (in extra successes) and an `effect` description.
* **Monstrous Traits:** Passive abilities or effects that do not cost AP to use. This is where unique rule interactions or inherent capabilities are defined.
* **Trait Descriptions:** When listing monstrous traits in an individual monster's file, include the full mechanical description of the trait for ease of GM reference during gameplay. Optionally, include a link back to the trait's definition in `[[Monstrous_Traits]]` for development purposes. Any monster-specific variations or addendums to the base trait should be clearly noted in the description within the monster's file.
* **Behavior & Other:**
  * `tactics`: Typical combat behavior.
  * `loot`: Potential resources gained.
  * `environment`: Typical habitats.
* **GM Discretion:** GMs should feel empowered to allow monsters to attempt reasonable actions not explicitly listed, using the `combat_pool` or GM judgment for resolution.

* **Monster Descriptions for GMs:**
  * Effective monster descriptions evoke atmosphere, mood, and thematic identity.
  * Use vivid, sensory language to paint a picture of the monster's appearance, behavior, and presence.
  * Highlight unique or unsettling features that make the monster memorable and distinct.
  * Convey the monster's role and tactics through descriptive cues (e.g., "lurks in shadows," "moves with unnatural grace," "hunts in packs").
  * Balance detail with brevity; provide enough to inspire but leave room for GM improvisation.
  * Consider the monster's environment and lore to enrich the description.
  * Example: The Huldrekall is described as a gaunt, silent aberration haunting high, dark places, with spider-like limbs and a skin that absorbs light, evoking dread and mystery.
  * Use the Goblin Kinder description as a model for small, cunning foes: emphasizing their malice, numbers, and stealthy menace.
  * Encourage GMs to adapt descriptions to their campaign tone and player group.

## III. Core Stat Guidelines & Benchmarks

These guidelines promote consistency. A monster's concept and **Monstrous Traits** may justify stats outside these ranges.

* **`size` (Physical Scale):** Defines physical dimensions. Often correlates with other stats (larger = more wounds/DR, lower TD, potentially higher speed; smaller = fewer wounds/DR, potentially higher TD).
  * *Size Category Examples:* Tiny (Cat), Small (Goblin), Gnome, Medium (Human), Wolf, Large (Bear), Ogre, Huge (Giant), Elephant, Gargantuan (Kraken).
  * *Note:* Use Size as a starting point; traits can override correlations.

* **`combat_pool` (Dice Pool):** Base d6s for most actions. Represents general competence.
  * *Benchmarks:* Minion 2-4d6 (~31-52% chance ≥1 success); Standard 4-6d6 (~52-67%); Elite 6-8d6 (~67-77%); Boss 8-10+d6 (~77-84%+).
  * *Note:* Chance of multiple successes increases significantly with pool size, potentially generating extra successes to spend on `extra_success_effects` defined for specific actions.
  * *Group Attacks:* When multiple similar monsters coordinate an action (like an attack) against a single target, their `combat_pool` dice may be combined for the action roll. However, the base `damage`, `pierce`, and `force` typically do not increase unless specified by a trait. This increases success chance and potential for spending extra successes on defined `extra_success_effects`, but not raw hit impact.
* **Weaknesses & Vulnerabilities (Derived Action Penalties):** To add mechanical depth and reflect a monster's specific flaws or limitations without needing full attribute scores, monsters may have defined `weaknesses` or `vulnerabilities`. These represent areas where the monster is inherently less capable, tied to standard Derived Action names (RES, FRT, PHY, FIN, PRC, FCS, INS).
  * **Assigning Weaknesses/Vulnerabilities:** Consider the monster's core concept, physical form, mental state, or lore.
  * **Weaknesses** typically represent minor hindrances or areas of lesser natural aptitude (e.g., a brute Ogre might have a FIN Weakness due to clumsiness; a non-perceptive creature might have a PRC Weakness). They impose a modest penalty.
  * **Vulnerabilities** represent significant flaws, situational disadvantages, or thematic Achilles' heels (e.g., a creature terrified of fire might have a RES Vulnerability against fear effects caused by fire; a heavily armored but slow creature might have a FIN Vulnerability for dodging). They impose a substantial penalty, reflecting a major point of potential exploitation. The "half pool" mechanic is used to ensure the penalty scales proportionally with the monster's base competence.
  * **`weaknesses: [ACTION1, ACTION2]`**: When a check requires a Derived Action listed here, the monster rolls its `combat_pool` **minus 2 dice** (minimum 1 die). Represents a minor hindrance.
  * **`vulnerabilities: [ACTION3, ACTION4]`**: When a check requires a Derived Action listed here, the monster rolls its `combat_pool` **minus 4 dice** (minimum 1 die). Represents a significant flaw.
  * **Default Roll:** If a check requires a Derived Action *not* listed under `weaknesses` or `vulnerabilities`, the monster rolls its full `combat_pool`.
  * This system maintains GM simplicity while leveraging existing game terminology for specific checks when needed, allowing targeted challenges against a monster's defined weak points.

* **`action_points` (AP):** Actions per round (PCs have 4).
  * *Benchmarks:* Slow/Simple 2 AP; Standard 3 AP; Agile/Elite 4 AP; Boss 5+ AP.

* **`speed` (Movement):** Hexes/units per AP (Avg Human = 4). Includes special types like Climb, Fly (e.g., `4 (Climb 4)`).
  * *Benchmarks:* Slow 2-3; Average 4; Fast 5-6; Swift 7+.

* **`wounds` (Wound Threshold Pools):** Tiered pools (`minor`, `major`, `grievous`, `deadly`) tracking resilience. Effective penetrating damage determines tier (1=Minor, 2=Major, 3=Grievous, 4+=Deadly). Wounds "upgrade" if a lower tier pool is full. Defeat occurs when a wound is taken for a tier with no capacity. See `[[../Wounds/_Wounds_README]]`.
  * *Benchmarks (Total effective wounds):* Fragile 1-2 Minor; Standard 3-4 (incl. Major); Tough 4-6 (incl. Grievous); Very Tough 6+ (incl. Grievous/Deadly).
  * *Note:* Adjust based on role (tank vs. glass cannon).

* **`target_difficulty` (TD):** Adds Difficulty Dice to attackers. Represents evasiveness/size.
  * *Benchmarks:* Easy Target TD 0; Average TD 1; Agile/Small TD 2; Very Hard TD 3+ (often via Traits).
  * *Note:* High base TD can be frustrating; consider traits that interact with it.

* **`dr` (Damage Reduction):** Reduces damage. Represents hide/armor (PC range ~1-5+).
  * *Benchmarks:* Unarmored DR 0; Light/Hide DR 1-2; Medium/Natural DR 3-4; Heavy DR 5+.
  * *Note:* High DR + high Wounds = damage sponge; consider vulnerabilities.

## IV. Monster Attack Damage Scale

A standardized damage scale is used for monster attacks to ensure consistency and balance across different creature types. This scale is calibrated against typical Player Character Damage Reduction (DR) and Wound Roll mechanics to provide predictable outcomes and appropriate threat levels.

**Approved Damage Scale:**

* Level 1 (Nuisance): Damage 3
* Level 2 (Minor): Damage 4
* Level 3 (Moderate): Damage 5
* Level 4 (Serious): Damage 6
* Level 5 (Severe): Damage 7
* Level 6 (Extreme): Damage 8

**Guidelines for Choosing a Damage Level:**

When assigning a damage level to a monster's attack, consider the following:

* **Monster's Role and Threat Level:** A low-level minion might use Level 1 or 2, while a boss monster could use Level 5 or 6.
* **Attack's Nature:** A bite or claw attack might be lower level, while a powerful slam or magical blast could be higher.
* **Frequency of Attack:** Attacks that can be made multiple times per round or as part of a group attack might be lower level than a single, powerful strike.
* **Synergy with Traits:** Consider how the damage level interacts with the monster's other abilities and traits.

**Considering Penetration and Force:**

While the damage scale provides a base value, **Penetration** and **Force** are crucial modifiers that should be considered when designing monster attacks.

* **Penetration:** Represents the attack's ability to bypass or ignore armor and Damage Reduction (DR).
  * Attacks with high Penetration are more likely to inflict higher-tier wounds, even with a lower base damage level, as they reduce the target's effective DR.
  * Designers should assign a Penetration value to attacks based on their nature (e.g., piercing weapons, magical effects). This value is applied *before* the damage is reduced by the target's DR.
  * *Example:* A monster with a "Venomous Bite" might have Damage Level 3 but Penetration 2, making it effective against lightly armored targets.
* **Force:** Represents the physical impact or concussive power of an attack, often leading to effects like knockback, stagger, or being knocked Prone.
  * Force is typically handled separately from the base damage and Penetration. It can be represented by:
    * A specific `force` value on the action, which might interact with target stats (like Resilience or Stability) to determine secondary effects.
    * An `extra_success_effect` tied to the attack action, allowing the monster to spend extra successes to trigger a Force-related effect (e.g., "Spend 2 extra successes: Target is knocked Prone").
    * A **Monstrous Trait** that triggers Force-related effects under certain conditions (e.g., "Whenever this monster hits a target with a melee attack, the target must make a Resilience check or be pushed back 1 hex").
  * Designers should decide whether Force is an inherent property of the attack (a `force` value), a potential outcome of a successful hit (`extra_success_effect`), or a passive ability (`Monstrous Trait`).

By considering the base Damage Level alongside appropriate Penetration and Force mechanics, designers can create monster attacks that are both balanced and thematic.

## V. Creating New Monsters

1. Start with concept and theme.
2. Define primary role (damage, disruptor, tank, etc.).
3. Assign core stats using guidelines, considering size correlations.
4. Develop 1-3 key **Monstrous Traits** that fulfill the "Truly Monstrous" philosophy (rule interaction, unique capabilities).
5. Define standard actions, tactics, flavor text, and loot.
6. Review: Is it distinct? Challenging? Manageable?
7. **Assess Relative Threat:** Evaluate overall challenge based on stats/traits synergy (for GM guidance).

## VI. Examples

Refer to existing monster files for implementation examples:

* [[Goblin Kinder]] (Minion example)
* [[Huldrekall]] (Elite/Specialist example)

## VII. Monster Categories

Here are the defined categories for monsters, providing a framework for their thematic and mechanical traits:

### Fae

Creatures of the Fae are tied to the mystical and often capricious realms beyond the mundane. They operate by strange rules and possess abilities that defy conventional understanding, often interacting with emotions, promises, and the natural world in unsettling ways.

### Aetherborn

Creatures in this category are those that have been significantly influenced, mutated, or created by the increased presence and volatility of the Aether since the Great Vanishing. They are often unpredictable, their forms and abilities warped by raw magical energy. This can range from subtle physical changes to complete transformations into beings of pure Aetheric force. Their connection to the Aether makes them susceptible to certain forms of counter-magic but also potentially resistant to non-magical attacks.

### Primordials

This category encompasses creatures that existed in the world before the arrival of the Garden Born (Alfari and Erdari). They are often tied to the raw, untamed forces of the natural world and the powerful primal spirits or gods of that era. Primordials can range from colossal beasts to elemental entities or ancient, powerful beings that predate recorded history. They represent the wild, untamed aspects of the world that the Garden Born sought to "tame."

## VIII. Overwhelm Effects

Monsters can possess powerful secondary effects or actions that are triggered when their standard actions achieve a high number of successes on the dice roll. Unlike `extra_success_effects` which are tied to specific actions, Overwhelm Effects are general capabilities that can be triggered by *any* action roll that meets the required success threshold. This provides a way for monsters to have impactful, high-moment effects tied to their core mechanics without relying on separate action pools or resources.

* **Trigger:** An Overwhelm Effect is triggered when a monster achieves a specific Success threshold (e.g., 3+, 4+) on *any* standard action's dice roll (attack, skill, etc.). The required threshold is defined per monster and per Overwhelm Effect.
* **Activation:** The Overwhelm Effect triggers immediately upon resolving the action that met the success threshold, unless otherwise specified in the monster's stat block.
* **No Additional Cost:** Using an Overwhelm Effect does not require spending additional Action Points or utilizing monster resources like Stamina or Will.
* **Frequency:** Overwhelm Effects can trigger whenever the specified success threshold is met on a relevant action roll. There are no per-encounter or per-session limits on their use.
* **Potency:** Overwhelm Effects are intended to be significantly more impactful than standard `extra_success_effects`, representing a moment where the monster's raw power or nature overwhelms defenses or the environment.

Examples of Overwhelm Effects:

* When an Ogre achieves 4+ successes on an action roll, it might cause the ground to shake, potentially knocking all nearby targets Prone.
* When a Huldrekall achieves 3+ successes on an action roll, it might cause a wave of debilitating fear to wash over all targets within a radius.
* When a Dire Wolf achieves 2+ successes on an action roll, it might allow the wolf to immediately make a free attack against a nearby target.

## IX. Limiting Powerful Abilities

While Monstrous Traits and Actions can grant powerful capabilities, it's often necessary to include limitations to ensure balance and provide players with counterplay opportunities. Here are several mechanics that can be used to limit the frequency or circumstances under which a monster can use a powerful ability:

### Conditional Triggers

The ability can only be used when specific, observable conditions in the game state are met. These conditions should be clear to the players through description or mechanics.

* **Description:** Ability use is tied to specific, observable game states (e.g., monster's wound level, presence of terrain features, preceding actions).
* **Example:** A creature's teleport ability might only be usable while it is within a shadow or if it has taken damage in the current round.

### Consequence / Backlash

Using the ability incurs an immediate negative effect on the monster, creating a risk-reward dynamic.

* **Description:** Using the ability incurs an immediate negative effect on the monster (e.g., self-damage, temporary penalty/Weakness, reduced defense).
* **Example:** A monster's powerful energy blast might cause it to take self-damage and gain a temporary Vulnerability to energy attacks.

### Charge-Up / Build-Up

The ability requires one or more preparatory actions using AP in preceding turns or earlier in the current turn, making its use telegraphed and allowing players to react.

* **Description:** The ability requires one or more preparatory actions using AP in preceding turns or earlier in the current turn.
* **Example:** A dragon's fiery breath might require spending an AP on the previous turn to "inhale" or gather energy.

### Success-Based Recharge

The ability becomes available again only after the monster achieves a specific number of successes on a different, specified type of action roll, encouraging the monster to engage in other actions.

* **Description:** The ability becomes available again only after the monster achieves a specific number of successes on a different, specified type of action roll.
* **Example:** A monster's venom spit attack might only recharge after it achieves 2 or more successes on a grapple attack.

## X. Monsters in Combat

### Initiative & Action Declaration

Sellsword uses an initiativeless system focused on simultaneous declaration and GM-guided resolution:

1. **Declarative Phase:** The GM first declares the intended actions for all monsters and NPCs. Then, players declare their actions.
2. **Active Phase:** The GM resolves all declared actions in an order that makes narrative and mechanical sense, typically resolving lower AP cost actions first. Within an AP cost tier, the suggested order is Ranged Attacks -> Melee Attacks -> Movement -> Complex Actions/Magic. The GM has the final say on resolution order.

### Group Tactics & Mob Attacks

Some monsters, particularly minions, are designed to fight in groups.

* **Combining Pools:** As noted under `combat_pool`, multiple similar monsters acting in concert against the same challenge or target may combine their dice pools for **any appropriate check** (e.g., a group RES check for morale, a group PRC check for perception). This increases success chance and potential for spending extra successes on defined `extra_success_effects` defined for specific actions, but does not increase base damage/effects for attacks. When pools are combined for a group check, the group makes a **single roll**, and the outcome (success or failure, including any extra successes or consequences) applies **collectively** to all participating monsters in that group action.
* **Traits:** Traits like `Swarm Tactics` often interact with group formations.

### Spending Extra Successes

Monsters can generate extra successes on their action rolls (rolling more '6's than needed for a basic success or to overcome TD/Difficulty). These extra successes can be spent by the GM to activate additional effects defined within the specific action used.

* **Source:** Available effects are listed under the `extra_success_effects` field for each action in the monster's stat block.
* **Spending:** Each effect has a `cost` in extra successes. The GM can choose which available effects to purchase, up to the total number of extra successes generated.
* **Examples:** Effects might include dealing extra damage, inflicting conditions, gaining momentum, or causing unique thematic results like knocking a target prone.
* **Default:** If an action generates extra successes but has no `extra_success_effects` defined, the GM can typically choose to apply narrative effects appropriate to the situation and monster type, or potentially inflict +1 Damage per extra success if the attack allows (e.g., Pierce >= DR), subject to GM discretion.

## Reach Calculation Guidelines

When determining a monster's reach for attacks:

1. **Base Reach by Size:**
   * Tiny: 0
   * Small: 0
   * Medium: 1
   * Large: 2
   * Huge: 3
   * Gargantuan: 4

2. **Total Reach = Base Reach (Size) + Weapon Reach**
   * Unarmed/Natural attacks use the creature's base reach
   * Weapons add their reach value (typically 0-2)

Example for a Large creature (base reach 2):

* Unarmed attack: Reach 2
* Sword (reach 1): Total reach 3
* Spear (reach 2): Total reach 4

This ensures monsters with larger size categories properly threaten a greater area in combat, reflecting their physical advantage.

## Loot Guidelines

When specifying monster loot:

1. **Anatomical Parts:** Don't quantify body parts with dice rolls. If a monster has ears, tusks, or hide, simply list them without quantities.
2. **Crafting Materials:** Note crafting potential without specifying amounts (e.g., "Hide can be crafted into crude armor with DR 2").
3. **Non-anatomical Loot:** Use only d6 dice notation for currency, gems, or other collectibles (e.g., "2d6 Silver coins").
4. **Dice Notation:** Always use standard d6 notation (e.g., "2d6" not "1d6+1d6").

Example:

```yaml
loot: |
  Crude Teeth, Tusks, Hide (can be crafted into armor), 2d6 Silver coins, 1d6 Gemstones
```

## XI. Monster Danger Rating System

The Danger Rating system provides GMs with a consistent way to gauge monster threat levels and build appropriate encounters. Each monster has calculated offensive and defensive ratings that combine into an overall danger score, along with role classifications to guide tactical usage.

### Danger Rating Calculation

#### Offensive Rating (OR)

```text
OR = (Base Combat Effectiveness) + (Action Economy) + (Damage Potential) + (Special Offensive Traits)

Where:
* Base Combat Effectiveness = combat_pool × 1.5
* Action Economy = action_points × 2
* Damage Potential = highest_damage + highest_pierce + (force/2)
* Special Offensive Traits = Sum of trait values based on impact:
  * Minor trait (situational benefit): +1
  * Standard trait (consistent benefit): +2
  * Major trait (game-changing): +3
  * Overwhelm effects typically count as standard (+2) or major (+3)
```

#### Defensive Rating (DR)

```text
DR = (Durability) + (Avoidance) + (Damage Reduction) + (Special Defensive Traits)

Where:
* Durability = total_wound_points × 2 (minor=1, major=2, grievous=3, deadly=4)
* Avoidance = target_difficulty × 3
* Damage Reduction = (body_dr × 2) + voids_dr
* Special Defensive Traits = Sum of trait values based on impact:
  * Minor trait (situational protection): +1
  * Standard trait (consistent protection): +2
  * Major trait (immunity or significant damage reduction): +3
```

#### Overall Danger Rating (ODR)

```text
ODR = (OR + DR) / 2
```

### Danger Tiers

The numerical Overall Danger Rating maps to these tiers:

* **Tier 1 (Nuisance)**: 1-5 points
  * Threat to untrained civilians
  * Use 3-4 per character for a challenge
  * Good for showing character competence

* **Tier 2 (Hazardous)**: 6-10 points
  * Dangerous to unprepared adventurers
  * Use 2 per character for a moderate challenge
  * Good for resource attrition

* **Tier 3 (Formidable)**: 11-15 points
  * Significant threat requiring tactics
  * Use 1 per character for a balanced encounter
  * Good for standard combat challenges

* **Tier 4 (Dangerous)**: 16-20 points
  * Major threat requiring preparation
  * Use 1 per 2 characters for a serious challenge
  * Good for climactic encounters

* **Tier 5 (Lethal)**: 21+ points
  * Extreme danger, potentially campaign-defining
  * Use 1 per 4 characters or as solo encounters
  * Good for boss fights and major story moments

### Combat Roles

Each monster is assigned a primary role and optionally a secondary role to guide tactical usage:

* **Striker**: Focuses on dealing high damage to single targets
* **Brute**: High durability with moderate damage output
* **Controller**: Manipulates battlefield conditions and movement
* **Ambusher**: Specializes in surprise and initial advantage
* **Swarm**: Relies on numbers and coordinated attacks
* **Support**: Enhances allies or debuffs enemies
* **Elite**: Well-rounded with multiple capabilities
* **Titan**: Exceptional in multiple categories, often with environmental effects

### Implementation in Monster Files

The danger rating appears in monster files as:

```yaml
danger_rating:
  offensive: 26
  defensive: 20
  overall: 23
  tier: 5 # Lethal
  primary_role: Striker
  secondary_role: Ambusher
  recommended_encounter: "1 per 2 characters, or 3-4 for a full party"
```
