# Big Takeaways
Action die work as intended but giving back action die after the end of each players turns promotes burst interactions. Potential fix to this is to make a more limited set of actions that can be done when it's not your turn.

Making move an action die unduly promotes stagnation. DC20 deals with this by giving disADV to repeat attacks. Multiple attacks in TEST are already at a disadvantage due to using less action die so I don't want to do that. Possible fix is just granting movement every turn, free of using an AD. The other idea is to make a maneuver that is possibly less move but also does something else

Some confusion in regards to what players can do. Question about having a set of maneuvers front and center that present players with best options. I think the solutions is to categorize maneuvers by ones they can do on their turn, and ones they can do off their turn

In regards to Maneuvers definitely need to provide some basic maneuvers for action die but one thing to differentiate between players is to give SPECIAL maneuvers by class that can do cooler things. maybe have better modifiers or outcomes.

The math was not tuned very well but need to make it such that minions are hittable on average by striker characters with only 1 AD and non strikers on 2 AD. Ajlso tune it so that minions by themselves are extremely unlikely to hit a character. A possible solution for this is making it so that it is essentially impossible for a single minion to hit a relatively fast character with only 1 AD but introduce critical hits on 6's by making them explode