## Uploaded my google sheet as text

## Gemini
Okay, I've processed the `sellsword_weapons.txt` file. It's a great, detailed list covering weapons, ammo, shields, and armor with lots of characteristics!

To make sure I understand how these stats integrate with the game mechanics we've been discussing (Derived Actions, Skills, Difficulty/Complexity, Stunts, Wounds, etc.), I have a few clarifying questions:

**1. Core Gear Mechanic & Ratings:**

- **Ratings to <PERSON>ce:** You mentioned previously that gear ratings (like <PERSON><PERSON>, <PERSON>, Block, Parry - typically 1-3?) add dice to the relevant action pool. Is it always a direct +X dice based on the rating? (e.g., Attacking with a weapon with Damage 2 adds +2 dice to the attack roll pool? Blocking with a shield with Block 3 adds +3 dice to the defensive roll pool?)
- **Damage Calculation:** This is still the biggest question. After a successful hit (meeting the 1 success minimum, or more if Complex), how is the actual damage determined before applying armor/Fortitude?
    - Is it simply equal to the weapon's `Damage` rating? (e.g., Damage 2 weapon always deals 2 base damage?)
    - Is it related to the number of successes rolled on the attack?
    - Is it the `Damage` rating plus extra successes (Stunts)?
    - The `Stunt 1` column sometimes lists damage effects ("upgrade wound severity"). How does this interact with the base damage?

**2. Column Definitions & Mechanics:**

- **`Carry`:** Does this number represent the item's "weight" or "bulk" that counts against the character's `Carry` derived stat (which is `3 + STR`)? For example, does carrying a Spear (Carry 2) and a Heater Shield (Carry 5) use up 7 points of a character's Carry capacity?
- **`Pnts`:** What does this column represent? Is it a point value for purchasing gear, a measure of encumbrance separate from `Carry`, or something else?
- **`STR`:** Is this the minimum STR attribute required to wield the item effectively without penalty?
- **`Durability`:** How does this mechanic work? Do items take damage or break? When is this rating checked?
- **`Reach` / `Range`:** `Reach` seems clear (melee range in hexes). For `Range`, is this the maximum effective range? Are there penalties for shooting at longer ranges within this maximum?
- **`Cover`:** This appears mainly on shields. How does this value function mechanically? Does it add Difficulty dice to attacks against the wielder? Does it provide a form of damage reduction?
- **`Force`:** What kind of actions does this rating apply to? Is it for pushing opponents back, sundering objects/armor, or something else?
- **`Hook`:** Similar question – what actions use this rating? Tripping, disarming, pulling enemies off balance?
- **`Body` / `Voids` (Armor & Shields):** These look like Armor Value (AV) ratings. How do they work against incoming damage?
    - Do they subtract directly from the calculated damage number? (e.g., Damage 3 hit vs Body 5 = 0 damage?)
    - Do they subtract successes from the attack roll _after_ it hits?
    - Do they add Difficulty dice to attacks targeting that location? (_Values seem high for direct subtraction against low base weapon damages_).

**3. Weapon/Shield Stunts:**

- The `Stunt 1` / `Stunt 2` columns list specific effects (e.g., "Disarm opponent," "Put opponent off balance," "Remove 1 level of armor"). Are these Stunts that are _unlocked_ specifically by using this weapon/shield? Do they still cost 1 extra success to activate like normal Stunts, or are they triggered differently?

**4. Ammunition:**

- The Ammunition section lists ratings (like Damage, Pierce). How do these interact with the base stats of the bow/crossbow? Does firing a Bodkin arrow (Pierce 2) from a Medium Recurve Bow (Damage 2) change the attack's stats? Does the ammo override the bow's stats, add to them, or use a different mechanic?

**5. Exotics:**

- Do exotic weapons listed require specific Tier 3 Focus skills to use effectively? Do they use the standard Derived Actions or have unique rules associated with them?

Answering these will help solidify how gear functions mechanically within the system we've built! It's a fantastic list with a lot of evocative potential.

## Response
**1. Core Gear Mechanic & Ratings:**

- **Ratings to Dice:** You mentioned previously that gear ratings (like Damage, Pierce, Block, Parry - typically 1-3?) add dice to the relevant action pool. Is it always a direct +X dice based on the rating? (e.g., Attacking with a weapon with Damage 2 adds +2 dice to the attack roll pool? Blocking with a shield with Block 3 adds +3 dice to the defensive roll pool?)

Yes this is correct. the idea is that each parameter for an item is rated 1-3 dictating how many 'gear' dice are added to the pool in the relevant sitation.

- **Damage Calculation:** This is still the biggest question. After a successful hit (meeting the 1 success minimum, or more if Complex), how is the actual damage determined before applying armor/Fortitude?
    - Is it simply equal to the weapon's `Damage` rating? (e.g., Damage 2 weapon always deals 2 base damage?)
    - Is it related to the number of successes rolled on the attack?
    - Is it the `Damage` rating plus extra successes (Stunts)?
    - The `Stunt 1` column sometimes lists damage effects ("upgrade wound severity"). How does this interact with the base damage?

Initially i had thought to sum the gear dice and let that be the weapon damage. For example if a dagger has damage 2, when attacking would add 2 gear dice to your pool, and then if you hit the result of those dice would be your damage (2d6). The armor of whatever you are attacking would then be subtracted and thats your damage. But now i am considering if I should just stick with the 1-3 value and just let the damage be straight. This would make combat faster, less math, and also i'd already planned on changing the armor values to match the same value system (1-3). This also allows me to seperate offense, or attack, from damage.  So now a dagger could have damage 2, amount of damage done with a successful hit before armor, and offense 1, amount of gear dice added to the dice pool when attacking.

The stunt column are ideas i had for allowing weapons to add stunts that players could do on top of the normal stunts they have access to.

**2. Column Definitions & Mechanics:**

- **`Carry`:** Does this number represent the item's "weight" or "bulk" that counts against the character's `Carry` derived stat (which is `3 + STR`)? For example, does carrying a Spear (Carry 2) and a Heater Shield (Carry 5) use up 7 points of a character's Carry capacity?

both. Carry is the amount of carry slots an item takes up and also represents the items speed, or action cost. I initially had them separate but noticed they largely correlated. If something is heavy or bulky, it is also typically slow to wield. Carry now will also be ranked from (1-3). Something no carry or 0, is essentially something that is palm sized or for better terms (weightless)

- **`Pnts`:** What does this column represent? Is it a point value for purchasing gear, a measure of encumbrance separate from `Carry`, or something else?

These pnts are just for casually comparing equipment. I had a formula that added bonuses and subtracted penalties. With the idea being that weapons of a class should largely be similar

- **`STR`:** Is this the minimum STR attribute required to wield the item effectively without penalty?

Yes. I am considering stripping this requirement but its still in there for the moment.

- **`Durability`:** How does this mechanic work? Do items take damage or break? When is this rating checked?

My current idea on durability is to not have a tracked value but instead have items 'break' on rolling 1's on the gear dice. With the durability being the amount of 1's required. I'd like to have maybe a two stage system where an item 'wears' first but am not sure how to do this yet without adding bookkeeping to the player and gm

- **`Reach` / `Range`:** `Reach` seems clear (melee range in hexes). For `Range`, is this the maximum effective range? Are there penalties for shooting at longer ranges within this maximum?

Reach is the amount of hexes 'added' to a players reach. So a standard humanoid has 1, large 2, small 0. So if a weapon has a reach of 0, small knife for ex, when wielded by a standard humanoid they'd have a reach of 1.

Range is a multiplier of Precision. That is the range up to which no penalties would be added. You can attack past this range but with penalties. So a character with Precision of 6, shooting a weapon with range 5, would be able to attack without penalty up to 30 hexes

- **`Cover`:** This appears mainly on shields. How does this value function mechanically? Does it add Difficulty dice to attacks against the wielder? Does it provide a form of damage reduction?

Cover adds difficulty to attackers, subtracts from dice pool, not damage reduction. it is a passive benefit. Not to be confused with 'Block' which is active protection.

- **`Force`:** What kind of actions does this rating apply to? Is it for pushing opponents back, sundering objects/armor, or something else?

Essentially. The idea of force is to allow players to wager or spend stamina up to a weapons force value and add that to damage or stunt with it. I am still unsure exactly how to accomplish this but like the idea. A dane axe has the same size cutting edge as a hand axe but do to its long haft can deliver much more force to an enemy. How do we capture that mechanically?

- **`Hook`:** Similar question – what actions use this rating? Tripping, disarming, pulling enemies off balance?

Yes hook is just a general term for manipulating your opponent with your weapon.

- **`Body` / `Voids` (Armor & Shields):** These look like Armor Value (AV) ratings. How do they work against incoming damage?
    - Do they subtract directly from the calculated damage number? (e.g., Damage 3 hit vs Body 5 = 0 damage?)
    - Do they subtract successes from the attack roll _after_ it hits?
    - Do they add Difficulty dice to attacks targeting that location? (_Values seem high for direct subtraction against low base weapon damages_).

As mentioned above Body and Voids armor values are just going to be 1-3 like everything else, and be a straight damage reduction. So if a weapon has damage 2 and hits an opponents body with Armor 2, no damage is done. These armor values have effect on dice pools whatsoever as i want to decouple the idea that armor somehow makes you harder to hit. On the contrary armors with high Armor Values will incur encumbrance and maybe fatigue slowing you down and making you easier to hit 


**3. Weapon/Shield Stunts:**

- The `Stunt 1` / `Stunt 2` columns list specific effects (e.g., "Disarm opponent," "Put opponent off balance," "Remove 1 level of armor"). Are these Stunts that are _unlocked_ specifically by using this weapon/shield? Do they still cost 1 extra success to activate like normal Stunts, or are they triggered differently?

Yes the idea here is to just add stunts to the available list a player would otherwise have. They aren't free. You would still have to spend a success to 'activate' them.

**4. Ammunition:**

- The Ammunition section lists ratings (like Damage, Pierce). How do these interact with the base stats of the bow/crossbow? Does firing a Bodkin arrow (Pierce 2) from a Medium Recurve Bow (Damage 2) change the attack's stats? Does the ammo override the bow's stats, add to them, or use a different mechanic?

This is somewhat outdated data. But the idea would be for the stats to 'add' to eachother. For example a bow would have no damage value, but a force value possibly. And the ammunition would provide the damage, pierce, stats. If we go to an attack value as mentioned above the bow would contain that parameter, meaning it would govern 'accuracy' amount of dice added to your attack pool, but not govern damage as that would depend on the ammunition.

**5. Exotics:**

- Do exotic weapons listed require specific Tier 3 Focus skills to use effectively? Do they use the standard Derived Actions or have unique rules associated with them?

Answering these will help solidify how gear functions mechanically within the system we've built! It's a fantastic list with a lot of evocative potential.

I haven't really looked at the exotics in a while. i'd just ignore those for the time being.