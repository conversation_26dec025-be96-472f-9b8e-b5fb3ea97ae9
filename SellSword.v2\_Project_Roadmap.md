# Sellsword TTRPG - Project Roadmap (Preliminary)

This document outlines potential next steps and areas for development based on the current state (April 2025).

## I. Foundational Elements

*   [ ] **Worldbuilding:** Flesh out the setting details (geography, factions, history, culture, cosmology). This is crucial context for other systems.
    *   [ ] Update `World/_World_README.md` (currently placeholder).
    *   [ ] Create/populate specific world detail files.
*   [ ] **Core Rules Finalization:** Solidify mechanics marked as TBD or placeholder.
    *   [ ] Define risks for Stamina/Will push mechanic (rolling '1's).
    *   [ ] Finalize Fatigue/Strain maximum levels/caps.
    *   [ ] Detail precise timers/mechanics for Shock and Dying states.
    *   [ ] Define Armor Sundering rules.
    *   [ ] Expand `Rules/_Rules_README.md` with detailed Combat procedures (initiative, actions, targeting, etc.).
    *   [ ] Expand `Rules/_Rules_README.md` with detailed Exploration rules (travel, time, events, resource management like light/food).

## II. Second Pass Review & Refinement

Review and refine systems that have had a first pass, ensuring consistency, balance, and clarity.

*   [ ] **Attributes/Derived Stats/Actions:** Review calculations, ranges, and impact. (`Attributes/`)
*   [ ] **Skills:** Review tiers, dice bonuses, special maneuvers. (`Skills/`, `Features/` - as skills are under Features)
*   [ ] **Features:** Review balance, prerequisites, interactions. (`Features/`)
*   [ ] **Equipment:** Review stats, costs, availability, balance (especially weapons/armor DR). (`Equipment/`)
*   [ ] **Stunts:** Review cost, effect, and applicability. (`Stunts/`)
*   [ ] **Standing:** Review mechanics and integration with downtime/social encounters. (`Standing/`)
*   [ ] **Lore (Non-Magic):** Review knowledge domains and mechanics. (`Lore/`)
*   [ ] **Monsters:** Review stat blocks, unique traits, consistency. (`Monsters/`)
*   [X] **Wound System:** *Initial review and 4-tier restructuring complete (April 2025). Further balance/playtesting needed.* (`Wounds/`)
*   [ ] **Magic System:** *Second pass review previously done. Further balance/playtesting needed.* (`Lore/`, `Rules/Magic_Guide.md`)

## III. Playtesting

*   [ ] Conduct focused playtests of core mechanics (combat, skills, magic).
*   [ ] Conduct broader playtests simulating campaign play (exploration, social, combat).
*   [ ] Gather feedback and iterate on rules based on playtest results.

## IV. Future Goals / Advanced Features

*   [ ] **Programmatic Tools:** Explore creating tools (e.g., MCPs, scripts) to assist with content generation or validation, leveraging the markdown/YAML structure (Proof-of-concept?).
*   [ ] **Character Sheet:** Design/develop a character sheet (digital or printable).
*   [ ] **Introductory Scenario:** Create a starting adventure/scenario.
