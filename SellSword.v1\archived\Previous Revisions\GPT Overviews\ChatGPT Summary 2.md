## Character Creation

### Attributes and Derived Stats
- **Prime Attributes**: Characters are built around four main attributes: Strength, Spirit, Agility, and Intelligence, each affecting various aspects of gameplay. Players allocate points to these attributes using a point buy system. Humans have a flexible point allocation, while specialized races like elves have bonuses and penalties to specific attributes.
- **Derived Stats**: These are calculated as averages of two related prime attributes. They are:
  - **Physique**: Average of Strength and Agility, affects encumbrance and physical skills.
  - **Focus**: Average of Spirit and Intelligence, used for spellcasting and complex mental tasks.
  - **Finesse**: Average of Agility and Intelligence, key for precision tasks and rogue-like combat.
  - **Fortitude**: Average of Strength and Spirit, used for endurance and injury resistance.

### Racial Traits
- Each race has a unique bonus to a derived stat and a penalty to a prime attribute, reflecting their cultural and biological predispositions.

## Equipment
- **Armor**: Provides Damage Reduction (DR) and adds to encumbrance (ENC). ENC is calculated by subtracting the weight of the equipment from Strength.
- **Weapons**: Offer offensive (Off) and defensive (Def) bonuses and have traits such as range and damage type. A longsword, for example, might offer +2 Off and +1 Def, with a reach of 1 and capable of both slashing and piercing damage.

### Encumbrance Example
- A character with a Strength of 3 wearing a cuirass (weight 2) and carrying a longsword (weight 1) would have an ENC calculation of 3 (Strength) - 3 (total equipment weight) = 0 ENC. 

## Combat System

### Action Dice
- Each character has four six-sided Action Dice (AD) per round.
- **Performing Actions**: Whenever a character performs an action, they roll an AD. The number rolled does not affect success but represents the action's expenditure.
- **Action Economy**: Actions such as attacking, defending, or reacting consume AD. Unused AD carry over for reactions.

### Active Offense and Defense
- **Active Offense (AO)**: Costs 2 AD, used for making deliberate, forceful attacks.
- **Active Defense (AD)**: Also costs 2 AD, used for consciously defending against an attack. If a player opts not to use AD, the attacker goes against the character's passive defense.

### Reactions
- Players can spend 1 AD for immediate responses, such as:
  - **Dodge**: Avoid an incoming attack.
  - **Shield Block**: Use a shield to deflect an attack.
  - **Harass**: Perform a light attack on an enemy moving within reach.

### Wound System
- **Damage and Wounds**: When a character takes damage exceeding their DR, the remainder is applied as a potential wound. For example, if a character with a DR of 4 takes 6 damage, they receive 2 points of potential wound.
- **Calculating Wounds**: The potential wound is modified by Fortitude. If a character's Fortitude is 1, they subtract 1 from the potential wound, rolling on a 2d6 wound table with this modifier.

### Critical Hits
- **Exploding Dice**: Rolling a 6 on an AD allows for an additional roll, adding to the action's effectiveness.
  
### Recovery
- **Healing**: After combat, characters must address their wounds. The severity of wounds can vary, influencing how quickly a character can recover and return to full strength.

## Examples in Gameplay

### Combat Example
- A rogue with high Finesse and Agility opts not to attack but reserves their AD for reactions. An enemy swings, and the rogue spends 1 AD to dodge, relying on their high passive defense to avoid less serious threats.

### Equipment Example
- A warrior with heavy armor (high DR) decides to make an Active Offense with a great sword. They use 2 AD for the attack, leaving 2 AD for potential Active Defense or other reactions.

This primer aims to encapsulate the core mechanics of your RPG system, highlighting the interplay between character attributes, equipment management, and a nuanced combat system that rewards strategic thinking and planning. It should serve as a comprehensive guide for new players and a quick reference for those familiar with the game.