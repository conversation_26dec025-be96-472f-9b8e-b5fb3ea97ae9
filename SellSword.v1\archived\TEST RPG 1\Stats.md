Very compressed compared to dnd 5e. Taken mostly from DC20. I don't really like the prime attribute idea though I do like the goals behind it. Stats go from -10 to 10, not from 1 - 20 and your stat is your modifier. Probably reward 1 stat boost per lvl? Allow buying positive stats at character creation with negative?

## Attributes
The 4 prime attributes are what you would assign points to at character creation:
Strength
	Governs what equipment you can use effectively, essentially strength requirements need to be met. Also adds 1 to 1 dmg per point
Agility
	Determines your combat effectiveness. This modifier is added to your "engagement" roll. It also determines your avoidance
Intelligence
	pretty self explanatory but mechanically would probably govern the power of your spells. Or another thought would be to somehow link intelligence to the focus points?
Presence, Wisdom?, Wit?
	Not sure on name but essentially reflects your force of character. Not charisma though. A high presence character would draw attention when entering a room but wouldn't be necessarily positive. People listen when they speak kind of thing.

These attributes would be calculated based on your prime attributes:
HP or Stamina = 5 + Strength + Agility
Will = 5 + Intelligence + Presence

### Point Buy
Maybe 6 points at start. No single stat greater than 3?

Ex lvl 1 character

Strength - 3
Agility - 2
Intelligence - 0
Presence - 1
HP - 10
Will - 6
## Features
These are more tailored traits that can be granted either by your choice of ancestry or background. I think these would be an a la cart sort of thing that would be point based that players could choose from at character creation with ancestry and background each providing separate pools to choose from. This is where you would get things like "charismatic" or "attractive", that would benefit your skill rolls in specific mechanical ways.

These are definitely not tied to class but more a way for players to hone in on what "fantasy" they are trying to replicate

For example the elf background might allow you to take the "Nimble" feature. which grants +1, or +2 to agility and then maybe some other flavor buff like advantage on Agility checks. This is pretty powerful but I want the features to be fairly robust to feel rewarding for taking and specializing. they could also be balanced by requiring negatives to attributes also?

