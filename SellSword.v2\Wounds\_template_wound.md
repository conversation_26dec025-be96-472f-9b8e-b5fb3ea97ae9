---
name: # Generic name of the wound type (e.g., <PERSON><PERSON>, Head Trauma, Laceration)
type: Wound
location: # Body | Voids | Vitals (Primary location where this wound type occurs)
tags: [wound, body | voids | vitals] # Add relevant location tag

# Severity Tier effects are listed below. The specific Tier applied is determined by the Wound Roll result consulting the Wound Table in _Wounds_README.md.

# --- Tier 1 (Minor) ---
# Goal: MUST apply temporary inconveniences, status effects, or minor penalties to Derived Stats.
# Examples: Gain Fatigue/Strain 1, Bleed 1, -1 Speed, -1 Carry, minor situational penalties (-1 die to specific actions like climbing/jumping if relevant).
tier_1_effect: # Mechanical effect for Tier 1 (Minor) severity. MUST primarily affect Derived Stats or apply Statuses.
tier_1_recovery: # Recovery notes for Tier 1 (Minor) severity (Typically Short Rest or basic First Aid)

# --- Tier 2 (Major) ---
# Goal: MUST apply significant penalties directly to relevant Derived Action rolls.
# Examples: -X dice penalty to PHY, FIN, FRT, PRC, FCS, RES, or INS rolls (typically -1 or -2). May also include Tier 1 effects like Fatigue/Strain/Bleeding.
tier_2_effect: # Mechanical effect for Tier 2 (Major) severity. MUST primarily affect Derived Action rolls.
tier_2_recovery: # Recovery notes for Tier 2 (Major) severity (Typically requires Extended Rest, possibly Physik checks/supplies)

# --- Tier 3 (Severe) ---
# Goal: MUST apply penalties directly to Base Attributes, impacting derived stats/actions broadly.
# Examples: -1 STR, -1 AGI, -1 END, -1 CON, etc. May also include Tier 1/2 effects. Often involves significant recovery time/checks.
tier_3_effect: # Mechanical effect for Tier 3 (Severe) severity. MUST primarily affect Base Attributes.
tier_3_recovery: # Recovery notes for Tier 3 (Severe) severity (Typically requires significant downtime, advanced Physik checks, risk of complications)

# --- Tier 4 (Grave) ---
# Goal: Represent immediately debilitating, potentially fatal, or permanently crippling consequences requiring urgent attention.
# Examples: Unconsciousness (KO), Dying state (requires stabilization), Limb Loss, Permanent Attribute Loss (-1 STR permanently), Sensory Loss (Blindness/Deafness), conditions requiring immediate intervention to prevent death (e.g., Massive Hemorrhage, Organ Failure).
tier_4_effect: # Mechanical effect for Tier 4 (Grave) severity (Often results in immediate incapacitation/death or is listed under Permanent Effects section below)
tier_4_recovery: # Recovery notes for Tier 4 (Grave) severity (Often involves stabilization, adaptation, or is simply fatal)

# --- Permanent Effects & Description ---
# Note: Grave (Tier 4) effects often manifest as permanent effects or immediate incapacitation/death. Tier 3 may also have a *chance* of lasting effects.
permanent_effect_chance: # Optional: Describe potential lasting issues from Tier 3/4 (e.g., "Possible -1 END", "Limp", "Blindness") - Use 'null' if none.
description: # Short flavor text description of the general injury type
---

# {{name}}

**Location:** {{location}}

**(Detailed description of the injury, common causes, visual cues, potential complications.)**

---
### Tier 1 (Minor) Effect
*   **Effect:** {{tier_1_effect}}
*   **Recovery:** {{tier_1_recovery}}

### Tier 2 (Major) Effect
*   **Effect:** {{tier_2_effect}}
*   **Recovery:** {{tier_2_recovery}}

### Tier 3 (Severe) Effect
*   **Effect:** {{tier_3_effect}}
*   **Recovery:** {{tier_3_recovery}}

### Tier 4 (Grave) Effect
*   **Effect:** {{tier_4_effect}}
*   **Recovery:** {{tier_4_recovery}}

{% if permanent_effect_chance and permanent_effect_chance != 'null' %}
*   **Permanent Effects:** {{permanent_effect_chance}}
{% endif %}
