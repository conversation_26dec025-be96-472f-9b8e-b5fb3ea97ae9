# Wounds System Overview

This directory contains details on specific wounds characters might suffer. Sellsword uses a Wound System instead of abstract Hit Points to represent injury, emphasizing the specific consequences and recovery times associated with different types of trauma.

**IMPORTANT:** The detailed Wound Tables and specific wound effects described in this document and the individual wound files (`Body/`, `Voids/`, `Vitals/`) are primarily for **Player Characters (PCs)**. Opponents and Monsters are handled using a simpler Wound Threshold system (see below).

## Determining Wounds (Player Characters)

When a Player Character suffers Penetrating Damage (Damage exceeding Armor DR for the Hit Location), the severity of the resulting wound is determined as follows:

1. **Calculate Penetrating Damage:** Determine how much damage exceeded the location's DR. If 0 or less, no wound occurs.
2. **Stamina Mitigation (Optional Spend BEFORE Determination):** Before determining the wound severity, the character *may* spend **Stamina** to reduce the effective Penetrating Damage for this calculation. For each point of Stamina spent, reduce the Penetrating Damage by 1 (minimum effective Penetrating Damage of 0). This represents bracing, twisting away, or absorbing impact to lessen the severity of the blow.
3. **Determine Wound Severity & Roll Necessity:** Based on the **effective Penetrating Damage** after Stamina mitigation:
    * **Effective Penetrating Damage 1:** This results in a minor effect. **DO NOT make a Wound Roll.** The character gains **1 Fatigue** (for physical attacks) or **1 Strain** (for mental/magical attacks). This is a temporary inconvenience and does not require consulting the detailed Wound Tables.
    * **Effective Penetrating Damage 2+:** This requires a **Wound Roll**. Proceed to Step 4.
4. **Determine Wound Roll Dice (If Needed):** If a Wound Roll is required (Effective Penetrating Damage 2+), the number of d6 rolled equals the **effective Penetrating Damage**.
5. **Make Wound Roll & Determine Severity Tier:** Roll the Wound Dice and sum the total result. The **final Wound Roll total** determines the Wound Severity Tier:
    * **Roll Total < 7:** Minor (Tier 1) Wound
    * **Roll Total 7-9:** Major (Tier 2) Wound
    * **Roll Total 10-13:** Grievous (Tier 3) Wound
    * **Roll Total 14+:** Deadly/Debilitating (Tier 4) Wound
6. **GM Chooses Specific Wound & Apply Effect:** Consult the detailed table for the **Hit Location** (Body, Voids, or Vitals) found in the files below:
    * `[[_WoundTable_Body]]`
    * `[[_WoundTable_Voids]]`
    * `[[_WoundTable_Vitals]]`
    The GM **chooses the specific numbered row** within the determined severity range that best fits the narrative context of the attack (weapon type, impact). The chosen row indicates the specific **Wound Type** (`[[Location/Wound_Type]]`), its **Severity Tier**, the **Effect Summary**, and **Flavor Text**. Look up the indicated Wound Type file and apply the full effects listed for the determined Severity Tier.
7. **Armor Sundering:** Some wound results may indicate armor DR is reduced (details TBD in Equipment section).

## Determining Wounds (Opponents & Monsters)

Opponents and Monsters do not use the detailed Wound Tables or specific wound effects like Player Characters. Instead, they have a simpler **Wound Threshold** system to determine when they are defeated or significantly impaired.

* Monsters have pools of wounds they can absorb at different tiers (e.g., "2 Minor, 1 Major").
* When a monster suffers Penetrating Damage, determine the Wound Severity Tier based on the **effective Penetrating Damage** (after any monster-specific mitigation):
  * Effective Penetrating Damage 1: Counts as 1 Minor Wound.
  * Effective Penetrating Damage 2: Counts as 1 Major Wound.
  * Effective Penetrating Damage 3: Counts as 1 Grievous Wound.
  * Effective Penetrating Damage 4+: Counts as 1 Deadly/Debilitating Wound.
* Track the number of Minor, Major, Grievous, and Deadly/Debilitating wounds the monster has taken.
* When a monster receives a wound of a tier whose pool is empty, that wound is "upgraded" to the next tier for the purpose of depleting *that* pool. (e.g., if a monster has a "2 Minor" pool and receives a 3rd Minor wound, that 3rd wound counts as a Major wound).
* A monster is defeated when they receive a wound of a tier they have no capacity for. This occurs if their pools for that tier and all lower tiers are depleted, or if the incoming wound is of a tier higher than their highest capacity.

This system keeps monster combat fast and focused on dealing sufficient Penetrating Damage to reach key thresholds, without the GM needing to manage detailed wound effects or conditions for every enemy.

## Core Wound Types (Overview)

This section provides a general overview of the planned wound types for **Player Characters**. Each type has a dedicated file detailing its Tier 1 (Minor), Tier 2 (Major), Tier 3 (Severe), and Tier 4 (Grave) effects. The specific Tier applied is determined by the Wound Roll result on the location tables linked above.

## Common Conditions & Status Effects

See `[[_Wound_Statuses]]` for definitions of common conditions inflicted by wounds on **Player Characters**.
