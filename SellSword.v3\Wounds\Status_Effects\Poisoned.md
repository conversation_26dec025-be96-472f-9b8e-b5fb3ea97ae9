---
name: Poisoned
type: Status
category: Physical
tags: [status, physical, poison, toxin, infection, venom]

# Status Effect Details
description: "Toxins, infections, or venoms in the system causing ongoing debilitation."
mechanical_effect: "The character is considered 'compromised' for all actions until the poisoning is treated. This represents a general state of debilitation that affects all aspects of performance."
stacking: "Does not stack in intensity, but different types of poisons may have different durations or treatment requirements."
max_level: "No levels. Poisoned is a binary state (either poisoned or not)."

# Recovery Information
recovery_method: "Requires appropriate treatment based on the poison type (antidote, medicine, magical healing)."
recovery_difficulty: "Typically requires a Physik check (TD based on poison potency) or specific antidote/treatment."
recovery_time: "Varies by poison type. Some may resolve after treatment, others may require extended recovery time."
recovery_resources: "Specific antidotes, medicinal herbs, or magical remedies depending on the poison type."

# Additional Information
common_sources: "Venomous creatures, poisoned weapons, toxic plants, contaminated food/water, infected wounds."
related_statuses: "May cause Fatigue, Pain, or other effects depending on the specific poison."
special_interactions: "Characters with the Poison Resistance Feature may have advantage on recovery checks or reduced effects."
---

# Poisoned

**Category:** Physical

Toxins, infections, or venoms in the system causing ongoing debilitation. The Poisoned status represents any harmful substance circulating in a character's body, whether from venomous creatures, toxic plants, contaminated food/water, or infected wounds.

## Mechanical Effects

When a character is Poisoned:

* **Compromised State:** The character is considered "compromised" for all actions until the poisoning is treated. This represents a general state of debilitation that affects all aspects of performance.

* **Stacking:** Does not stack in intensity, but different types of poisons may have different durations or treatment requirements
* **Maximum Level:** No levels. Poisoned is a binary state (either poisoned or not)

## Exposure & Resistance

When a character is exposed to poison, they typically have a chance to resist its effects:

### Resistance Checks

When exposed to poison, a character makes a **FRT check** against a **TD based on the poison's potency**:

* **Mild Poison:** FRT check (TD 2)
* **Standard Poison:** FRT check (TD 3)
* **Potent Poison:** FRT check (TD 4)
* **Deadly Poison:** FRT check (TD 5)

Success means the character resists becoming Poisoned. Failure means the character gains the Poisoned status.

### Automatic Poisoning

In some cases, resistance checks may not be allowed:

* Direct injection into bloodstream with specialized delivery methods
* Extremely potent magical or alchemical toxins
* Overwhelming exposure (complete immersion, massive dose)

### Delayed Resistance

Some poisons allow multiple resistance checks:

* **Slow-acting Poisons:** Allow a new FRT check each hour/day
* **Cumulative Poisons:** Require multiple failed checks before taking effect
* **Chronic Exposure:** Gradually increases TD for resistance checks

## Types of Poisoning

While the Poisoned status is a single condition mechanically, it can represent various types of toxins:

* **Venom:** From creature bites or stings, often fast-acting (typically requires immediate FRT check)
* **Toxin:** From plants, fungi, or minerals, variable onset (may allow delayed resistance)
* **Infection:** From untreated wounds or disease, typically slow onset (usually allows multiple resistance checks)
* **Contamination:** From tainted food, water, or air (often allows a single resistance check when consumed)

## Recovery

* **Method:** Requires appropriate treatment based on the poison type
* **Difficulty:** Typically requires a Physik check (TD based on poison potency) or specific antidote/treatment
* **Time Required:** Varies by poison type. Some may resolve after treatment, others may require extended recovery time
* **Resources Needed:** Specific antidotes, medicinal herbs, or magical remedies depending on the poison type

**Special:** Some poisons may have lasting effects even after the Poisoned status is removed.

## Additional Information

* **Common Sources:**
  * Venomous creatures (snakes, spiders, scorpions)
  * Poisoned weapons (coated blades, treated arrows)
  * Toxic plants and fungi
  * Contaminated food or water
  * Infected wounds (untreated with Infection Risk)

* **Related Status Effects:** May cause Fatigue, Pain, or other effects depending on the specific poison
* **Special Interactions:** Characters with the Poison Resistance Feature may have advantage on recovery checks or reduced effects

## Gameplay Considerations

The Poisoned status serves several narrative and mechanical purposes:

* Creates urgency to seek treatment
* Rewards preparation (carrying antidotes)
* Provides consequences for risky behavior (eating unknown plants, ignoring wound treatment)
* Creates interesting tactical decisions in combat (poisoned weapons)

**GM Note:** When applying the Poisoned status, consider specifying the type of poison and any special properties it might have, even though the mechanical effect (being "compromised") remains consistent.
