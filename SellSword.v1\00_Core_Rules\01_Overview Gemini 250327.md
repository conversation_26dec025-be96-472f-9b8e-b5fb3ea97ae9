Okay, here is a more organized presentation of the "Sellsword" TTRPG concept based on our discussion, incorporating the feedback and recommendations.

**Sellsword TTRPG: Concept Overview**

**I. Core Design Goals:**

- **High Verisimilitude:** Create a grounded, believable world and mechanics, especially regarding combat and character capabilities.
- **Equipment Matters:** Emphasize the importance of gear and using the "right tool for the job" for success.
- **Manageable Crunch:** Offer depth and tactical choices without overly complex or slow rules.
- **<PERSON><PERSON><PERSON>:** Reflect the challenging life of a sellsword, where resources are managed carefully and danger is real.

**II. Core Mechanic: d6 Dice Pool**

- **System:** Based on rolling pools of six-sided dice (d6s), similar to engines like the Year Zero Engine (_Alien RPG_, _Forbidden Lands_).
- **Building the Pool:** `(Relevant Derived Action Score) + Skill Dice + Gear Dice + Resource Dice - Difficulty Dice`
- **Rolling & Success:** Roll the dice pool. Each '6' rolled counts as one success. The number of successes typically determines the outcome or degree of success.
- **Wager/Push Mechanic:**
    - Players can choose to spend **Stamina** (for physical actions) or **Will** (for mental actions) to add dice equal to the amount spent to their pool _before rolling_.
    - **Risk:** If _any_ '1' is rolled on the dice comprising the initial pool (Action + Skill + Gear - Difficulty), the wagered Stamina/Will is lost. _(Recommendation: Confirm if this is the intended trigger, or if only 1s on specific dice types matter. YZE often links this to Attribute dice)._ This creates significant tension and risk/reward decisions.
- **Difficulty:** Represented by dice subtracted from the pool before rolling, making success inherently less likely. Determined by task difficulty, opposition, or environmental factors.

**III. Character Creation & Attributes**

- **Concept:** Player characters begin as competent professionals in their field, likely near peak potential for their background.
- **Prime Attributes:** Represent fundamental physical and mental capabilities.
    - **Scale:** -3 (very poor) to +3 (peak normal human), with 0 being average. Mapped to percentiles for grounding (e.g., +3 ≈ top 1%). Allows room above +3 for supernatural/extraordinary beings.
    - **Point Buy:** Players choose one method at creation:
        
        1. **Broad:** Start with 5 points, maximum starting attribute is +2.
        2. **Specialized:** Start with 4 points, maximum starting attribute is +3.
        
        - _Note:_ Players can assign negative values to attributes, but the final sum must equal the points granted by the chosen method (5 or 4).
- **A. Current Physical Attributes:** (Generally well-defined)
    - **Endurance (END):** Capacity for sustained physical activity.
    - **Strength (STR):** Physical power, ability to exert force.
    - **Agility (AGI):** Speed, quickness, gross motor skills, movement.
    - **Dexterity (DEX):** Fine motor control, hand-eye coordination, manipulation.
- **B. Current Mental Attributes:** (Functionally sound, but definitions have potential overlap)
    - **Concentration (CON):** Capacity for extended mental activity/focus.
    - **Intellect (INT):** Mental prowess, depth of thought, reasoning, analysis.
    - **Acuity (ACU):** Mental speed, awareness, taking in information quickly.
    - **Empathy (EMP):** Mental control, emotional intelligence, social understanding and influence.
- **C. Recommendation - Alternative Mental Attributes:** (For potentially clearer distinction and player concept support)
    - **Intellect (INT):** Unchanged. Logic, knowledge, analysis.
    - **Awareness (AWA):** Replaces Acuity. Perception, noticing details, situational alertness. Clearly "perceptive".
    - **Resolve (RES):** Replaces Concentration & "mental control" from Empathy. Internal mental fortitude, self-discipline, focus, resisting fear/influence. Clearly "strong-willed".
    - **Presence (PRE):** Replaces Empathy's social aspects. Charisma, persuasion, command, social maneuvering. Clearly "charismatic/socially adept".
    - _Benefit:_ This split explicitly separates internal willpower (Resolve) from social skill (Presence), and perceptive ability (Awareness) from analytical depth (Intellect), potentially making character concepts easier to realize.

**IV. Derived Characteristics**

- **A. Derived Stats:** Resources and secondary capabilities based on Prime Attributes.
    - **Speed:** `3 + AGI` (Movement distance in hexes per Action).
    - **Carry:** `3 + STR` (Number of item 'slots' before being burdened).
    - **Stamina:** `3 + END * 2` (Resource for physical exertion, wagerable. Typically refreshes after combat/rest).
    - **Will:** `3 + CON` (or `3 + RES` with alternative attributes) (Resource for mental exertion/magic, wagerable. Refreshes only under special circumstances – significant success, specific triggers, GM rewards – making it a key strategic resource).
    - **Influence:** `3 + EMP` (or `3 + PRE`) (Abstract 'slots' representing social capital, contacts, favors, blackmail material. Leveraged primarily in downtime/preparation).
    - **Knowledge/Lore:** `3 + INT * 2` (Abstract 'slots' representing areas of expertise. Used in downtime/prep and can provide situational bonuses via "Know vs. Do" principle).
    - _Note on Multipliers:_ The `*2` for END/INT derived stats intentionally compensates for these attributes currently contributing to only one Derived Action each. _Recommendation:_ Consider if this is sufficient, or if adding unique Talents/Skill interactions tied to END/INT would provide more engaging impact.
- **B. Derived Actions:** The core actions used for task resolution, derived from pairs of Prime Attributes. Streamlines GM arbitration.
    - **(Using Current Attributes):**
        - `FRT (Fortitude) = END + STR` (Resist physical harm/ailments)
        - `PHY (Physique) = STR + AGI` (Forceful action, climbing, jumping, _Body Attack_)
        - `FIN (Finesse) = AGI + DEX` (Balance, stealth, acrobatics, _Voids Attack_)
        - `PRC (Precision) = DEX + ACU` (Fine manipulation, ranged attacks, surgery, _Vitals Attack_, _Scribe Magic_)
        - `FCS (Focus) = ACU + CON` (Perception checks, tracking, investigation, _Weave Magic_)
        - `RES (Resolve Action) = CON + EMP` (Resist mental influence/fear, maintain spells, _Evoke Magic_)
        - `INS (Insight) = EMP + INT` (Social reads, solving puzzles, deduction, _Scry Magic_)
    - **(Mapping with Alternative Attributes):**
        - `PRC = DEX + AWA`
        - `FCS = AWA + RES`
        - `RES (Action) = RES + PRE` (Or potentially `RES + END`)
        - `INS = PRE + INT`
        - (FRT, PHY, FIN unchanged)
    - _Note:_ Both systems currently leave END and INT contributing directly to only one Action.

**V. Skills & Customization**

- **Function:** Allow character differentiation beyond attributes. Add dice to Action pools, grant special maneuvers, allow different resource use (like wagering Stamina on specific attack types), negate penalties, etc.
- **Structure:** Tiered progression (Skill Level 1, 2, etc.) often offering specialization choices at higher levels.
- **Example:** Archery 1 (+2 dice with bows); Archery 2 (Choose Marksman - ignore minor cover; or Quickshot - reduced action cost).

**VI. Equipment & Combat**

- **Core Principle:** Gear is crucial. Using appropriate equipment provides significant advantages.
- **Gear Bonuses:** Items (tools, armor, weapons) add dice (typically 1-3) to relevant Action pools based on their quality and function.
- **Weapons:**
    - Have ratings (1-3) in characteristics like **Damage, Pierce, Block, Parry**. The relevant rating determines the number of dice added to the pool for that specific use (e.g., using Parry rating when parrying).
    - **Damage Calculation:** _**Recommendation: Requires Clarification.**_ Define exactly how the final damage value is determined after a hit. Is it: Weapon's Damage Rating? Number of Successes? Rating + Successes? A separate damage roll (e.g., roll d6s equal to rating)? Clear definition needed.
    - **Lethality:** Designed to be high. Low base damage weapons can still be effective by targeting weaknesses (using Voids/Vitals attacks, leveraging Pierce).
- **Armor:**
    - Provides protection, likely by subtracting successes, adding to attacker's difficulty, or having an Armor Value soak. Has ratings (1-3).
    - **Coverage:** Differentiated zones like **Body** and **Voids** (gaps). Interacts tactically with different attack types (PHY aims for Body, FIN/PRC aim for Voids/Vitals).
- **Health & Damage:**
    - **No Hit Points.** Uses a **Wound System**. Damage results in wounds with specific debilitating effects (penalties, reduced function, risk of bleeding out, etc.). Strongly reinforces lethality and verisimilitude.

**VII. Resources & Downtime**

- **Stamina:** Represents short-term physical exertion. Relatively easy to recover (after combat, short rest).
- **Will:** Represents mental fortitude, morale, and focus. Scarce resource, recovery tied to significant narrative events, specific character traits/achievements, or GM rewards. Crucial for long-term success and magic use.
- **Influence & Knowledge:** Resources representing social networks and expertise. Managed via 'slots'. Primarily utilized during **Downtime** phases for preparation, investigation, and leveraging contacts before missions (e.g., "planning the heist").

**VIII. Summary & Key Strengths:**

- Strong thematic identity focusing on gritty realism and professional sellswords.
- Robust core dice pool mechanic with engaging risk/reward via the Wager system.
- Streamlined action resolution through the Derived Actions system.
- Meaningful equipment choices that directly impact mechanics ("Gear Matters").
- Central role for resource management, especially the narratively-tied Will resource.
- Wound system enhances the desired verisimilitude and gritty feel.

**IX. Key Recommendations / Decisions Pending:**

1. **Refine Mental Attributes:** Strongly consider adopting the alternative set (INT, AWA, RES, PRE) or similar for maximum clarity and character concept support.
2. **Balance END/INT Impact:** Decide if the `*2` derived stat multipliers are sufficient or if implementing Talents/Skills for more active contributions from END/INT is preferred.
3. **Finalize Damage Calculation:** Clearly define how weapon damage is calculated after a hit.
4. **Clarify Wager Risk:** Specify the exact condition for losing wagered resources (any '1' rolled, '1' on attribute dice, etc.).
5. **Playtest Extensively:** Focus on dice probabilities, resource economy (especially Will), combat pacing/lethality, skill impact, and overall feel.