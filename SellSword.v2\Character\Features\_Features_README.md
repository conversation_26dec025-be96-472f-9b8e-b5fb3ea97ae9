# Features Overview

This section details the Features available in Sellsword.

Features represent unique, often innate or deeply ingrained, aspects of a character chosen during creation. They are distinct from learned [[../Skills/_Skills_README|Skills]] or acquired [[../../Lore/_Lore_README|Lore]]. Features provide specific mechanical advantages, disadvantages, or unique ways to interact with the game world, helping to define a character's core identity and capabilities beyond their basic attributes and skills.

## Feature Acquisition

* **Character Creation:** Features are primarily selected during character creation using a pool of points or picks (exact number TBD).
  * **Ancestry Access:** A character's chosen [[../Ancestries/|Ancestry]] grants *access* to specific pools of Features. Some features might be exclusive to one ancestry's pool, while others might appear in multiple pools, representing shared traits or tendencies. Some ancestries might grant a specific feature automatically in addition to providing access to a pool.
  * **General Pool:** There is also a pool of General Features available to all characters, regardless of ancestry, representing common backgrounds, quirks, innate talents, or flaws.
* **Advancement:** Gaining new Features after character creation should be rare, perhaps tied to significant character arcs or specific in-game events, subject to GM approval.

## Feature Design Philosophy

* **Integral, Not Learned:** Features should feel like part of *who* the character is, not just *what* they can do.
* **Interesting Choices:** Features should offer meaningful mechanical effects or roleplaying hooks. They don't all need to be combat-focused.
* **Balanced Impact:** While some features might provide numerical bonuses (e.g., +N dice to a specific action under certain conditions), others might offer unique actions, resource interactions (Stamina/Will/Standing), or narrative permissions. Some might even include drawbacks (Quirks/Flaws). The overall power level should be noticeable but generally less impactful than high-level skills.
* **Synergy:** Features can interact with Attributes, Skills, and Equipment in interesting ways.

## Feature Structure & Conventions

* **Location:** All Feature files reside in the `Character/Features/` directory.
* **File Naming:** Title case, underscores for spaces (e.g., `Supernatural_Grace.md`).
* **Template:** Use `_template_feature.md` as a base.
* **Frontmatter:** Each file must include YAML frontmatter:
  * `name`: (String) The display name of the feature.
  * `type`: (String) Category (e.g., "Ancestry", "General", "Quirk", "Flaw").
  * `tags`: (List) Relevant keywords (e.g., `[alfarkin, social, combat, mental]`).
  * `summary`: (String) A brief one-sentence description.
  * `prerequisites`: (Optional String) Any requirements (e.g., "Requires Alfarkin Ancestry").
  * `related_features`: (Optional List) Links to synergistic or mutually exclusive features.
* **Content:** The body of the file should detail:
  * **Description:** Flavor text explaining the feature.
  * **Mechanics:** Clear explanation of the game rules effects. Use links `[[...]]` for related rules or concepts (e.g., [[../Attributes/FIN|FIN]], [[../../Rules/_Rules_README#WagerPush Mechanic|Wager Mechanic]]).

## Adding New Features

1. Copy `_template_feature.md` into this folder.
2. Rename it using the established naming convention.
3. Fill in the YAML frontmatter and content sections.
4. Commit changes with a descriptive message.

## Related Links

* [[../../SellSword_v2_Overview|Project Overview]]
* [[../../Rules/_Rules_README|Core Rules]]
* [[../_Character_README|Character Overview]]
* [[../Attributes/_Attributes_README|Attributes & Actions]]
* [[../Skills/_Skills_README|Skills Overview]]

## Cleanup & Standardization Plan

- [x] Audit & categorize all Feature files
- [x] Enforce frontmatter schema (name, type, tags, summary)
- [x] Validate each file and triage (Compliant / Needs Rewrite / Delete)
- [x] Archive (Deprecated/) or remove files marked “Delete”
- [x] Generate skeletons for “Needs Rewrite” using `_template_feature.md`
- [x] Rewrite mechanics with correct Derived Actions and links
- [x] Update README with compliance checklist example
- [x] Create Dataview query to list compliant Features
- [x] Implement lint/CI rule to enforce schema on changes

### Example Compliant Feature

```md
---
name: Steady Hand
type: General
tags: [precision, dexterity, FIN]
summary: "The character possesses exceptional fine motor control for precise tasks."
prerequisites: ""
related_features: []
---

## Description

Steady Hand represents the character's ability to maintain perfect control over small movements, from crafting to fine shooting.

## Mechanics

* **Effect:** When making a PRC (Precision) action under standard conditions, gain +1 die.
* **Condition (Optional):** Only applies when aiming or performing sustained precision tasks.
```

### Dataview Query to List Compliant Features

```dataview
TABLE name, type, tags, summary
FROM "Character/Features"
WHERE name AND type AND tags AND summary

## CI Lint Rule Example

Use this GitHub Actions workflow to validate Feature frontmatter on pull requests:

```yaml
name: Feature Schema Validation
on: [pull_request]
jobs:
  validate_features:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install markdownlint with frontmatter support
        run: npm install -g markdownlint-cli-front-matter
      - name: Validate Feature frontmatter
        run: markdownlint-cli-front-matter "Character/Features/*.md" --config .markdownlint.json
```

Example `.markdownlint.json`:

```json
{
  "front-matter-required-keys": ["name","type","tags","summary"],
  "default": true
}
