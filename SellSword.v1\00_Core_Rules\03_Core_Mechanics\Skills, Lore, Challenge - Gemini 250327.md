Okay, here is a primer covering Skills, Lore, Difficulty, Complexity, and Success Spending, designed for a new GM running Sellsword.

**Sellsword System Primer: Skills, Lore, & Challenges**

This primer explains how character knowledge (Lore) and training (Skills) interact with the challenges presented in Sellsword, using the core mechanic of rolling a pool of d6s and counting sixes ('6's) as successes.

**1. Difficulty vs. Complexity: Defining Challenges**

Not all challenges are equal. We distinguish between how hard something is due to circumstances (**Difficulty**) and how intricate it is to perform (**Complexity**).

- **Difficulty:**
    
    - **Represents:** Adverse conditions (poor light, bad weather, long range), simple targeting adjustments (small target, hitting limbs/armor Voids), inherent opposition strength.
    - **Mechanic:** Subtracts dice (`-X` dice) directly from the player's dice pool _before_ they roll. Each subtracted die linearly reduces the chance of success.
    - **Examples:** Shooting in dim light (-1 Difficulty), attacking a target behind cover (-2 Difficulty), aiming for armor Voids (-3 Difficulty).
- **Complexity:**
    
    - **Represents:** Inherently intricate actions, performing multiple maneuvers simultaneously, dealing with highly sophisticated mechanisms, or achieving effects requiring exceptional precision or control.
    - **Mechanic:** Requires **additional successes** (e.g., "Requires 2 successes") beyond the standard single success needed for simple actions. This must be achieved on the roll to gain the _full intended effect_ of the complex action. Failing to meet the threshold often results in partial success (e.g., you jump but miss the shot) or failure of the complex part. This significantly increases the actual difficulty of the task.
    - **Examples:** Picking a masterwork lock (Req. 2 successes + Difficulty), performing acrobatic multi-tasking like jumping off a ledge while shooting accurately (Req. 2 successes + Difficulty), weaving an advanced spell effect (Req. 3 successes + Difficulty). Complexity is usually declared or determined _before_ the roll based on the player's intended action.
- **Key Distinction:** Difficulty makes _getting any success_ harder. Complexity _raises the bar for what counts as success_ for intricate actions. An action can have both Difficulty and Complexity.
    

**2. Lore (Knowing)**

- **Concept:** Represents specific, retained information and areas of expertise ("KNOW"). Based on character background and study.
- **Source:** Characters have **Lore Slots** determined by their Intellect (`3 + INT*2`). These slots are filled with **Lore Entries** (e.g., "Local History (1)", "Monster Anatomy (2)", "Ancient Runes (3)"), each costing 1-3 slots based on depth.
- **Mechanics:**
    - **Information:** Provides narrative answers or insights when relevant ("GM, does my 'Monster Anatomy' lore tell me where this beast's heart is?").
    - **Task Aid:** At GM discretion, relevant Lore can grant **situational bonus dice** (+1 or +2) or **negate Difficulty dice** (-1 or -2) on related Action rolls. (e.g., "Ancient Runes" lore might negate Difficulty when attempting to decipher a script via an `INS` roll).
- **Flexibility:** Lore is not static. Characters can spend **Downtime** actions between missions to swap out Lore entries, representing research or consulting contacts. The Downtime cost equals the Lore entry's Slot Cost.

**3. Skills (Doing)**

- **Concept:** Represents practiced abilities, training, and techniques ("DO"). Skills **enhance** actions, making characters better, faster, or more efficient; they **do not gate** the ability to attempt an action (within reason).
- **Source:** Purchased using **Skill Points (SP)** earned at character creation and level up. Higher levels of any skill cost progressively more SP, encouraging breadth before extreme depth.
- **Structure (3 Tiers):**
    - **Tier 1 (Category):** Broad competence (`Melee Combat`, `Ranged Combat`, `Athletics`, `Stealth`, `Social`, etc.). Primarily grants **+1 die per level** to relevant actions. Acts as a prerequisite for Tier 2/3 skills.
    - **Tier 2 (Style):** Tactical approach (`Marksman`, `Dueling`, `Grappler`, `Fast Talk`, `Infiltrator`, etc.). Primarily grants **unique abilities, maneuvers, resource interactions**, or significantly **reduces/negates Complexity requirements** for specific actions/situations.
    - **Tier 3 (Focus/Gear):** Specific equipment mastery or technique refinement (`Bows`, `Swords`, `Lockpicking`, `Surgery`, `Herbalism`, etc.). Primarily grants **+1 die per level** with specific gear/tasks, **reduces Difficulty** for niche applications, or unlocks effective use of gear properties.
- **Key Function:** Skills make actions easier (reduce Difficulty), cheaper (reduce AP cost, Stamina/Will cost), more effective (+dice, better outcomes), more reliable (re-rolls, negate penalties), or crucially, **simpler (reduce or negate Complexity's extra success requirement)**.

**4. Success & Spending Extra Successes (Stunts)**

- **Basic Success:** For a standard, non-complex action, **one success ('6')** is typically required to achieve the intended outcome.
- **Complex Actions:** Must achieve the **declared number of successes (e.g., 2+)** just to succeed at the full, intricate action as intended. This represents a higher intrinsic risk for a potentially greater reward (pulling off the cool maneuver).
- **Extra Successes ("Stunts"):** If you roll _more_ successes than required (more than 1 for a simple action, or more than the 2+ needed for a complex one), these **extra successes** can be spent immediately on **minor, beneficial effects (Stunts)** relevant to the action.
    - **Examples:** +1 Damage, inflict a minor condition (off-balance, distracted), improve defensive position slightly, complete the action slightly faster (potentially reducing next action's cost?), impress/intimidate an onlooker.
    - **Important Distinction:** Stunts are opportunistic bonuses gained from rolling well. They are generally **less impactful** than the inherent reward of successfully completing a high-risk **Complex Action** that required multiple successes by design. Declaring and achieving a complex action rewards deliberate risk-taking more significantly than random extra successes on a simple task.

**GM Guidance Summary:**

1. **Listen to Player Intent:** What are they trying to achieve?
2. **Assess Challenge:**
    - Is it affected by adverse circumstances or simple targeting? Add **Difficulty** (-Dice).
    - Is it an intricate maneuver, multi-tasking, or dealing with high complexity? Set a **Complexity** requirement (Req. 2+ Successes). _An action can have both._
3. **Factor in Skills/Lore:** Do any Skills reduce the Difficulty or Complexity for this action? Does Lore negate Difficulty or grant bonus dice?
4. **Player Rolls:** Player builds pool (Action Score + Skill Dice + Gear Dice + Wager - Difficulty Dice), rolls, counts successes.
5. **Evaluate Outcome:** Did they meet the required success threshold (1 for simple, 2+ for complex)?
6. **Apply Stunts:** If they rolled _extra_ successes beyond the requirement, allow them to choose minor Stunts from a relevant list or based on context.

**5. Handling Large Dice Pools: Automatic Success Threshold**

- **Purpose:** To streamline gameplay when highly competent characters perform actions they excel at, avoiding the need to roll excessive numbers of dice (e.g., more than 10-12) while still representing mastery.
- **Trigger:** This rule applies if a character's **final dice pool** for an action (after adding all bonuses from Skills, Gear, Wagered Resources, and _after_ subtracting any Difficulty dice) is **11 dice or more**.
- **Procedure:**
    1. **Grant Automatic Success:** The character automatically achieves **one success** towards the action's requirement.
    2. **Reduce Pool:** Remove dice equal to the character's base **Derived Action score** (e.g., if their `PHY` score is 7, remove 7 dice) from the pool.
    3. **Roll for Extras:** Roll the **remaining dice**. Any '6's rolled on this smaller pool count as **extra successes** which can be spent on Stunts (see Section 6).
- **Interaction with Complexity:**
    - If the attempted action had a **Complexity** requirement (needed 2+ successes), the single automatic success granted counts as the _first_ required success.
    - The character **must still generate the remaining required successes** (e.g., 1 more success if Complexity required 2) from the dice rolled in Step 3 above. Only successes beyond _that_ threshold can be spent on Stunts. Skills that reduce or negate Complexity remain very valuable.
- **Example:**
    - A Sellsword has a `PHY` Action Score of 7. They have `Melee Combat 2` (+2 dice) and a `Focus: Swords 1` (+1 die). They use their trusty Sword (+2 Gear dice) and wager 3 Stamina (+3 dice) to attack a lightly armored guard (Difficulty -1 die).
    - Initial Pool Calculation: 7 (PHY) + 2 (Skill) + 1 (Skill) + 2 (Gear) + 3 (Stamina) - 1 (Difficulty) = 14 dice.
    - Check Threshold: 14 is >= 11, so Automatic Success triggers.
    - Resolution:
        1. They gain 1 automatic success. The attack hits.
        2. Remove dice equal to Action Score (PHY 7): Remove 7 dice.
        3. Roll Remainder: Roll 14 - 7 = **7 dice**.
        4. Any '6's rolled on these 7 dice are extra successes, spent on Stunts (extra damage, etc.).

**6. Success & Spending Extra Successes (Stunts)**

- **Base Success:** For standard actions without Complexity, **one success ('6')** achieves the basic intended outcome. If the Automatic Success threshold was met, this is granted automatically.
- **Complex Actions:** Must achieve the **declared number of successes (e.g., 2+)** to succeed fully. If Automatic Success triggered, it provides the first success, but the remainder roll must generate the rest. Failing to meet the threshold usually means partial success or failure of the complex part.
- **Extra Successes ("Stunts"):** If you roll _more_ successes than required (more than 1 for simple actions, more than the required number for complex actions), these **extra successes** can be spent immediately on **minor, beneficial effects (Stunts)**.
    - **Examples:** +1 Damage, inflict minor condition (off-balance), improve position, faster recovery/next action, impress/intimidate.
    - **Key Distinction:** Stunts are opportunistic bonuses from rolling well or having dice leftover after auto-success. They are generally **less impactful** than the inherent reward of successfully completing a declared high-risk **Complex Action**.

**7. GM Guidance Summary (Updated)**

1. Listen to Player Intent.
2. Assess Challenge: Apply **Difficulty** (-Dice) for adverse conditions/simple targeting. Set a **Complexity** requirement (Req. 2+ Successes) for intricate/multi-part actions.
3. Factor in Skills/Lore: Check for bonuses, Difficulty negation, or **Complexity negation/reduction**.
4. Calculate Final Dice Pool (after Difficulty).
5. **Check Threshold:** Is the final pool >= 11?
    - **If YES:** Apply **Automatic Success** procedure (grant 1 success, remove Action dice, roll remainder for extras/remaining Complexity).
    - **If NO:** Player rolls the full final dice pool.
6. Evaluate Outcome: Did they meet the required success threshold (1 or Complexity req.)?
7. Apply Stunts: Allow spending of any successes rolled _beyond_ the requirement.
