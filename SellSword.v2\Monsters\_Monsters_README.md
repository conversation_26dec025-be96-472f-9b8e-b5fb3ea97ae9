# Sellsword Monsters

This document outlines the design philosophy, structure, and guidelines for creating monsters in the Sellsword TTRPG.

## I. Design Philosophy: Truly Monstrous

The core goal is that monsters should feel distinct from player characters and genuinely **monstrous**. This means:

1. **Superhuman Capabilities:** Often exceed normal limits in strength, speed, resilience, senses, etc.
2. **Rule Interaction:** Possess unique traits or abilities that interact with or bypass standard game mechanics in interesting ways, making encounters less predictable.
3. **Focus on Theme & Role:** Have a clear identity (e.g., ambusher, brute, controller) reinforced by mechanics.
4. **Manageable Complexity:** Prioritize impactful, unique mechanics over numerous minor ones for ease of GM use.

## II. Monster Structure & Template (`_template_monster.md`)

All monsters use the `_template_monster.md` structure. Key components:

* **Metadata:** `name`, `description_short`, `size`, `type`, `tags`.
* **Core Stats:** Simplified combat values (detailed in Section III).
  * `size`: Physical scale (Tiny to Gargantuan).
  * `combat_pool`: Base dice pool for actions.
  * `action_points`: AP per round.
  * `speed`: Movement per AP (including special types).
  * `wounds`: Tiered wound threshold pools.
* **Defense:**
  * `target_difficulty` (TD): Adds Difficulty Dice to attackers.
  * `dr`: Damage Reduction (Body/Voids).
  * Resistances/Vulnerabilities.
* **Actions:** A list defining all actions the monster can perform that cost Action Points (AP). This includes standard attacks and special maneuvers. (`name`, `ap_cost`, `pool_mod`, `reach`, `damage`, `pierce`, `force`, `notes`, `extra_success_effects`).
  * `ap_cost`: The number of Action Points required to perform the action (default 1 if unspecified).
  * `extra_success_effects`: An optional list defining effects that can be purchased by spending extra successes generated on the action's roll. Each effect has a `cost` (in extra successes) and an `effect` description.
* **Monstrous Traits:** Passive abilities or effects that do not cost AP to use. This is where unique rule interactions or inherent capabilities are defined.
* **Behavior & Other:**
  * `tactics`: Typical combat behavior.
  * `loot`: Potential resources gained.
  * `environment`: Typical habitats.
* **GM Discretion:** GMs should feel empowered to allow monsters to attempt reasonable actions not explicitly listed, using the `combat_pool` or GM judgment for resolution.

* **Monster Descriptions for GMs:**
  * Effective monster descriptions evoke atmosphere, mood, and thematic identity.
  * Use vivid, sensory language to paint a picture of the monster's appearance, behavior, and presence.
  * Highlight unique or unsettling features that make the monster memorable and distinct.
  * Convey the monster's role and tactics through descriptive cues (e.g., "lurks in shadows," "moves with unnatural grace," "hunts in packs").
  * Balance detail with brevity; provide enough to inspire but leave room for GM improvisation.
  * Consider the monster's environment and lore to enrich the description.
  * Example: The Huldrekall is described as a gaunt, silent aberration haunting high, dark places, with spider-like limbs and a skin that absorbs light, evoking dread and mystery.
  * Use the Goblin Kinder description as a model for small, cunning foes: emphasizing their malice, numbers, and stealthy menace.
  * Encourage GMs to adapt descriptions to their campaign tone and player group.

## III. Core Stat Guidelines & Benchmarks

These guidelines promote consistency. A monster's concept and **Monstrous Traits** may justify stats outside these ranges.

* **`size` (Physical Scale):** Defines physical dimensions. Often correlates with other stats (larger = more wounds/DR, lower TD, potentially higher speed; smaller = fewer wounds/DR, potentially higher TD).
  * *Size Category Examples:* Tiny (Cat), Small (Goblin), Gnome, Medium (Human), Wolf, Large (Bear), Ogre, Huge (Giant), Elephant, Gargantuan (Kraken).
  * *Note:* Use Size as a starting point; traits can override correlations.

* **`combat_pool` (Dice Pool):** Base d6s for most actions. Represents general competence.
  * *Benchmarks:* Minion 2-4d6 (~31-52% chance ≥1 success); Standard 4-6d6 (~52-67%); Elite 6-8d6 (~67-77%); Boss 8-10+d6 (~77-84%+).
  * *Note:* Chance of multiple successes increases significantly with pool size, potentially generating extra successes to spend on `extra_success_effects` defined for specific actions.
  * *Group Attacks:* When multiple similar monsters coordinate an action (like an attack) against a single target, their `combat_pool` dice may be combined for the action roll. However, the base `damage`, `pierce`, and `force` typically do not increase unless specified by a trait. This increases success chance and potential for spending extra successes on defined `extra_success_effects`, but not raw hit impact.

* **`action_points` (AP):** Actions per round (PCs have 4).
  * *Benchmarks:* Slow/Simple 2 AP; Standard 3 AP; Agile/Elite 4 AP; Boss 5+ AP.

* **`speed` (Movement):** Hexes/units per AP (Avg Human = 4). Includes special types like Climb, Fly (e.g., `4 (Climb 4)`).
  * *Benchmarks:* Slow 2-3; Average 4; Fast 5-6; Swift 7+.

* **`wounds` (Wound Threshold Pools):** Tiered pools (`minor`, `major`, `grievous`, `deadly`) tracking resilience. Effective penetrating damage determines tier (1=Minor, 2=Major, 3=Grievous, 4+=Deadly). Wounds "upgrade" if a lower tier pool is full. Defeat occurs when a wound is taken for a tier with no capacity. See `[[../Wounds/_Wounds_README]]`.
  * *Benchmarks (Total effective wounds):* Fragile 1-2 Minor; Standard 3-4 (incl. Major); Tough 4-6 (incl. Grievous); Very Tough 6+ (incl. Grievous/Deadly).
  * *Note:* Adjust based on role (tank vs. glass cannon).

* **`target_difficulty` (TD):** Adds Difficulty Dice to attackers. Represents evasiveness/size.
  * *Benchmarks:* Easy Target TD 0; Average TD 1; Agile/Small TD 2; Very Hard TD 3+ (often via Traits).
  * *Note:* High base TD can be frustrating; consider traits that interact with it.

* **`dr` (Damage Reduction):** Reduces damage. Represents hide/armor (PC range ~1-5+).
  * *Benchmarks:* Unarmored DR 0; Light/Hide DR 1-2; Medium/Natural DR 3-4; Heavy DR 5+.
  * *Note:* High DR + high Wounds = damage sponge; consider vulnerabilities.

## IV. Creating New Monsters

1. Start with concept and theme.
2. Define primary role (damage, disruptor, tank, etc.).
3. Assign core stats using guidelines, considering size correlations.
4. Develop 1-3 key **Monstrous Traits** that fulfill the "Truly Monstrous" philosophy (rule interaction, unique capabilities).
5. Define standard actions, tactics, flavor text, and loot.
6. Review: Is it distinct? Challenging? Manageable?
7. **Assess Relative Threat:** Evaluate overall challenge based on stats/traits synergy (for GM guidance).

## V. Examples

Refer to existing monster files for implementation examples:

* [[Goblin Kinder]] (Minion example)
* [[Huldrekall]] (Elite/Specialist example)

## VI. Monsters in Combat

### Initiative & Action Declaration

Sellsword uses an initiativeless system focused on simultaneous declaration and GM-guided resolution:

1. **Declarative Phase:** The GM first declares the intended actions for all monsters and NPCs. Then, players declare their actions.
2. **Active Phase:** The GM resolves all declared actions in an order that makes narrative and mechanical sense, typically resolving lower AP cost actions first. Within an AP cost tier, the suggested order is Ranged Attacks -> Melee Attacks -> Movement -> Complex Actions/Magic. The GM has the final say on resolution order.

### Group Tactics & Mob Attacks

Some monsters, particularly minions, are designed to fight in groups.

* **Combining Pools:** As noted under `combat_pool`, multiple similar monsters attacking the same target may combine their dice pools for the attack roll, increasing success chance but not base damage/effects.
* **Traits:** Traits like `Swarm Tactics` often interact with group formations.

### Spending Extra Successes

Monsters can generate extra successes on their action rolls (rolling more '6's than needed for a basic success or to overcome TD/Difficulty). These extra successes can be spent by the GM to activate additional effects defined within the specific action used.

* **Source:** Available effects are listed under the `extra_success_effects` field for each action in the monster's stat block.
* **Spending:** Each effect has a `cost` in extra successes. The GM can choose which available effects to purchase, up to the total number of extra successes generated.
* **Examples:** Effects might include dealing extra damage, inflicting conditions, gaining momentum, or causing unique thematic results like knocking a target prone.
* **Default:** If an action generates extra successes but has no `extra_success_effects` defined, the GM can typically choose to apply narrative effects appropriate to the situation and monster type, or potentially inflict +1 Damage per extra success if the attack allows (e.g., Pierce >= DR), subject to GM discretion.
