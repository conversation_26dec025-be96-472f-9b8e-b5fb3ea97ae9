# SellSword Skill Structure Overview

This diagram illustrates the tiered structure of skills in Sells<PERSON>, showing the progression from broad Categories to specific Styles and deep Specializations.

```mermaid
graph TD
    subgraph Skill System Structure

        subgraph Tier1_Categories [Tier 1 Categories - Add Dice]
            Cat1[Category Skill 1<br/>e.g. Weapons Craft<br/>L1: +1d (2 SP)<br/>L2: +2d (3 SP)<br/>L3: +3d (4 SP)]
            Cat2[Category Skill 2<br/>e.g. Subterfuge<br/>L1: +1d (2 SP)<br/>L2: +2d (3 SP)<br/>L3: +3d (4 SP)]
            Cat3[Category Skill 3<br/>e.g. Evoke<br/>L1: +1d (2 SP)<br/>L2: +2d (3 SP)<br/>L3: +3d (4 SP)]
            CatN[...]
        end

        subgraph Tier2_Styles [Tier 2 Styles - Unique Benefits]
            Style1A[Style A<br/>e.g. Dueling<br/>Req: Cat1 L1<br/>Cost: 3 SP<br/>Benefit: <PERSON> Stunt] --> StyleFocus1A1
            Style1B[Style B<br/>e.g. Two-Handed<br/>Req: Cat1 L1<br/>Cost: 3 SP<br/>Benefit: Cleave Ability] --> StyleFocus1B1

            Style2A[Style C<br/>e.g. Infiltrator<br/>Req: Cat2 L1<br/>Cost: 3 SP<br/>Benefit: Reduce Move Silently Complexity] --> StyleFocus2A1
            Style2B[Style D<br/>e.g. Investigator<br/>Req: Cat2 L1<br/>Cost: 3 SP<br/>Benefit: Enhanced Spot Detail Stunt] --> StyleFocus2B1

            Style3A[Style E<br/>e.g. Elemental Focus Fire<br/>Req: Cat3 L1<br/>Cost: 3 SP<br/>Benefit: Reduce Fire Weave Difficulty] --> StyleFocus3A1
        end

        subgraph Tier3_Specializations [Tier 3 Specializations - Niche Benefits]
            StyleFocus1A1[Spec: Swords<br/>Req: Style1A<br/>Cost: 3 SP<br/>Benefit: Reduce Sword Attack Difficulty]
            StyleFocus1B1[Spec: Axes<br/>Req: Style1B<br/>Cost: 3 SP<br/>Benefit: Improve Axe Damage vs Armor]

            StyleFocus2A1[Spec: Lockpicking<br/>Req: Style2A<br/>Cost: 3 SP<br/>Benefit: Reduce Lock Difficulty by 2]
            StyleFocus2B1[Spec: Trapfinding<br/>Req: Style2B<br/>Cost: 3 SP<br/>Benefit: Auto-spot simple traps]

            StyleFocus3A1[Spec: Fire Bolt<br/>Req: Style3A<br/>Cost: 3 SP<br/>Benefit: Reduce Fire Bolt Complexity]
        end

        Cat1 --> Style1A
        Cat1 --> Style1B
        Cat2 --> Style2A
        Cat2 --> Style2B
        Cat3 --> Style3A

        %% Optional: Direct path from Category to Specialization for some skills?
        %% Cat2 --> Spec_Disguise[Spec: Disguise<br/>Req: Cat2 L2<br/>Cost: 3 SP]

    end

    style Cat1 fill:#f9d,stroke:#333,stroke-width:2px
    style Cat2 fill:#f9d,stroke:#333,stroke-width:2px
    style Cat3 fill:#f9d,stroke:#333,stroke-width:2px
    style CatN fill:#f9d,stroke:#333,stroke-width:2px

    style Style1A fill:#ccf,stroke:#333,stroke-width:2px
    style Style1B fill:#ccf,stroke:#333,stroke-width:2px
    style Style2A fill:#ccf,stroke:#333,stroke-width:2px
    style Style2B fill:#ccf,stroke:#333,stroke-width:2px
    style Style3A fill:#ccf,stroke:#333,stroke-width:2px

    style StyleFocus1A1 fill:#9cf,stroke:#333,stroke-width:2px
    style StyleFocus1B1 fill:#9cf,stroke:#333,stroke-width:2px
    style StyleFocus2A1 fill:#9cf,stroke:#333,stroke-width:2px
    style StyleFocus2B1 fill:#9cf,stroke:#333,stroke-width:2px
    style StyleFocus3A1 fill:#9cf,stroke:#333,stroke-width:2px
```
