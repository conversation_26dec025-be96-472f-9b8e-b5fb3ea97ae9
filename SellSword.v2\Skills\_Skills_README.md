# Skills Overview

This section details the Skills available in Sellsword.

Skills represent trained abilities and knowledge that characters can acquire. They typically add dice to relevant checks or provide specific mechanical benefits.

## Skill Acquisition

Skills are purchased using **Skill Points (SP)** earned at character creation (7 SP) and potentially through advancement.

## Skill Tiers & Methodology

Skills are organized into three tiers, allowing for broad competence followed by specialization:

### Tier 1: Category Skills

* **Function:** Represent broad competence in a general area (e.g., [[Weapons_Craft]], [[Athletics]], [[Evoke]]).
* **Primary Benefit:** Add dice (+1 per level, up to 3 levels) to relevant [[../Attributes/_Attributes_README|Derived Action]] rolls.
* **Prerequisite:** Often required for Tier 2/3 skills within the same category.
* **SP Cost:** Escalating cost per level (e.g., L1=2 SP, L2=3 SP, L3=4 SP - subject to balancing).
* **Examples:** See files like [[Weapons_Craft]], [[Athletics]], [[Subterfuge]], [[Influence]], [[Survival]], [[Physik]], [[Crafting]], [[Tactics]], [[Husbandry]], [[Evoke]], [[Weave]], [[Scribe]], [[Scry]].

### Tier 2: Style Skills

* **Function:** Define a specific approach, specialization, or tactical style within a Category (e.g., `Dueling` within `Weapons Craft`, `Infiltrator` within `Subterfuge`). Focus on *how* actions are performed.
* **Primary Benefits (Choose 1-2 per skill):**
  * Grant unique [[_Stunts_README|Stunts]] or passive abilities (e.g., "Gain the Parry maneuver," "Can move silently at full speed").
  * **Reduce Complexity:** Lower the number of successes needed for specific complex actions (e.g., "Reduce Complexity of aimed Vitals attacks by 1").
  * **Novel Resource Use:** Allow spending Stamina/Will in new ways or under different conditions (e.g., "May spend Stamina instead of Will when Wager-ing on defensive rolls," "Spend 1 Will to ignore difficult terrain for one round").
* **Secondary Benefit (Optional):** Minor Difficulty reduction in very specific circumstances related to the style.
* **Dice Bonus:** Generally **do not** add dice directly to pools.
* **Prerequisite:** Typically requires Level 1 in the relevant Tier 1 Category skill.
* **SP Cost:** Suggest a flat cost (e.g., **3 SP** per Style skill - subject to balancing).
* **(Examples TBD - e.g., Marksman, Dueling, Grappler, Fast Talk, Infiltrator)**

### Tier 3: Specialization Skills

* **Function:** Represent deep mastery of a specific technique, tool, or piece of equipment within a Category or Style (e.g., `Swords` within `Weapons Craft`, `Lockpicking` within `Subterfuge`, `Herbalism` within `Physik`).
* **Primary Benefits (Choose 1-2 per skill):**
  * **Reduce Difficulty:** Significantly lower Difficulty for niche applications related to the specialization (e.g., "Reduce Difficulty by 1 when using Longbows," "Reduce Difficulty by 2 when picking Masterwork locks").
  * **Unlock Advanced Use:** Enable specific advanced maneuvers, crafting recipes, or the effective use of special properties on related gear.
  * **Enhanced Effect:** Improve the outcome of specific actions beyond what [[_Stunts_README|Stunts]] allow (e.g., "Successful Surgery checks also restore 1 Stamina," "Identify specific poison type on successful Physik check").
  * **Further Complexity Reduction:** May further reduce Complexity for highly specific tasks within the specialization.
* **Dice Bonus:** Generally **do not** add dice directly to pools. Level-less.
* **Prerequisite:** Typically requires the relevant Tier 1 skill at level 2.
* **SP Cost:** Suggest a flat cost (e.g., **3 SP** per Specialization skill - subject to balancing).
* **(Examples TBD - e.g., Bows, Swords, Lockpicking, Surgery, Herbalism)**

This tiered structure allows players to build characters with broad foundations (Tier 1) and then specialize through an "a la carte" selection of Styles (Tier 2) and Specializations (Tier 3) that grant unique capabilities rather than just stacking dice bonuses.
