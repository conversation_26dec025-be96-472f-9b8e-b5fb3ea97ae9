# **Optimizing Markdown for TTRPG Reference Documents: A Framework for Human Readability and LLM-Powered Retrieval Augmented Generation**

## **I. Introduction**

### **A. The Dual Challenge: Crafting TTRPG Documents for Human Understanding and LLM Efficacy**

Tabletop Role-Playing Game (TTRPG) sourcebooks, adventure modules, and bestiaries represent a unique intersection of narrative prose, structured rules, statistical data, and often, complex visual layouts. The primary challenge addressed in this report is the development of a Markdown-based organizational strategy that serves two masters: the human Game Master (GM) or player who needs clear, accessible, and navigable information during game preparation and play; and the Large Language Model (LLM) that requires well-structured, semantically rich data for effective Retrieval Augmented Generation (RAG) in applications like AI-powered GMs or advanced query systems.  
Standard Markdown, while simple, provides a foundational layer of structure. However, to truly unlock its potential for TTRPGs, it must be augmented with consistent conventions, strategic file organization, and rich metadata. Structured data formats like Markdown are inherently more LLM-friendly than plain text, improving parsing, interpretation, and reducing ambiguity.1 This is because LLMs can more easily understand and utilize the inherent organization within formats like Markdown, such as headings and lists, which aids in tasks like summarization and analysis by allowing the LLM to identify key topics and their relationships more effectively than in unstructured plain text.2 The formatting elements within Markdown, such as bold and italics, provide semantic cues about the importance and type of text, further enhancing LLM comprehension.1 This report will detail a framework to achieve this dual utility.

### **B. Overview of the Proposed Markdown Framework and Pipeline**

This report proposes a comprehensive approach, beginning with core principles for TTRPG Markdown that emphasize clarity, semantics, and LLM optimization. It then introduces a system-agnostic hierarchical framework designed for modularity, allowing for game-specific adaptations (e.g., for D\&D 5e or Pathfinder 2e). A key component is the strategic use of YAML frontmatter to embed detailed, queryable metadata, transforming individual Markdown files into structured data objects.  
Furthermore, the report will delve into optimizing this Markdown ecosystem for RAG systems, focusing on context-aware chunking and the role of metadata. Finally, it will critically evaluate the proposed data ingestion pipeline: PDF → Raw Markdown → Formatted TTRPG Markdown → ChromaDB, analyzing tools and techniques at each stage. The overarching aim is to provide a robust, flexible, and extensible methodology for digital TTRPG knowledge management.

## **II. Core Principles for TTRPG Markdown**

### **A. Foundational Markdown Best Practices for Clarity and Semantics**

The utility of Markdown in TTRPG documentation hinges on its capacity to provide a basic yet effective structure that benefits both human readers and LLM processing. While LLMs can process raw text, their comprehension, segmentation, and retrieval accuracy are markedly improved by the explicit structural and semantic cues that Markdown offers.1 Markdown elements such as headings, lists, bold text, and blockquotes directly correspond to semantic concepts like sectioning, enumeration, emphasis, and distinct content types (e.g., read-aloud text in adventures).2 This explicit marking enhances parsing, enables more accurate interpretation of content roles, and diminishes ambiguity for LLMs during data ingestion and query processing.3 In comparison to more verbose formats like XML or JSON for prose content, Markdown is significantly more lightweight and human-readable, aligning with the "writing format" philosophy intended by its creator, John Gruber.6 Consequently, Markdown serves as an accessible, human-readable intermediary that machines can also leverage effectively, forming an ideal foundation for TTRPG reference documents.

* **Semantic Headings (H1-H6):** Headings are the cornerstone of document structure. A single, unique H1 heading per document, ideally mirroring the filename or the primary entity name (e.g., a specific monster or location), establishes the document's main topic.7 Subsequent headings (H2-H6) should be used to create a logical and consistent hierarchy. For example, an adventure module might use H1 for the adventure title, H2 for chapter titles, H3 for major locations or scenes within a chapter, and H4 for specific encounter elements or room descriptions.5 This hierarchical structure is not only vital for human navigation (often used to generate a Table of Contents) but is also fundamental for LLMs to understand content segmentation, context boundaries, and the relative importance of different information blocks.1  
* **Ordered and Unordered Lists:** Lists are essential for enumerating discrete pieces of information, such as spell components, NPC characteristics, items in a treasure hoard, or steps in a complex game procedure. Markdown's simple syntax for lists (\*, \-, or 1.) is clean, easily readable by humans, and readily parsed by LLMs, helping to distinguish individual items within a collection and understand their relationship.1  
* **Emphasis (Bold and Italics):** Strategic use of bold text (\*\*text\*\* or \_\_text\_\_) is recommended for highlighting key terms, such as "**DC 15 Dexterity saving throw**," labels within stat blocks (e.g., "**Armor Class**"), or the first significant mention of character or location names. Italic text (\*text\* or \_text\_) is well-suited for emphasizing words or phrases in prose, denoting spell names (e.g., *fireball*), book titles, or as a common convention for indicating read-aloud text cues in adventure modules.2 These formatting elements provide valuable semantic hints to LLMs regarding the significance and type of the text they are processing.1  
* **Blockquotes (\>):** The use of blockquotes is the standard and most effective method for delineating text that is intended to be read aloud to players, particularly in adventure modules. This formatting visually separates player-facing descriptions from GM-only notes, significantly enhancing usability during actual gameplay.5 LLMs can also be trained or prompted to recognize blockquoted text as a specific category of content, allowing for differential processing or retrieval.  
* \*\*Code Blocks (Fenced or Indented):\*\* Code blocks are useful for presenting verbatim rules text, pre-formatted stat blocks (if not fully managed by YAML frontmatter, as discussed later), custom script definitions for Virtual Tabletops (VTTs), or even simple ASCII art maps. When using fenced code blocks, specifying the language (e.g.,yaml, json,plaintext) is a best practice, as it aids syntax highlighting for human readers and enables specialized parsing by tools or LLMs.7  
* **Horizontal Rules (--- or \*\*\*):** These provide clear visual separation between distinct sections within a single Markdown file. They can be used, for example, to separate different encounters in an adventure chapter or to distinguish different traits within a monster's description if subheadings are not used for that level of granularity.8  
* **Consistency and Style Guides:** Adherence to a consistent Markdown style across all documents is paramount. This includes choices like using a single type of list marker (e.g., always using \- for unordered lists), maintaining consistent heading levels for similar types of information (e.g., all room descriptions start with an H4), and uniform application of emphasis. Consistency improves human readability and predictability, and significantly simplifies the parsing logic required for automated tools and LLMs.5 The Google Markdown Style Guide, for example, offers excellent general principles that can be adapted for TTRPG documentation.7

### **B. Designing for Human Readability and Navigability**

While structuring for LLMs is crucial, TTRPG documents must remain highly usable for GMs and players.

* **Visual Hierarchy and Scannability:** The effective use of Markdown's structural elements—headings, lists, bolding, and indentation—creates a clear visual flow. This allows GMs to quickly scan a document and locate specific information, whether during game preparation or in the heat of a session at the table.5 A well-structured document, by its nature, is more scannable and less intimidating to approach.  
* **Conciseness and Clarity:** TTRPGs often involve rich lore and intricate rules. However, the text conveying these elements, especially rules and descriptive passages, should be as clear and concise as possible. Jargon should be avoided unless it is a defined game term; where simpler language suffices, it should be preferred. This aids rapid comprehension.  
* **Table of Contents (ToC):** For any document of substantial length, such as an adventure module or a chapter of a rulebook, a Table of Contents is indispensable for navigation. Many Markdown rendering platforms (including those on GitHub, GitLab, and various static site generators) can automatically generate a ToC from the document's headings. If automated ToC generation is not available, a manually curated ToC with links to major sections (using heading anchors like (\#section-title)) should be included at the beginning of the document.7  
* **Logical Information Flow:** The organization of content within a document, and across a collection of documents, should mirror how a GM or player would typically access or utilize that information. For instance, an adventure module often flows from a general overview and plot hooks, to detailed descriptions of locations and scenes, then to NPC details and encounter setups, and finally to treasure and rewards.5 Similarly, rulebooks frequently progress from character creation to core game mechanics, and then to more specific subsystems like magic or combat.9  
* **Effective Cross-Referencing:** The extensive use of Markdown links (\[link text\](target.md\#section)) is vital for interconnecting related pieces of information. For instance, an NPC mentioned in a room description should link directly to their full stat block or detailed character file. A magic item discovered as part of a treasure hoard should link to its complete rules description.5 This practice creates a highly navigable, "wiki-like" experience for the human user, reducing the need to flip through pages or search across multiple documents.

### **C. Structuring for Optimal LLM Ingestion and RAG Performance**

The way Markdown content is structured has a direct and profound impact on its utility for LLM ingestion and, particularly, for Retrieval Augmented Generation systems. The effectiveness of a RAG system hinges on its ability to retrieve the most relevant context (chunks) for a given user query. Markdown's inherent semantic structure provides a superior basis for chunking compared to unstructured text. RAG systems retrieve these chunks to provide context to an LLM for generating an answer, and the relevance of these chunks is paramount for the quality of that answer.10 While fixed-size chunking is simple to implement, it often splits sentences or ideas awkwardly, thereby harming contextual integrity.14 Context-aware or semantic chunking strategies, which aim to preserve meaning within chunks, are generally more effective.11 Markdown's structural elements—headings, paragraphs, lists, blockquotes—naturally delineate semantically related blocks of information.4 For example, all content under an H3 heading titled "The Dragon's Lair" likely pertains to that specific location. Tools and libraries such as LangChain's MarkdownTextSplitter or custom scripts can be configured to split Markdown documents based on headers or other structural markers.16 This means that chunks generated from well-structured Markdown are more likely to be self-contained and contextually rich. By designing TTRPG documents with a clear and consistent Markdown hierarchy, the content is proactively engineered for more effective and meaningful chunking, a foundational requirement for high-performing RAG applications.

* **Granular, Semantically Coherent Sections:** Breaking down content into well-defined, logically distinct sections using Markdown headings (primarily H2, H3, and H4) is critical. Each section should ideally cover a single, focused topic, such as one room in a dungeon, one NPC's background information, or one specific game rule. This granularity directly aids in creating meaningful, context-rich chunks that are optimal for RAG systems.4  
* **Markdown as a Blueprint for Chunking:** The hierarchical structure inherent in Markdown (e.g., H1 \> H2 \> H3 \> paragraph \> list item) provides natural and semantically relevant boundaries for developing chunking strategies. Context-aware chunking algorithms that respect these Markdown structures are generally superior to arbitrary fixed-size chunking methods. This is because they are more likely to preserve the full meaning and context of the information within each chunk, which is vital for the LLM's understanding.11  
* **Metadata Association at the Chunk Level:** While the Markdown content itself forms the body of a chunk, the real power for RAG applications comes from associating rich metadata with each chunk. This metadata, often derived from YAML frontmatter (as will be detailed in Section V) or inferred from the document structure (e.g., parent headings, file name), can include critical information such as the source document, the type of content (e.g., "monster stat block," "room description," "spell details"), the relevant game system, and pertinent tags. This metadata is crucial for enabling effective filtering and boosting of search results within a vector database during the retrieval phase of RAG.10

## **III. A System-Agnostic Framework for TTRPG Reference Documents**

A robust TTRPG reference system requires a foundational structure that is game-system agnostic, allowing for flexibility and broad applicability, while also supporting modular additions for specific game rules and content. This framework should organize information hierarchically, from the broad strokes of a campaign world down to the minutiae of individual game entities.

### **A. Hierarchical Organization: From Campaigns to Individual Entities**

The organization of TTRPG documents should follow a logical hierarchy that reflects the scale and relationship of different pieces of information.

* **Top-Level (Campaign/World):** This tier serves as the root for a specific campaign setting or game world. It can be represented by a root directory (e.g., MyCampaign/) or a central index Markdown file (e.g., MyCampaign/index.md). Content at this level includes overarching information such as world lore, major historical events, pantheons of deities, cosmological details, key factions, and links to specific adventures, geographical regions, or core rule modifications. This approach aligns well with the concept of structuring a "world wiki" for comprehensive reference.18  
* **Mid-Level (Adventures/Regions/Rulebooks):** Sub-directories (e.g., MyCampaign/Adventures/TheSunlessCitadel/, MyCampaign/Regions/SwordCoast/, MyCampaign/Rules/HouseRules/) or large, well-structured Markdown files should be used for these discrete units of content. An adventure module, for example, might be broken down by chapters or major parts, where each could be a sub-folder or a major H2 section within a larger adventure file.5 The typical structure of an encounter (Title, Read-Aloud Text, GM Information, Creatures, Treasure) as described in TTRPG writing guides can be mapped directly to H3/H4 headings and standard Markdown elements like blockquotes and lists.5  
* **Low-Level (Encounters/Locations/NPCs/Items/Monsters/Spells):** Ideally, these granular entities are best managed as individual Markdown files, especially if they are complex or likely to be reused across different parts of the campaign or even in different adventures. Examples include:  
  * MyCampaign/NPCs/ElaraSwiftwind.md  
  * MyCampaign/Locations/Dungeons/SunlessCitadel/Room15\_GoblinBarracks.md  
  * MyCampaign/Monsters/GoblinCutthroat.md  
  * MyCampaign/Items/Magic/Sunsword.md This granular approach is highly recommended for building a robust and queryable knowledge base. It facilitates the "single source of truth" principle, meaning each piece of information has a canonical location, and makes content management, versioning, and inter-linking significantly more efficient.18  
* **File Naming Conventions:** A consistent and descriptive file naming convention is crucial for organization and machine processing. Using prefixes like npc\_, loc\_, item\_, monster\_, spell\_, rule\_ can be very helpful. These prefixes aid in quick identification of content type and can simplify scripting for batch processing or indexing. Hyphens or underscores should be used instead of spaces in filenames to ensure compatibility across different operating systems and web environments (e.g., npc\_baron-von-hendrik.md).

### **B. Standardized Sectioning for Common TTRPG Document Types**

To maintain consistency and improve usability for both humans and LLMs, standardized sectioning within common TTRPG document types is recommended.

* **Adventures:**  
  * \# Adventure Title (H1)  
  * \#\# Introduction (Adventure background, overall synopsis, character hooks, starting information for the GM)  
  * \#\# Dramatis Personae (A list of key Non-Player Characters involved in the adventure, with brief descriptions and links to their full NPC files if they exist separately)  
  * \#\# Chapter X: \[Chapter Name\] (H2) (For multi-chapter adventures)  
    * \#\#\# Scene/Location Y: (H3) (e.g., \#\#\# C1. Public Floor – Low 1 as seen in some adventure formats 5)  
      * \#\#\#\# Read-Aloud Text (H4, followed by a blockquote: \>...) 5  
      * \#\#\#\# GM Information (Background details pertinent to this scene/location, NPC motivations, potential developments, relevant skill check DCs, secret information) 5  
      * \#\#\#\# Creatures (List of monster names, their quantities, and links to their full stat block files, e.g., \* 2x \[\[monster\_goblin\]\]s) 5  
      * \#\#\#\# Hazards (Description of any traps, environmental dangers, or challenging terrain, including detection and disarm DCs, and effects)  
      * \#\#\#\# Treasure (Detailed list of items, currency, and other rewards found here, with links to magic item files if applicable) 5  
      * \#\#\#\# Developments (How the story might progress based on player actions in this specific area or encounter, potential consequences)  
* **Bestiaries/Monster Manuals:**  
  * Each monster should ideally be its own Markdown file (e.g., monster\_aboleth.md). This modularity is key.  
  * \# Monster Name (H1) (e.g., \# Aboleth from a D\&D SRD example 21)  
  * YAML frontmatter (see Section V for detailed schema) should contain all structured statistical data (AC, HP, abilities, attacks, etc.).  
  * \#\# Description (Lore about the monster, its physical appearance, typical behavior, societal structure if applicable, and role in the game world).  
  * \#\# Habitat (Typical environments or planes where the monster is found).  
  * \#\# Combat Tactics (How the monster typically behaves in a fight, its common strategies, preferred targets, and when it might flee).  
  * (Optional) \#\# Stat Block (If not relying solely on YAML frontmatter and a rendering plugin/script for display, a Markdown-formatted stat block can be included here. However, for data integrity and LLM processing, storing the canonical data in YAML is strongly preferred).  
* **Rulebooks/System Reference Documents (SRDs):**  
  * These documents should be organized by major game systems or rule categories (e.g., Character Creation, Abilities & Skills, Combat, Magic & Spellcasting, Equipment, Conditions, Exploration). Each major system could be a dedicated folder containing multiple files, or a top-level H2 section in a larger, consolidated SRD file.  
  * A clear, hierarchical heading structure (H2, H3, H4, etc.) should be used for each rule, sub-rule, and specific game term to ensure easy navigation and logical segmentation.7 A common structure for a player-focused rulebook includes: Introduction / Basic Mechanics; Character Creation (including Advancement); Stats, Classes, Equipment; Exploration Rules (often with Combat as a subsection); Downtime Activities; GM Rules (if included); Bestiary (or references); and Appendices with tables and charts.9  
  * Extensive internal linking is crucial for rulebooks. Game terms, conditions, actions, and spells mentioned in one rule should link to their detailed descriptions elsewhere in the SRD. This creates a deeply interconnected web of rules that is easy for players to navigate and for LLMs to traverse when resolving rule queries.

### **C. Cross-Referencing and Linking Strategies within the Markdown Ecosystem**

Effective cross-referencing is the glue that binds individual Markdown files into a cohesive and navigable TTRPG knowledge base. TTRPG information is inherently relational: an NPC resides in a location, possesses items, knows spells, and interacts with other NPCs and monsters. Without explicit links, these connections remain merely implicit within the prose. Markdown links serve to make these relationships explicit and machine-readable. When parsed, this network of links forms a graph structure where files are nodes and links are edges, representing these relationships. This effectively creates a knowledge graph. For RAG systems, when an LLM retrieves a chunk of text that includes such a link, it has an explicit pointer to another potentially relevant piece of information. Advanced RAG systems could be designed to follow these links to gather a more complete context before generating an answer. This explicit graph structure is generally more reliable for establishing connections than relying solely on semantic similarity between text embeddings, especially for the nuanced and specific relationships found in TTRPG content. Therefore, diligent cross-referencing is not just a convenience for human readers but a critical step in building a high-quality, interconnected knowledge base suitable for advanced AI applications.

* **Internal Links:** These are fundamental for creating a navigable web of information. Most Markdown environments, including popular TTRPG note-taking tools like Obsidian, support Wikilinks (e.g., \] or \]) or standard Markdown links that use relative paths (e.g., (./path/to/target\_file.md\#target-heading)).22  
  * **Recommendation:** For maximum portability and compatibility with a wider range of tools, including static site generators and version control platforms like GitHub, using relative file path links is generally more robust.24 Tools like Docusaurus are designed to process these relative paths, automatically converting them and removing the .md extension in the final URL output.24 It is good practice to always start relative paths with ./ (for same directory) or ../ (for parent directory) to ensure clarity and correct resolution.24  
  * A consistent linking style should be adopted and maintained across the entire corpus of documents.7  
* **Linking to Specific Sections/Headings:** Most Markdown renderers automatically create HTML anchors for headings within a document. This allows for direct linking to specific sections (e.g., (rules.md\#combat)). This capability is crucial for precise navigation, allowing users and systems to jump directly to the exact piece of information required, rather than just to the top of a long document.23  
* **Benefits for Humans and LLMs:** For human users, a well-linked system creates an intuitive and efficient "wiki-like" experience, making it easy to explore related concepts and follow threads of information. For LLMs, these explicit links provide strong relational signals. They help the model understand the connections between different entities, concepts, and rules, which is invaluable for RAG when it needs to synthesize answers from multiple information sources or understand the context of a retrieved chunk.

## **IV. Modular Design for Game-Specific Formatting**

While the core framework outlined in Section III aims for system agnosticism, TTRPGs are inherently system-specific when it comes to mechanics like stat blocks, spell effects, and action resolution. A modular design allows these specific elements to be integrated cleanly. The choice of how to represent complex TTRPG elements, such as detailed monster stat blocks or intricate tables, involves a trade-off. Representing them directly in Markdown using its native syntax might enhance the immediate readability of the raw Markdown file for simpler elements. However, for data-centric LLM processing and ensuring data integrity, using YAML frontmatter to store the structured data, which is then rendered into a human-readable format by a separate tool or plugin, is often superior. YAML provides a highly structured, key-value format that is easily parsed programmatically, which is essential for reliable LLM ingestion and RAG systems.5 Tools like Obsidian's Fantasy Statblocks plugin can render this YAML data into a visually appealing format within the Markdown preview, offering the best of both worlds.25 Thus, for complex, data-rich elements, YAML frontmatter is generally the preferred method for LLM/RAG applications, even if it makes the raw Markdown file less immediately "readable" without a dedicated renderer. Simpler elements or textual summaries can still effectively use inline Markdown.

### **A. Integrating System-Specific Elements into the Agnostic Framework**

The system-agnostic framework provides the overarching structure (file organization, common section headings like "Description," "GM Information," "Treasure"). Game-specific elements, such as the precise format of a monster stat block, a spell's mechanical description, or the definition of special actions, are then implemented *within* these standard sections or, more effectively, through system-specific YAML frontmatter schemas.  
A crucial strategy for managing this modularity is the consistent use of a game\_system field within the YAML frontmatter of every relevant Markdown file (e.g., game\_system: dnd5e, game\_system: pathfinder2e, game\_system: coc7e). This field acts as a discriminator, allowing processing tools, rendering engines (like Obsidian plugins specifically designed for certain game systems), and LLMs to identify the system context and apply appropriate parsing rules, display templates, or interpretation logic. For instance, a section headed \#\# Stat Block in a monster's Markdown file would be interpreted and rendered differently based on the value of the game\_system tag, potentially by different plugins or custom display logic tailored to that system's conventions.

### **B. Case Study: Dungeons & Dragons 5e**

Dungeons & Dragons 5th Edition (D\&D 5e) has a well-established format for its elements, particularly monster stat blocks and spell descriptions.

* **Stat Blocks:**  
  * **Direct Markdown Representation:** D\&D 5e stat blocks can be represented directly in Markdown using a combination of bold text for labels (e.g., **Armor Class**, **Hit Points**), paragraphs for descriptive traits and actions, and lists for multi-part actions or legendary actions. Examples from resources like LootTheRoom 5 and AIDE DD 31 demonstrate this approach. The Homebrewery tool, popular for creating D\&D 5e homebrew content, also relies heavily on Markdown for formatting stat blocks.33  
  * **YAML Frontmatter with Renderer:** This is generally the preferred method for ensuring data integrity and optimal usability for LLMs. The complete statistical data for a monster (AC, HP, speeds, ability scores, skills, saving throws, traits, actions, reactions, legendary actions, etc.) is stored in a structured YAML format within the frontmatter of the monster's Markdown file. An Obsidian plugin, such as Fantasy Statblocks 25, or a custom script can then parse this YAML data and render it as a visually conventional D\&D 5e stat block within the Markdown preview. The ebullient/ttrpg-convert-cli tool is designed to convert 5eTools JSON data into Markdown, frequently outputting YAML tailored for the Fantasy Statblocks plugin, and utilizes templates like monster2md-yamlStatblock-body.txt for this purpose.25 An example of a simple YAML-like structure for a Medusa from 35 includes name: Medusa, type: monstrosity, cr: 6\.  
  * A representative, though simplified, D\&D 5e stat block using a mix of YAML frontmatter (for structured data) and Markdown body (for descriptive text and rendering guidance) might look like this for a Goblin:  
    YAML  
    \---  
    name: Goblin  
    size: Small  
    type: humanoid  
    subtype: goblinoid  
    alignment: neutral evil  
    ac: 15  
    ac\_description: (leather armor, shield)  
    hp: 7  
    hp\_formula: 2d6  
    speed: "30 ft."  
    ability\_scores:  
      str: 8  
      dex: 14  
      con: 10  
      int: 10  
      wis: 8  
      cha: 8  
    skills:  
      stealth: 6 \# Includes proficiency bonus  
    senses:  
      darkvision: 60 ft.  
      passive\_perception: 9  
    languages:  
      \- Common  
      \- Goblin  
    cr: 1/4  
    xp: 50  
    traits:  
      \- name: Nimble Escape  
        description: The goblin can take the Disengage or Hide action as a bonus action on each of its turns.  
    actions:  
      \- name: Scimitar  
        type: Melee Weapon Attack  
        attack\_bonus: \+4  
        reach: 5 ft.  
        target: one target  
        damage\_dice: 1d6 \+ 2  
        damage\_type: slashing  
        average\_damage: 5  
      \- name: Shortbow  
        type: Ranged Weapon Attack  
        attack\_bonus: \+4  
        range: 80/320 ft.  
        target: one target  
        damage\_dice: 1d6 \+ 2  
        damage\_type: piercing  
        average\_damage: 5  
    \---  
    \# Goblin

    \*Small humanoid (goblinoid), neutral evil\*

    \*\*Armor Class\*\* {{frontmatter.ac}} {{frontmatter.ac\_description}}  
    \*\*Hit Points\*\* {{frontmatter.hp}} ({{frontmatter.hp\_formula}})  
    \*\*Speed\*\* {{frontmatter.speed}}

| STR | DEX | CON | INT | WIS | CHA |
| ----- | ----- | ----- | ----- | ----- | ----- |
| {{frontmatter.ability\_scores.str}} (-1) | {{frontmatter.ability\_scores.dex}} (+2) | {{frontmatter.ability\_scores.con}} (+0) | {{frontmatter.ability\_scores.int}} (+0) | {{frontmatter.ability\_scores.wis}} (-1) | {{frontmatter.ability\_scores.cha}} (-1) |

    \*\*Skills\*\* Stealth \+{{frontmatter.skills.stealth}}  
    \*\*Senses\*\* darkvision {{frontmatter.senses.darkvision}}, passive Perception {{frontmatter.senses.passive\_perception}}  
    \*\*Languages\*\* {{ frontmatter.languages | join: ", " }}  
    \*\*Challenge\*\* {{frontmatter.cr}} ({{frontmatter.xp}} XP)

    \*\*\*Nimble Escape.\*\*\* {{ frontmatter.traits | find\_trait\_by\_name: "Nimble Escape" | property: "description" }}

    \#\#\# Actions  
    \*\*\*Scimitar.\*\*\* \*{{ frontmatter.actions | find\_action\_by\_name: "Scimitar" | property: "type" }}:\* \+{{ frontmatter.actions | find\_action\_by\_name: "Scimitar" | property: "attack\_bonus" }} to hit, reach {{ frontmatter.actions | find\_action\_by\_name: "Scimitar" | property: "reach" }}, {{ frontmatter.actions | find\_action\_by\_name: "Scimitar" | property: "target" }}. \*Hit:\* {{ frontmatter.actions | find\_action\_by\_name: "Scimitar" | property: "average\_damage" }} ({{ frontmatter.actions | find\_action\_by\_name: "Scimitar" | property: "damage\_dice" }}) {{ frontmatter.actions | find\_action\_by\_name: "Scimitar" | property: "damage\_type" }} damage.

    \*\*\*Shortbow.\*\*\* \*{{ frontmatter.actions | find\_action\_by\_name: "Shortbow" | property: "type" }}:\* \+{{ frontmatter.actions | find\_action\_by\_name: "Shortbow" | property: "attack\_bonus" }} to hit, range {{ frontmatter.actions | find\_action\_by\_name: "Shortbow" | property: "range" }}, {{ frontmatter.actions | find\_action\_by\_name: "Shortbow" | property: "target" }}. \*Hit:\* {{ frontmatter.actions | find\_action\_by\_name: "Shortbow" | property: "average\_damage" }} ({{ frontmatter.actions | find\_action\_by\_name: "Shortbow" | property: "damage\_dice" }}) {{ frontmatter.actions | find\_action\_by\_name: "Shortbow" | property: "damage\_type" }} damage.  
    \`\`\`  
    (Note: The \`{{...}}\` syntax above is illustrative of how a templating engine might access YAML frontmatter; actual syntax depends on the specific tool, e.g., Liquid, Jinja2, or plugin-specific syntax.)  
    The BTMorton dnd-5e-srd GitHub repository offers SRD monsters in Markdown, serving as a valuable reference for formatting conventions.\[21, 36, 37\] For instance\[21\] shows Markdown tables used for size categories, hit dice by size, proficiency bonus by challenge rating, and ability scores.

* **Spells:** Spell information is also highly structured. YAML frontmatter is ideal for capturing attributes like spell level, school of magic, casting time, range, components (verbal, somatic, material), duration, and a brief summary. The full descriptive text of the spell would reside in the Markdown body.  
  * An example YAML structure for a spell, based on information from 38:  
    YAML  
    \---  
    name: "Shield of Faith"  
    level: 1  
    school: Abjuration  
    casting\_time: "1 bonus action"  
    range: "60 feet"  
    components:  
    material: "a small parchment with a holy symbol inscribed on it"  
    duration: "Concentration, up to 10 minutes"  
    description\_short: "A shimmering field appears and surrounds a creature of your choice within range, granting it a \+2 bonus to AC for the duration."  
    classes: \[Cleric, Paladin\]  
    source\_book: "Player's Handbook"  
    game\_system: dnd5e  
    \---  
    \# Shield of Faith  
    \*1st-level abjuration\*  
    \*\*Casting Time:\*\* {{frontmatter.casting\_time}}  
    \*\*Range:\*\* {{frontmatter.range}}  
    \*\*Components:\*\* {{frontmatter.components | join: ", " }}{% if frontmatter.material %} ({{frontmatter.material}}){% endif %}  
    \*\*Duration:\*\* {{frontmatter.duration}}

    {{frontmatter.description\_short}} The full spell description from the sourcebook would follow here.

* **Items:** Similar to spells, magic items and even mundane equipment benefit from structured YAML frontmatter detailing item type (weapon, armor, potion, wondrous item), rarity, attunement requirements, specific properties (e.g., weapon damage, armor AC), weight, cost, and a summary of effects. The detailed lore or descriptive text can be in the Markdown body.

### **C. Case Study: Pathfinder 2e**

Pathfinder Second Edition (PF2e) features its own distinct presentation for stat blocks and actions, notably its use of specific icons to denote action costs (e.g., single action, two-action activity, reaction, free action).

* **Stat Blocks:** As with D\&D 5e, PF2e stat blocks can be represented through a combination of direct Markdown formatting and YAML frontmatter. The pf2e-statblock-for-obsidian plugin provides a specific Markdown syntax to be used within a pf2e-stats code block.8 This syntax includes:  
  * \# Name (H1 for the creature or item name)  
  * \#\# Type Level (H2 for the type and level, e.g., \#\# Creature 5 or \#\# Item 8\)  
  * Traits are wrapped in double-equals signs: \==Unique== \==Magical== \==Humanoid==  
  * Action Icons are represented by specific bracketed keywords: \[one-action\] for a single action, \[two-actions\] for a two-action activity, \[three-actions\] for a three-action activity, \[reaction\] for a reaction, and \[free-action\] for a free action.  
  * Horizontal rules (---) are used as dividers to separate sections within the stat block.

  * ## **An example from the plugin documentation 8: pf2e-stats**     **Awesome Belt of Various Actions**     **Item 25**

---

...[source](https://github.com/pixley/pf2e-statblock-for-obsidian) detailed equipment lists and complex combat maneuver outcomes.

* **Basic Markdown Tables:** For simple tabular data, standard Markdown table syntax (using pipes | to define columns and hyphens \- to separate the header row from data rows) is generally sufficient and widely supported.40 This format is readable in raw Markdown and renders cleanly in most viewers.  
  * Example:

| Die Roll | Encounter |
| :---- | :---- |
| 1-2 | Goblin Patrol |
| 3-4 | Giant Spiders |
| 5-6 | Merchant Caravan |

* **Complex Tables (Merged Cells, Multi-layered Headers):** Standard Markdown syntax has limitations when it comes to representing more complex table structures, such as those requiring merged cells (colspan/rowspan) or multiple header rows.6  
  * **HTML Tables:** For these highly complex tables, embedding raw HTML table tags (\<table\>, \<tr\>, \<th\>, \<td\>, utilizing colspan and rowspan attributes) directly within the Markdown document is a viable and often necessary workaround. Most Markdown parsers are designed to pass through HTML, allowing these tables to be rendered correctly in the final output.6 While this provides maximum control over the table's structure and appearance, it significantly reduces the readability of the raw Markdown source and can make manual editing more cumbersome.  
  * **Specialized Converters/Plugins:** Some PDF-to-Markdown conversion tools or specialized Markdown editing environments and plugins might offer extended syntax or post-processing capabilities to handle complex tables more gracefully. For instance, pymupdf4llm is capable of extracting tables from PDFs and converting them into Markdown format.17 Similarly, Hyland Document Filters offer options for converting PDF tables to Markdown, including using HTML for more complex structures.47 The pdfplumber library, when combined with tabulate, can also be used to extract table data from PDFs and output it in Markdown format.48  
* **LLM Processing of Tables:** Well-structured basic Markdown tables are generally parsable by LLMs. For very complex tables, especially those represented using embedded HTML, providing a simplified textual description or summary of the table's key information alongside the complex structure might be beneficial for RAG systems. LLMs can interpret tabular data effectively if it is presented clearly and unambiguously.49

The choice between basic Markdown tables and embedded HTML for TTRPG data involves weighing the simplicity and raw readability of Markdown against the structural fidelity offered by HTML for complex layouts. The following table summarizes these considerations:  
**Table 1: Comparison of Markdown Table Syntax vs. HTML for Complex TTRPG Data**

| Feature | Basic Markdown Table | HTML Table in Markdown |
| :---- | :---- | :---- |
| **Merged Cells (colspan)** | Not supported | Supported via colspan attribute |
| **Merged Cells (rowspan)** | Not supported | Supported via rowspan attribute |
| **Multi-Layered Headers** | Limited; typically single header row | Fully supported by nesting \<th\> elements |
| **Cell Alignment** | Basic left, right, center via colons in separator line | Full CSS control possible if styles are applied |
| **Readability of Raw MD** | High | Low; HTML tags obscure content |
| **Ease of Manual Editing** | High | Moderate to Low; prone to syntax errors |
| **LLM Parsability** | Generally good for simple structures | Can be challenging for LLMs without HTML parsing capabilities; may require pre-processing |
| **Rendering Consistency** | Dependent on Markdown parser | Consistent if HTML is well-formed |
| **Complexity Handled** | Low to Moderate | High |

This table illustrates that for TTRPGs, which often feature complex tables (e.g., detailed equipment lists with subcategories, monster ability charts with conditional effects, or multi-axis random generation tables), embedded HTML may be necessary to preserve the intended structure, despite the impact on raw Markdown readability. For LLM consumption, such HTML tables might need to be specifically processed or accompanied by a textual summary to ensure accurate interpretation.

## **V. Strategic File Organization and YAML Frontmatter**

The organization of Markdown files and the metadata embedded within them are critical for creating a TTRPG reference system that is both human-navigable and machine-actionable. Splitting content into granular files, each representing a distinct entity like an NPC or a location, coupled with rich YAML frontmatter, establishes a single source of truth and lays the groundwork for advanced functionalities, including those envisioned for AI Game Masters (AIGMs). The concept of granular, YAML-defined entities serving as building blocks for sophisticated AIGM functionalities is pivotal. Without this structured, machine-readable data, AIGMs would have to rely on less reliable Natural Language Processing (NLP) over prose, making tasks such as generating modifiable adventure copies significantly more challenging and less precise. An AIGM needs to understand the components of an adventure—locations, NPCs, items, monsters, encounters—and their properties and relationships. Storing these components as individual Markdown files with comprehensive YAML frontmatter transforms them into discrete, identifiable, and programmatically accessible objects.19 An AIGM can then read the YAML of an NPC, for instance, understand its attributes (like stats, faction allegiance, or current location), and subsequently modify these attributes or create a new file with altered attributes. If this information were only available in narrative prose, the AIGM would face the complex task of extracting and then modifying it, with a substantially higher probability of error. Thus, YAML frontmatter provides the necessary structured "handles" for AIGMs to effectively manipulate adventure components.

### **A. Granularity and Modularity: When to Split Content into Separate Files**

The decision of when to split TTRPG content into separate Markdown files versus keeping it within a larger document hinges on the principles of granularity and modularity.

* **Benefits of Granular Files:**  
  * **Single Source of Truth (SSoT):** Each distinct game entity (e.g., an NPC, a specific magic item, a unique location, a monster) is defined in one canonical file. Any updates or corrections to that entity are made in this single place, ensuring consistency across all references.  
  * **Improved Reusability:** An NPC file, for example, can be linked from multiple adventure modules, encounter descriptions, or location notes without duplicating the NPC's core information. This promotes efficiency and reduces the risk of inconsistencies.  
  * **Better for RAG:** Smaller, more focused files generally lead to more precise chunking and retrieval for RAG systems. LLMs receive more targeted and relevant context when a retrieved chunk pertains to a single, well-defined entity.19  
  * **Easier Management:** A collection of granular files is often simpler to navigate, edit, and manage under version control systems (like Git) compared to monolithic documents. Finding and modifying a specific piece of information becomes more straightforward.  
  * **AIGM Support:** This granularity is essential for AIGMs that might need to copy, modify, or reference specific game entities programmatically. For example, an AIGM tasked with "take NPC X and move them to Location Y" or "create a variant of Monster Z with increased hit points" can operate much more effectively if NPC X, Location Y, and Monster Z are defined in their own structured files \[User Query\].  
* **When to Split Content:**  
  * **Frequently Referenced Entities:** Any game element that is likely to be referenced multiple times (e.g., common NPCs, recurring monsters, standard magic items, key city locations) should be in its own file.  
  * **Entities with Significant Data:** Entities that possess a substantial amount of associated data (e.g., a major city with numerous districts, points of interest, and NPCs, versus a minor, single-room cave) benefit from dedicated files.  
  * **Entities Requiring Distinct Metadata:** When an entity has a unique set of metadata attributes that are best managed in its own YAML frontmatter block, a separate file is warranted. Many TTRPG campaign management approaches, particularly within tools like Obsidian, advocate for creating one note (Markdown file) per entity, such as NPCs, locations, items, monsters, and even individual game sessions.18 Folder-based categorization is also a common and effective strategy for organizing these granular files.18

### **B. YAML Frontmatter as the Single Source of Truth**

YAML frontmatter, a block of YAML-formatted key-value pairs at the top of a Markdown file, serves as the primary mechanism for storing structured metadata about the file's content. This metadata is machine-readable and queryable, making it invaluable for RAG systems, content management tools, and AIGMs.5

* **Purpose:** To encapsulate all the structured, factual data about an entity, separating it from the narrative or descriptive prose in the Markdown body.  
* **Syntax:** YAML frontmatter is enclosed by triple-dashed lines (---) at the very beginning of the Markdown file. It uses standard YAML syntax, which emphasizes human readability with its indentation-based structure.53  
* **Essential Universal Metadata Fields (Game-System Agnostic Core):** A set of common fields should be considered for nearly all TTRPG entity files to ensure a baseline of interoperability and queryability:  
  * id: A globally unique identifier for the entity (e.g., a UUID, or a human-readable namespaced string like npc.elara\_swiftwind). This is crucial for stable inter-linking and for use as a primary key if the data is ingested into a relational database.  
  * entity\_type: Specifies the kind of entity the file represents (e.g., npc, location, item, monster, spell, adventure, rule\_section). This allows for efficient filtering and the application of type-specific processing or rendering.  
  * name: The human-readable name of the entity (e.g., "Elara Swiftwind," "The Sunken Temple").  
  * game\_system: Indicates the TTRPG system the entity belongs to (e.g., dnd5e, pathfinder2e, coc7e, agnostic). This is vital for modularity.  
  * source\_book: The original sourcebook or material for the entity (e.g., "Player's Handbook," "Lost Mines of Phandelver," "Custom Homebrew").  
  * tags: A list of relevant keywords or categories for searching, filtering, and categorization (e.g., \["undead", "swamp\_encounter", "quest\_giver", "level\_1\_spell"\]).56  
  * aliases: A list of alternative names, nicknames, or common misspellings for the entity, improving searchability.52  
  * summary: A brief one or two-sentence description of the entity, useful for generating previews, tooltips, or providing concise context in RAG results.  
* Detailed YAML Schemas for Key TTRPG Entities:  
  The following are illustrative YAML schemas for common TTRPG entities, demonstrating how structured data can be captured. These can be expanded or adapted based on specific game system needs.  
  * **NPCs:**  
    YAML  
    \---  
    id: npc.elara\_swiftwind  
    entity\_type: npc  
    name: Elara Swiftwind  
    game\_system: dnd5e  
    source\_book: "Custom Campaign"  
    tags: \["quest\_giver", "elf", "ranger", "harper\_agent"\]  
    aliases: \["Whisperwind", "Lady Elara"\]  
    summary: "A stoic elf ranger and Harper agent, known for her skill with the bow and knowledge of the northern wilds."  
    status: alive  
    race: Wood Elf  
    class\_levels: Ranger 5  
    alignment: Neutral Good  
    stats\_id: statblock.elara\_swiftwind \# Link to a separate stat block file or embed key stats  
    description\_physical: "Tall and slender with long, braided auburn hair and keen green eyes. Typically wears practical leather armor."  
    description\_personality: "Reserved and observant, but fiercely loyal to her allies and dedicated to her cause."  
    motivations: \["Protect the innocent", "Preserve the balance of nature"\]  
    relationships:  
      \- target\_id: npc.borin\_stonebeard  
        type: ally  
        details: "Respectful working relationship."  
      \- target\_id: faction.zhentarim  
        type: enemy  
        details: "Actively opposes their regional activities."  
    current\_location\_id: loc.phandalin\_stonehill\_inn  
    inventory\_ids: \["item.longbow\_plus\_1", "item.potion\_healing\_x2"\]  
    \---  
    (Markdown body for detailed background, roleplaying notes, quest details)

    Obsidian TTRPG setups often include YAML frontmatter for creatures and NPCs.27  
  * **Locations:**  
    YAML  
    \---  
    id: loc.phandalin\_stonehill\_inn  
    entity\_type: location  
    name: Stonehill Inn  
    game\_system: dnd5e  
    source\_book: "Lost Mines of Phandelver"  
    tags: \["phandalin", "inn", "social\_hub", "quest\_source"\]  
    summary: "A modest, welcoming inn in the town of Phandalin, run by Toblen Stonehill."  
    location\_type: building (inn)  
    parent\_location\_id: loc.phandalin  
    description\_general: "The Stonehill Inn is a two-story building with a common room, kitchen, and several guest rooms."  
    atmosphere: "Generally warm and noisy, a common gathering place for locals and travelers."  
    points\_of\_interest:  
      \- name: Common Room Bar  
        description: "Toblen Stonehill can usually be found here. Various rumors can be overheard."  
      \- name: Private Booths  
        description: "Offer some privacy for conversations."  
    connections:  
      \- target\_location\_id: loc.phandalin\_town\_square  
        travel\_method: walking  
        travel\_time: "1 minute"  
    hazards:  
    npcs\_present\_ids: \["npc.toblen\_stonehill", "npc.elsa\_the\_barmaid"\]  
    \---  
    (Markdown body for detailed room descriptions, map links, specific events)

    Generic YAML usage in Obsidian often involves simple key-value pairs that can be adapted for locations.54  
  * **Items (Magic Item Example):**  
    YAML  
    \---  
    id: item.sunsword  
    entity\_type: item  
    name: Sunsword  
    game\_system: dnd5e  
    source\_book: "Curse of Strahd"  
    tags: \["magic\_item", "weapon", "sword", "sentient", "holy", "undead\_bane"\]  
    summary: "A sentient lawful good longsword that sheds bright light and deals extra radiant damage, especially to undead."  
    item\_type: weapon (longsword)  
    rarity: legendary  
    attunement: "Requires attunement by a creature of good alignment"  
    properties:  
      damage: 1d8 slashing (or 1d10 versatile)  
      bonus\_attack\_damage: \+2  
      extra\_damage\_radiant: 1d8  
      extra\_damage\_vs\_undead: "Additional 1d8 radiant (total 2d8 extra vs undead)"  
    description\_visual: "A longsword hilt from which a blade of pure sunlight issues when activated."  
    effects:  
      \- "Sheds bright light in a 15-foot radius and dim light for an additional 15 feet."  
      \- "Grants advantage on saving throws against spells and other magical effects from fiends and undead."  
    sentience\_details: "Contains the spirit of Sergei von Zarovich. Communicates via empathy."  
    charges: null  
    weight: 3 lb.  
    cost: "Priceless (artifact)"  
    \---  
    (Markdown body for lore, history, detailed sentient properties, and activation methods)

    Discussions on narrative-rich magic items suggest properties that could be well-defined in YAML.58 Frontmatter examples for publishing items to Confluence also show array and string usage.57  
  * **Spells:**  
    YAML  
    \---  
    id: spell.fireball\_dnd5e  
    entity\_type: spell  
    name: Fireball  
    game\_system: dnd5e  
    source\_book: "Player's Handbook"  
    tags: \["evocation", "fire\_damage", "area\_of\_effect", "wizard\_spell", "sorcerer\_spell"\]  
    summary: "A bright streak flashes from your pointing finger to a point you choose within range and then blossoms with a low roar into an explosion of flame."  
    spell\_level: 3  
    school\_of\_magic: Evocation  
    casting\_time: "1 action"  
    range: "150 feet"  
    components:  
    material\_description: "a tiny ball of bat guano and sulfur"  
    duration: Instantaneous  
    area\_of\_effect: "20-foot-radius sphere"  
    saving\_throw: "Dexterity"  
    save\_effect: "half damage on success"  
    spell\_resistance: "No" \# Or applicable rule  
    description\_short: "Explosion of flame dealing 8d6 fire damage in a 20-foot radius."  
    damage\_dice: 8d6  
    damage\_type: fire  
    classes:  
    \---  
    (Markdown body for the full, official spell description text, including "At Higher Levels" section)

    An example for spell frontmatter from 38 includes fields like Level, Casting\_Time, Range\_Area, Components, Duration, School, ClassSpellList, Attack\_Save, and Damage\_Effect.  
  * **Monsters:**  
    YAML  
    \---  
    id: monster.ancient\_red\_dragon\_dnd5e  
    entity\_type: monster  
    name: Ancient Red Dragon  
    game\_system: dnd5e  
    source\_book: "Monster Manual"  
    tags: \["dragon", "legendary", "fire", "gargantuan"\]  
    summary: "A gargantuan, immensely powerful red dragon, embodying greed and destruction, with a devastating fire breath."  
    size: Gargantuan  
    monster\_type: dragon  
    subtype: null  
    alignment: chaotic evil  
    armor\_class: 22  
    ac\_description: (natural armor)  
    hit\_points: 546  
    hp\_formula: 28d20 \+ 252  
    speed:  
      walk: 40 ft.  
      fly: 80 ft.  
      climb: 40 ft.  
    ability\_scores:  
      str: 30  
      dex: 10  
      con: 29  
      int: 18  
      wis: 15  
      cha: 23  
    saving\_throws:  
      dex: \+7  
      con: \+16  
      wis: \+9  
      cha: \+13  
    skills:  
      perception: \+16  
      stealth: \+7  
    damage\_vulnerabilities:  
    damage\_resistances:  
    damage\_immunities: \[fire\]  
    condition\_immunities:  
    senses:  
      blindsight: 60 ft.  
      darkvision: 120 ft.  
      passive\_perception: 26  
    languages: \[Common, Draconic\]  
    challenge\_rating: 24  
    xp: 62000  
    special\_abilities:  
      \- name: Legendary Resistance (3/Day)  
        description: If the dragon fails a saving throw, it can choose to succeed instead.  
      \- name: Frightful Presence  
        description: "Each creature of the dragon's choice that is within 120 feet of the dragon and aware of it must succeed on a DC 21 Wisdom saving throw or become frightened for 1 minute. A creature can repeat the saving throw at the end of each of its turns, ending the effect on itself on a success. If a creature's saving throw is successful or the effect ends for it, the creature is immune to the dragon's Frightful Presence for the next 24 hours."  
    actions:  
      \- name: Multiattack  
        description: "The dragon can use its Frightful Presence. It then makes three attacks: one with its bite and two with its claws."  
      \- name: Bite  
        type: Melee Weapon Attack  
        attack\_bonus: \+17  
        reach: 15 ft.  
        target: one target  
        damage\_dice: 2d10 \+ 10  
        damage\_type: piercing  
        extra\_damage\_dice: 4d6  
        extra\_damage\_type: fire  
        average\_damage: 21  
        average\_extra\_damage: 14  
      \#... other actions like Claw, Tail, Fire Breath  
    legendary\_actions:  
      \- name: Detect  
        description: The dragon makes a Wisdom (Perception) check.  
      \- name: Tail Attack  
        description: The dragon makes a tail attack.  
      \- name: Wing Attack (Costs 2 Actions)  
        description: "The dragon beats its wings. Each creature within 15 feet of the dragon must succeed on a DC 25 Dexterity saving throw or take 17 (2d6 \+ 10\) bludgeoning damage and be knocked prone. The dragon can then fly up to half its flying speed."  
    lair\_actions: \# Optional, if applicable  
      \- description: "On initiative count 20 (losing initiative ties), the dragon takes a lair action to cause one of the following effects..."  
    \---  
    (Markdown body for lore, detailed descriptions of abilities, lair details, etc.)

    Examples of D\&D stat blocks with YAML-like structures include 35 (name: Medusa, type: monstrosity, cr: 6). The Obsidian Fantasy Statblocks plugin heavily uses YAML frontmatter, as shown in 29 with fields like statblock: true, name, level, alignment, size, trait\_03, and nested perception data. The plugin documentation also explicitly states that creatures can be defined in frontmatter with statblock: true and a name parameter.28 Online D\&D 5e stat block generators often list numerous fields that map well to a YAML structure.59

**Table 2: Example YAML Frontmatter Schemas for Core TTRPG Entities**

| Entity Type | Field Name | Data Type | Example Value | Description/Purpose |
| :---- | :---- | :---- | :---- | :---- |
| **NPC** | id | String | npc.grom\_the\_blacksmith | Unique identifier for the NPC. |
|  | entity\_type | String | npc | Specifies the type of entity. |
|  | name | String | Grom the Blacksmith | Human-readable name. |
|  | game\_system | String | dnd5e | Game system this NPC belongs to. |
|  | race | String | Dwarf | NPC's race or species. |
|  | alignment | String | Lawful Good | NPC's moral and ethical alignment. |
|  | stats\_id | String | statblock.grom\_commoner\_variant | Link to a separate stat block file or identifier. |
|  | motivations | List (String) | \["Protect family", "Forge masterwork items"\] | Key driving forces for the NPC. |
|  | current\_location\_id | String | loc.town\_anvil\_forge | Current whereabouts of the NPC. |
| **Location** | id | String | loc.whispering\_woods\_shrine | Unique identifier for the location. |
|  | entity\_type | String | location | Specifies the type of entity. |
|  | name | String | Shrine in the Whispering Woods | Human-readable name. |
|  | location\_type | String | sacred\_grove | Type of location (e.g., city, dungeon, room, forest). |
|  | parent\_location\_id | String | loc.whispering\_woods | Link to a larger containing location, for hierarchy. |
|  | tags | List (String) | \["forest", "ruins", "ancient", "hidden"\] | Descriptive tags for searching and categorization. |
|  | points\_of\_interest | List (Object) | \[{name: "Altar", desc: "Ancient stone altar"}\] | Key features or sub-areas within the location. |
| **Item** | id | String | item.potion\_greater\_healing | Unique identifier for the item. |
|  | entity\_type | String | item | Specifies the type of entity. |
|  | name | String | Potion of Greater Healing | Human-readable name. |
|  | item\_type | String | potion | Category of item (e.g., weapon, armor, potion, wondrous). |
|  | rarity | String | Uncommon | Rarity level of the item (system-specific). |
|  | attunement | Boolean/String | false | Whether attunement is required, or description of requirement. |
|  | effects\_summary | String | Restores 4d4 \+ 4 hit points. | Brief summary of the item's primary effect. |
|  | weight | Number | 0.5 | Weight of the item (e.g., in lbs). |
|  | cost\_gp | Number | 250 | Typical cost in gold pieces (system-specific). |
| **Spell** | id | String | spell.magic\_missile\_pf2e | Unique identifier for the spell. |
|  | entity\_type | String | spell | Specifies the type of entity. |
|  | name | String | Magic Missile | Human-readable name. |
|  | game\_system | String | pathfinder2e | Game system this spell belongs to. |
|  | level | Number | 1 | Spell level. |
|  | school\_of\_magic | String | Evocation | Magical school or tradition. |
|  | casting\_time | String | 1 action | Time required to cast the spell. |
|  | range | String | 120 feet | Maximum range of the spell. |
|  | components | List (String) | \`\` | Verbal, Somatic, Material components. |
|  | duration | String | Instantaneous | How long the spell's effects last. |
| **Monster** | id | String | monster.owlbear\_generic | Unique identifier for the monster. |
|  | entity\_type | String | monster | Specifies the type of entity. |
|  | name | String | Owlbear | Human-readable name. |
|  | size | String | Large | Size category (e.g., Small, Medium, Large). |
|  | monster\_type | String | monstrosity | Creature type (e.g., beast, humanoid, undead). |
|  | alignment | String | Unaligned | Typical alignment. |
|  | ac | Number | 13 | Armor Class. |
|  | hp\_average | Number | 59 | Average hit points. |
|  | speed\_walk | String | 40 ft. | Walking speed. |
|  | ability\_scores | Object | {str: 20, dex: 12, con: 17,...} | Key ability scores. |
|  | challenge\_rating | String/Number | 3 | Difficulty indicator (system-specific). |

This table provides concrete examples that operationalize the concept of using YAML frontmatter for a single source of truth, directly supporting the creation of LLM-friendly structured data and illustrating how game-specific information can be nested within a broadly system-agnostic structure.

### **C. Enabling AIGM Capabilities through Structured YAML Data**

The meticulous structuring of TTRPG content into granular Markdown files with rich YAML frontmatter is not merely an organizational exercise; it is a foundational step towards enabling sophisticated AI Game Master (AIGM) functionalities.

* **Modifiable Adventures:** An AIGM can parse an adventure's Markdown files. If locations, NPCs, encounters, and items are defined with comprehensive YAML, the AIGM can:  
  * Reliably identify all distinct entities within an adventure by querying entity\_type and id fields.  
  * Create programmatic copies of these entities. For instance, it could duplicate an NPC's YAML block and then modify attributes like stats\_id (to use a different stat block), status, or current\_location\_id.  
  * Programmatically alter relationships between entities, such as updating an NPC's relationships list in their YAML or changing an item's owner.  
  * Adjust encounter difficulty by referencing monster YAML to understand their capabilities, then swapping monster id references in an encounter's creature list, or modifying monster counts or even individual monster attributes if the AIGM has write-access to the monster files.  
* **Dynamic Content Generation:** AIGMs can leverage the structured data within YAML frontmatter as a factual basis for generating new content. For example, by understanding an NPC's motivations, personality\_description, and relationships from their YAML, an AIGM could generate plausible dialogue for that NPC in a given situation. Similarly, location descriptions, plot hooks, or even new minor quests could be dynamically generated by combining information from various YAML-defined entities.  
* **Advanced Querying and Reasoning:** With entities and their properties stored in a machine-readable format like YAML, this data can be ingested into a vector database (like ChromaDB, discussed later) or a graph database. This allows an AIGM to perform complex queries and reasoning tasks, such as "Find all NPCs in the city of Waterdeep who are members of the Zhentarim faction and have a hostile attitude towards any character with the 'Harper' tag," assuming such data points are captured in the YAML. This capability moves beyond simple keyword search into more semantic understanding and relational inference.

## **VI. Optimizing Markdown for Retrieval Augmented Generation (RAG)**

The combination of well-structured Markdown for defining semantically coherent content blocks and rich YAML metadata for precise filtering creates a powerful synergy for TTRPG RAG systems. Markdown's structure helps define "what" a chunk of information is about at a content level (e.g., the description of a specific room, the mechanics of a particular spell). Simultaneously, YAML metadata adds layers of contextual information—"who, where, when, and why" (e.g., this room is in the 'Sunless Citadel' adventure for 'D\&D 5e', this spell is a '2nd-level evocation' available to 'Wizards'). This dual approach enables highly precise and contextually relevant information retrieval. For example, a query like "Pathfinder goblin tactics" can first be filtered by game\_system: pathfinder and entity\_type: monster and name: goblin (attributes derived from YAML metadata). Then, a semantic search can be performed specifically on the content of those pre-filtered chunks, which are themselves well-structured due to good Markdown practices. This multi-stage process of filtering and then searching is significantly more efficient and accurate than performing a semantic search alone across an entire, undifferentiated dataset of TTRPG content. Therefore, the careful interplay between Markdown's content structure and YAML's metadata structure is fundamental to optimizing RAG for the complexities of the TTRPG domain.

### **A. Context-Aware Chunking Strategies for TTRPG Content**

Effective chunking is paramount for RAG performance, as it determines the units of information retrieved to provide context to the LLM.10

* **Leveraging Markdown Structure:** As previously established, Markdown headings (H1-H6), paragraphs, lists, and blockquotes provide natural semantic boundaries. Chunking strategies should respect these boundaries to maintain contextual integrity.4 A logical chunk could be a single room description under an H4 heading, an NPC's background section under an H3, or a specific rule delineated by an H3 and its subsequent paragraphs.  
* **Recursive Splitting:** For longer sections of text that fall under a single heading (e.g., extensive lore for a region), a recursive splitting strategy can be effective. This involves first splitting by available subheadings, then by paragraphs within those subsections, and finally, if necessary, by sentences.60 This hierarchical approach attempts to preserve as much semantic structure as possible.  
* **Sentence Chunking:** While sentence-level chunking can be useful for very dense, information-rich text, it carries the risk of losing critical context if individual sentences are too short or are heavily reliant on preceding or subsequent sentences for their full meaning.13 This is often less ideal for narrative or descriptive TTRPG text.  
* **Semantic Chunking with LLMs:** More advanced techniques involve using LLMs themselves to identify semantic shifts or topic boundaries within the text, thereby determining optimal chunk breakpoints.12 Tools like LumberChunker exemplify this approach.12 This could be particularly beneficial for TTRPG lore, complex plot descriptions, or nuanced rule explanations where simple structural cues might be insufficient.  
* **Chunk Size Considerations:** The ideal chunk size is a balance between specificity and context. Chunks that are too small may lack sufficient context for the LLM to understand the information fully. Conversely, chunks that are too large might dilute the specific information relevant to a query or exceed the LLM's context window limitations.10 The optimal size is often use-case dependent and may require empirical tuning.  
* **Overlap:** Introducing a strategic overlap between consecutive chunks (e.g., the last sentence of chunk N becomes the first sentence of chunk N+1) can help maintain context, especially when using fixed-size or sentence-based chunking methods.11  
* **Botpress Example:** The Botpress platform illustrates a practical application of these principles by using a semantic approach to headings and subheadings for chunking. It relies on the document's inherent structure to accurately identify and group logical segments of content for vectorization and retrieval.4

### **B. The Role of YAML Frontmatter and In-Text Metadata in Enhancing RAG Retrieval**

YAML frontmatter and other forms of metadata are not just for organization; they are critical for enhancing the precision and relevance of RAG retrieval.

* **Metadata in Embeddings:** When creating vector embeddings for each text chunk, it is highly beneficial to include relevant metadata derived from the YAML frontmatter (e.g., entity\_type, name, game\_system, source\_book, tags) alongside the textual content of the chunk. Some systems store this metadata separately but link it to the vector.  
* **Filtering at Query Time:** This associated metadata allows for powerful filtering capabilities at the query stage, before or after the semantic search in the vector database. For example, a user query like "Show me D\&D 5e dragons" can be processed by first filtering the vector store for chunks that have metadata tags game\_system: dnd5e AND (monster\_type: dragon OR tags: \["dragon"\]). The semantic search for "dragons" is then performed only on this much smaller, highly relevant subset of chunks.10  
* **Contextualization for LLM:** When retrieved chunks are passed to the LLM to generate a response, their associated metadata should also be provided. This gives the LLM crucial context about the source, nature, and specific attributes of the information, leading to more accurate and nuanced answers.  
* **Voiceflow Example:** Voiceflow's best practices for its Knowledge Base highlight the importance of enhancing documents with metadata such as lastUpdated and pageLink. They also recommend using their tabular data format for structured information, which allows for defining searchable fields and tags that can be used for dynamic filtering based on user queries.20

### **C. Embedding Strategies for TTRPG-Specific Terminology and Concepts**

TTRPGs are rich with domain-specific vocabulary and unique game mechanics that may not be well-represented in general-purpose LLM training data or standard embedding models.

* **Domain-Specific Vocabulary:** Terms like "THAC0," "Saving Throw," "Feat," "Metamagic," "Krit," or system-specific monster names and abilities (e.g., "Mind Flayer's Tentacles," "Pathfinder's Attack of Opportunity") carry very precise meanings within their respective game systems. Standard embedding models might not capture the full semantic nuance of these terms.  
* **Fine-tuning Embedding Models:** For optimal performance in a TTRPG RAG system, fine-tuning an embedding model on a large corpus of TTRPG text (rulebooks, adventures, fan-created content for the specific systems of interest) could significantly improve the quality of embeddings for domain-specific queries. This would help the model learn better representations for TTRPG-specific terminology and concepts. (While this is an advanced topic beyond simple Markdown structuring, it is a key consideration for achieving high-performance RAG in this domain).  
* **Glossary/Definition Linking:** For very specific, obscure, or newly introduced game terms, ensuring that clear definitions are available in linked documents or are included as part of the text chunk itself can help the LLM understand them correctly. If a term is frequently misunderstood, explicitly providing its definition in proximity to its usage in the Markdown can be beneficial.

## **VII. Validating the Ingestion Pipeline: PDF → Formatted Markdown → ChromaDB**

The proposed pipeline involves several transformation stages, each with its own challenges and validation requirements. The quality of the "Formatted TTRPG Markdown" is pivotal; it represents the bridge where human-centric PDF content is transformed into machine-parseable, metadata-rich structures that directly fuel the effectiveness of the RAG system. If this intermediate stage is poorly executed—resulting in inaccurate text, lost structure, or incomplete metadata—the quality of data ingested into ChromaDB will be significantly degraded. This, in turn, will lead to suboptimal RAG performance, as the system will struggle to retrieve relevant and accurate information. Therefore, careful attention to each step of this pipeline is essential.

### **A. Stage 1: PDF to Raw Markdown Conversion**

Converting TTRPG PDFs into usable raw Markdown is often the most challenging step due to the inherent complexity of these documents. TTRPG PDFs frequently feature multi-column layouts, intricately formatted embedded stat blocks, visually distinct callout boxes and sidebars, complex tables for rules and random generation, and a wide array of decorative fonts and images.29 A primary difficulty is preserving the correct reading order, especially with multi-column text or text flowing around images, as the internal structure of a PDF does not always match the visual presentation.70

* Evaluating Tools:  
  Several Python libraries and tools can be employed for this conversion, each with varying strengths and weaknesses when applied to complex TTRPG documents.  
  * **PyMuPDF4LLM:**  
    * **Capabilities:** This library is designed to convert PDF content into Markdown format, optimized for LLM and RAG applications. It handles multi-column pages, extracts images and tables, and supports page-level chunking that outputs dictionaries rich in metadata. This metadata can include document information (file path, page count, current page number), Table of Contents items, bounding boxes for tables, images, and graphics, and optionally, word-level coordinates.17 It also attempts to detect and format headers, bold/italic text, code blocks, and lists.45 Users have reported it as being extremely fast with good table parsing capabilities.77  
    * **Key to\_markdown() Parameters for TTRPGs (derived from 42):** The page\_chunks=True parameter is particularly valuable, as it returns a list of dictionaries, one for each page, containing the Markdown text and associated metadata like "file\_path", "page\_number", "toc\_items", "tables" (with bounding boxes, row/column counts), and "images" (with bounding boxes and file paths if write\_images=True is enabled).41 The extract\_words=True option adds detailed word-level coordinate information, which can be useful for very fine-grained analysis or linking annotations back to precise text locations.41 Image handling parameters like write\_images, embed\_images, image\_path, image\_format, and dpi offer control over how visual elements are processed.41 The table\_strategy parameter (with options like "lines\_strict", "lines", "text") allows for tuning table detection.42 Parameters like ignore\_images or ignore\_graphics can be useful for speeding up processing or improving text detection in visually crowded TTRPG layouts by telling the parser to disregard these elements.42  
    * **Limitations for TTRPGs:** While generally robust for standard PDF structures, PyMuPDF4LLM may not perfectly identify highly stylized TTRPG-specific elements like complex monster stat blocks (which often have intricate internal formatting beyond simple tables) or visually distinct callout boxes as unique semantic units without custom post-processing logic. The library itself does not offer features explicitly designed for TTRPG stat blocks or callout boxes beyond its general text, table, and image extraction capabilities.41 Some users have also reported issues when using it in multithreaded environments.77  
  * **Marker (84):**  
    * **Capabilities:** Marker is an open-source tool that converts PDFs (and other formats like DOCX, PPTX, EPUB) to Markdown, JSON, and HTML. It aims for high accuracy by employing a pipeline of deep learning models for tasks including OCR (heuristics, Surya, Tesseract), layout detection (Surya), block formatting (heuristics, Texify for equations), and post-processing (heuristics, PDF Postprocessor).84 It can remove headers/footers, format tables and code blocks, extract and save images, and convert most equations to LaTeX. A significant feature is its optional "hybrid mode" (--use\_llm), which leverages an LLM (Gemini, Ollama, Claude, OpenAI) alongside its internal models to improve accuracy, particularly for complex tables and layouts.87 Benchmarks provided by its developers suggest it is faster and more accurate than tools like Nougat on certain datasets.84  
    * **TTRPG Relevance:** Marker's strong focus on accurate table formatting and sophisticated layout detection is highly promising for the complex structures found in TTRPG rulebooks and adventures. The removal of artifacts like page headers and footers is also beneficial. The ability to use an LLM for post-processing could potentially help in correctly interpreting and structuring TTRPG-specific elements, provided the LLM is prompted effectively, though this is not explicitly detailed for TTRPG content.  
    * **Limitations for TTRPGs:** The documentation notes some limitations: not all equations may be converted to LaTeX perfectly; tables might not always be formatted 100% correctly (e.g., text in the wrong column); and whitespace or indentations may not always be perfectly preserved.84 It performs best on digital PDFs that require minimal OCR.84 The primary documentation does not provide specific examples or case studies for TTRPGs, graphic novels, or magazines, though its handling of multi-column academic papers is demonstrated.87  
  * **Nougat (89):**  
    * **Capabilities:** Developed by Meta, Nougat (Neural Optical Understanding for Academic Documents) is a Visual Transformer model designed to transcribe scientific PDFs into Markdown (specifically, MultiMarkdown .mmd format). It excels at handling LaTeX math equations and tables commonly found in academic papers.91 It uses a VisionEncoderDecoder architecture similar to Donut.94  
    * **TTRPG Relevance:** Its proficiency with complex layouts in academic documents might translate reasonably well to some TTRPG rulebooks, especially those that are text-heavy with structured content and tables.  
    * **Limitations for TTRPGs:** Nougat is primarily optimized for academic documents. User experiences suggest it performs less effectively on other document types, such as invoices.93 It might struggle with the highly graphical and often non-standard layouts of many TTRPG books, particularly elements like artistic stat blocks or heavily stylized callout boxes that do not resemble typical academic tables or text blocks.95 Its output is .mmd, which may require further conversion or handling.  
  * **Other tools/libraries:**  
    * **ebullient/ttrpg-convert-cli** 25: This tool is not a direct PDF-to-Markdown converter. Instead, it converts structured JSON data from sources like 5eTools or Pf2eTools into Obsidian-friendly Markdown. It's highly relevant for users who have access to TTRPG content in these JSON formats, as it can produce well-structured Markdown with YAML frontmatter suitable for stat blocks, often compatible with Obsidian plugins like Fantasy Statblocks.25  
    * **pdfplumber** 48: A Python library good for extracting text, detailed character information (including fonts and positions), and tables from PDFs. It provides fine-grained control and can be combined with other libraries like tabulate to output extracted table data into Markdown format.48 Its strength lies in accessing the low-level details of PDF structure.  
    * **iamarunbrahma/pdf-to-markdown** 68: This Python project aims to convert PDFs to structured Markdown, preserving elements like tables, images, lists, and text formatting (bold, italic). It claims to handle multi-column layouts, perform OCR on images, and even generate image captions. The output is optimized for RAG. However, it notes limitations with very complex layouts or specialized symbols and does not provide specific TTRPG conversion examples.  
    * **LlamaParse** 74: A GenAI-native parsing platform from LlamaIndex, capable of handling various file types including PDF. It excels at parsing embedded tables accurately into text and semi-structured representations, extracting data from visual elements, and can be customized with natural language parsing instructions. It can output to Markdown or JSON. Its LLM-enabled nature makes it adaptive.  
    * **Traditional OCR tools (e.g., Tesseract):** While often used as a component in more advanced PDF parsing pipelines (e.g., by Marker or iamarunbrahma/pdf-to-markdown 69), standalone OCR tools generally struggle with complex layouts and understanding the semantic structure of documents without significant pre-processing and post-processing efforts.61

**Table 3: Comparative Analysis of PDF-to-Markdown Conversion Tools for TTRPG Content**

| Tool | Key Features for TTRPGs | Strengths for TTRPGs | Weaknesses/Limitations for TTRPGs | Handling of Tables | Handling of Multi-Column | Image Extraction | Stat Block Suitability (Heuristic) | Callout Box Suitability (Heuristic) |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| **PyMuPDF4LLM** | MD output, multi-column, image/table extraction, page chunks with metadata (coords, ToC), word extraction 42 | Fast, good general text/table parsing, rich metadata output per page chunk, word coordinates for detailed analysis. | May not semantically identify complex TTRPG stat blocks/callouts without custom logic; some threading issues reported.77 | Good (tunable strategies) 42 | Good 45 | Yes (external or embedded, controllable) 42 | Moderate (as tables/text) | Low (as general text/graphics) |
| **Marker** | MD/JSON/HTML output, OCR, layout detection, artifact removal, table/equation formatting, optional LLM enhancement 87 | High accuracy on complex layouts (esp. with \--use\_llm), good table/artifact handling, supports various input formats. | Best on digital PDFs, some table/whitespace imperfections, no specific TTRPG optimization.84 | Very Good (esp. with LLM) 87 | Good 87 | Yes (saved externally) 87 | Moderate to High (as tables/text with LLM assist) | Moderate (layout analysis might help) |
| **Nougat** | MD output (academic focus), LaTeX/table understanding, Vision Transformer model 91 | Strong on scientific docs with math/tables. | Optimized for academic papers, less effective on other types (e.g., invoices), may struggle with graphical TTRPG layouts.93 | Good (for academic style) 91 | Likely Good | Limited information, focus is text/math. | Low to Moderate (if table-like) | Low |
| **LlamaParse** | GenAI-native, various inputs (PDF, DOCX), MD/JSON output, table/image data, customizable instructions 74 | Adapts with LLM instructions, good table/visual element extraction, handles diverse document types. | Can be slower/costlier if using cloud API for LLM features; effectiveness depends on instruction quality. | Very Good (LLM-enhanced) 103 | Very Good 103 | Yes (with metadata) 103 | Moderate to High (with good instructions) | Moderate to High (with good instructions) |
| **pdfplumber** | Text/table extraction, character-level details (font, position) 100 | Fine-grained control, precise coordinate data, good for building custom parsers. | Primarily an extraction library, requires significant custom code for full MD conversion and semantic understanding. | Good (raw data extraction) 48 | Requires custom logic | Basic (image objects) | Low (raw text/data) | Low (raw text/data) |
| **iamarunbrahma/ pdf-to-markdown** | MD output, preserves formatting, multi-column, OCR, image captions, RAG-optimized 69 | Aims for RAG-ready MD, handles common structures. | Limitations on very complex layouts/symbols, requires manual review for non-standard formats.69 | Good 69 | Good 69 | Yes (with OCR/captions) 69 | Moderate (as text/tables) | Low (as general text) |

Table 4: Key pymupdf4llm.to\_markdown() Parameters for TTRPG Document Conversion  
(Based on 42\)

| Parameter | Description | Default Value | Effect on Output | Relevance for TTRPGs |
| :---- | :---- | :---- | :---- | :---- |
| doc | Input PDF file path or PyMuPDF Document object. | None | Specifies the source document. | Essential for any conversion. |
| pages | List/range of 0-based page numbers to process. None for all pages. | None | Limits conversion to specified pages. | Useful for extracting specific chapters or sections from large TTRPG books. |
| hdr\_info | Custom header detection logic or False to disable. | None | Controls how Markdown headers (\#, \#\#) are identified based on font sizes. | Important for preserving the structural hierarchy of TTRPG documents (chapters, sections, encounter titles). |
| write\_images | If True, extracts images to image\_path and links them in Markdown. | False | Creates external image files. | Crucial for maps, illustrations, and visual elements in TTRPG books. Linked images are better than embedded for searchability.68 |
| embed\_images | If True, embeds images as base64 strings in Markdown. Overrides write\_images. | False | Includes images directly in MD text, increasing file size. | Less ideal for TTRPGs due to large file sizes and potential impact on search performance in tools like Obsidian.68 |
| ignore\_images | If True, images are disregarded. (New in v0.0.20) | False | Excludes images from output. Can speed up processing or improve text detection on crowded pages. | Useful if only text is needed, or if images interfere with layout analysis of dense stat blocks or text boxes. |
| ignore\_graphics | If True, vector graphics are disregarded (except for table detection). (New in v0.0.20) | False | Excludes vector graphics. Similar benefits to ignore\_images. | Can help with complex page borders or decorative elements that might confuse text extraction. |
| image\_size\_limit | Float (0-1). Images smaller than this fraction of page dimensions are ignored. | 0.05 | Filters out very small images. | Can remove icons or minor graphical elements not relevant for content. |
| dpi | Resolution for extracted images (if write\_images=True). | 150 | Affects quality and file size of saved images. | Higher DPI for maps needing detail, lower for simple icons. |
| image\_path | Folder to save extracted images (if write\_images=True). | '' (script dir) | Specifies image storage location. | Essential to organize extracted visual assets. |
| image\_format | Desired format for saved images (e.g., "png", "jpg"). | 'png' | Determines image file type. | Choose based on balance of quality and file size (e.g., PNG for line art/maps, JPG for photos if present). |
| force\_text | If True, extracts text even if it overlaps images/graphics. | True | Ensures text on images is captured. | Important for text within callout boxes that might be rendered as images, or text labels on maps. |
| margins | Page margins (float or list) to restrict content extraction area. | 0 | Excludes content outside specified margins. | Can help ignore page numbers, headers/footers if they are consistently placed. |
| page\_chunks | If True, returns a list of dictionaries (one per page) with MD text and metadata. | False | Structures output per page with rich metadata. | **Highly Recommended.** Provides page number, ToC info, table/image coordinates associated with the Markdown text of that page, crucial for RAG context and data validation. |
| table\_strategy | Table detection strategy (e.g., "lines\_strict", "lines", "text"). None to disable. | 'lines\_strict' | Influences how tables are identified and converted. | Critical for stat blocks or other tabular data. Experimentation may be needed for best results on varied TTRPG table formats. |
| extract\_words | If True, adds detailed word-level data (coordinates, text) to page chunks. Forces page\_chunks=True. | False | Provides granular word information. | Extremely useful for fine-grained analysis, identifying specific text blocks (like callouts) by coordinates, or creating precise annotations/links. |

* Techniques for Preserving Reading Order and Identifying Semantic Regions:  
  The accurate preservation of reading order and the identification of distinct semantic regions (like callout boxes or sidebars) are significant hurdles in PDF-to-Markdown conversion for TTRPG documents. Standard PDF-to-Markdown tools are unlikely to capture these perfectly without tailored post-processing, as they primarily focus on text flow, headings, lists, and standard tables, and may not recognize TTRPG-specific visual or semantic structures as distinct entities.45  
  * **Multi-Column Layouts:** Advanced PDF parsers like PyMuPDF4LLM 45, Marker 87, and iamarunbrahma/pdf-to-markdown 69 claim to handle multi-column text effectively. PyMuPDF, for instance, offers specific utilities for multi-column text detection and extraction, often based on analyzing the bounding boxes of text blocks to infer flow.70 LlamaParse also reports capabilities in handling complex layouts including multi-column text.74  
  * **Callout Boxes/Sidebars/Read-Aloud Text:** These elements are particularly challenging as their identification often relies on visual cues or subtle formatting not always captured by standard text extraction logic.  
    * **Heuristic 1 (Visual Cues & Coordinate-Based Extraction):** If callout boxes or sidebars have distinct visual characteristics such as borders, specific background colors, or are consistently placed in particular page regions (e.g., a shaded box in the margin), a multi-step approach can be employed. First, the PDF page can be rendered as an image using a library like PyMuPDF. Then, image processing techniques with libraries such as OpenCV can be used to identify these visually distinct regions by detecting contours, color profiles, or geometric shapes.108 Once the bounding box coordinates of such a region are determined from the image analysis, PyMuPDF can be used to extract text specifically from those coordinates on the original PDF page (page.get\_text(clip=fitz.Rect(x0,y0,x1,y1))).76 This extracted text can then be wrapped in appropriate Markdown (e.g., a blockquote for read-aloud text, or a custom div/fenced block if the target Markdown flavor supports it).  
    * **Heuristic 2 (Stylistic Cues from Rich Text Extraction):** If callout text consistently uses a distinct font style, size, or weight, and if the PDF parser can provide this detailed text information (e.g., PyMuPDF's get\_text("dict") or extractDICT() methods 42, or pymupdf4llm with extract\_words=True), this information could be used to heuristically identify and segment these blocks.  
    * **Heuristic 3 (Keyword/Pattern Matching):** Many TTRPG documents use explicit textual cues for certain boxes. For example, read-aloud text might be preceded by a bolded label like "**Read Aloud:**" or "**Boxed Text:**".5 Sidebars might start with "DM Note:" or "Designer's Insight:". Regular expressions applied to the extracted Markdown can identify these patterns and allow for re-wrapping the subsequent text block with appropriate Markdown (e.g., \> for blockquotes).  
    * **Limitations:** These heuristic approaches often require custom scripting and are not typically out-of-the-box features for most general-purpose PDF-to-Markdown conversion tools. Their reliability depends on the consistency of formatting in the source PDFs.  
  * **Reading Order:** The inherent nature of the PDF format, where text elements can be placed in any order internally yet appear visually correct, makes preserving logical reading order non-trivial.71 Advanced parsers like PyMuPDF4LLM, Marker, and LlamaParse generally attempt to reconstruct the correct reading order by analyzing the geometric layout of text blocks on the page.45 However, for extremely complex layouts with interweaving text flows or text wrapped around irregular shapes, errors can still occur.  
* **Quality Assessment:**  
  * **Metrics:** If a ground truth (manually created, accurate Markdown version) of the TTRPG document exists, quantitative metrics can be used to evaluate the PDF-to-Markdown conversion. These include word-level precision, recall, and F-measure; sequence similarity scores like BLEU; and edit distance metrics like Average Normalized Levenshtein Similarity (ANLS).107 TextIn's markdown\_tester tool, for example, is designed to compare Markdown documents based on paragraph similarity, heading structure, table content, formula representation, and reading order.67  
  * **Manual Review:** For TTRPG documents, with their unique formatting conventions and critical game information, manual review is essential, even with the best automated tools. Spot-checking key sections—such as monster stat blocks, complex rule tables, read-aloud text passages, and chapter beginnings/endings—for accuracy, completeness, and correct formatting is crucial.  
  * **Specific Challenges in TTRPG PDFs:** PDF conversion tools, in general, often struggle with accurately converting complex tables (especially those with merged cells or unusual layouts), mathematical equations (less common in TTRPGs but can appear in some systems), and maintaining the precise formatting or semantic structure of non-standard elements like uniquely styled callout boxes or highly graphical stat blocks.29

### **B. Stage 2: Raw Markdown to Formatted TTRPG Markdown**

Once raw Markdown has been extracted from the PDF, it typically requires further processing to conform to the desired TTRPG-specific structure and formatting guidelines outlined in Sections II, III, and IV. This stage bridges the gap between generic extraction and a clean, semantically rich, and LLM-ready TTRPG document.

* **Automated Post-Processing:**  
  * **Regular Expressions (Regex):** Regex is a powerful tool for cleaning common OCR errors (e.g., misrecognized characters, inconsistent spacing), standardizing formatting for simple and repetitive elements within stat blocks (e.g., transforming \*\*Armor Class\*\* (\\d+) to AC: $1), reformatting lists that may have been imperfectly extracted, or identifying textual patterns that indicate specific TTRPG elements (like ability score blocks or action headers).105  
  * **Scripting (e.g., Python):** For more complex structural transformations, applying game-system-specific templates, or converting extracted data structures (such as the list of dictionaries from pymupdf4llm when page\_chunks=True) into the desired YAML frontmatter and Markdown body structure, custom scripts are often necessary. These scripts can parse the raw Markdown (or the structured output from the PDF converter), identify semantic sections based on heuristics or preliminary tags, populate YAML frontmatter fields, and reformat the Markdown body content.  
  * **Inspiration from Tools like ttrpg-convert-cli:** While ebullient/ttrpg-convert-cli primarily works from structured JSON data (like 5eTools exports) rather than raw PDF output 25, its templating engine and its approach to generating Obsidian-friendly Markdown (often with detailed YAML frontmatter for stat blocks compatible with plugins like Fantasy Statblocks 25) can serve as a valuable model for developing custom post-processing scripts.  
* **Best Practices for Manual Cleanup:** Even with sophisticated automated post-processing, some level of manual cleanup and review is almost always necessary for TTRPG documents due to their complexity and the importance of accuracy.  
  * Focus on semantic correctness: Ensure that headings are appropriate for the content they introduce, that read-aloud text is correctly identified and formatted (e.g., as blockquotes), and that game-specific terminology is consistent.  
  * Verify complex tables and stat blocks: These are often critical for gameplay and prone to errors during automated conversion. Check numerical values, ability descriptions, and overall formatting.  
  * Ensure links are correct: If automated linking was attempted, verify that internal references point to the correct targets and that external links are valid.  
  * Standardize formatting: Apply the established Markdown style guidelines (from Sections II & III) consistently throughout the document.  
  * General PDF cleaning best practices, such as reviewing document metadata or sanitizing headers/footers in the source PDF *before* conversion, can sometimes mitigate issues downstream, though this is more about source preparation than Markdown cleanup itself.119  
* **Strategies for Extracting and Structuring Stat Blocks (Post-Raw Extraction):** If stat blocks are not perfectly structured by the initial PDF-to-Markdown conversion, further steps are needed:  
  * **If Parsed as Tables:** Some PDF parsers might render stat blocks as tables if their layout is sufficiently grid-like. This tabular data might then need to be transformed by a script into the desired Markdown stat block style (e.g., the D\&D 5e style shown in 5 or the Pathfinder 2e style using specific plugin syntax 8).  
  * **Regex on Extracted Text:** If stat blocks are extracted as relatively consistent blocks of plain text, regular expressions can be used to capture key-value pairs (e.g., Hit Points: 100, Armor Class: 15, STR: 18, etc.).115 However, the variability in stat block formatting across different TTRPG books and even within the same book can make robust regex-based parsing very challenging.  
  * **Heuristic-Based Parsers:** Custom Python scripts can be developed to act as heuristic-based parsers. These scripts would look for common stat block keywords (e.g., "Armor Class," "Hit Points," "Speed," ability score abbreviations like STR, DEX, CON, sections like "Actions," "Reactions," "Legendary Actions") and textual patterns to segment the stat block and extract its constituent data into a structured format (like a Python dictionary, which can then be written to YAML). Programmatic extraction of stat blocks has been explored in various contexts.121  
  * **LLM for Extraction/Structuring:** A powerful approach is to use an LLM itself for structuring. A raw text stat block, extracted from the PDF, can be fed to an LLM with a prompt instructing it to convert the text into a specific structured format (e.g., JSON or YAML conforming to a predefined schema). This structured output can then be directly inserted into the YAML frontmatter of the Markdown file.  
  * **Visual Template Matching (Advanced, for Scanned PDFs):** For scanned PDFs where text extraction is reliant on OCR, and if stat blocks have a highly consistent visual layout, advanced techniques involving template matching on the stat block's image could be used to identify their regions and guide OCR. This is a complex computer vision task.

### **C. Stage 3: Formatted TTRPG Markdown to ChromaDB**

The final stage in the proposed pipeline is ingesting the formatted, metadata-rich TTRPG Markdown into a vector database like ChromaDB to enable semantic search and RAG capabilities.

* **Effective Chunking of Structured TTRPG Markdown:**  
  * The primary chunking strategy should leverage the semantic structure established in the formatted Markdown. Markdown headings (H1-H6) and distinct semantic sections (e.g., a single room description, an NPC's abilities section, a specific rule explanation) should serve as the primary boundaries for creating chunks.4  
  * For Markdown files that represent single, discrete entities (such as an NPC, a Monster, or a Spell) and contain detailed YAML frontmatter, the entire Markdown body (which typically contains descriptive prose, lore, or narrative elements) can often be treated as a single chunk. The rich YAML frontmatter then provides the queryable metadata for this chunk. Alternatively, if the descriptive body text is very long, it can be further chunked by its own internal subheadings or paragraphs.  
  * It is crucial to ensure that chunks are large enough to contain meaningful, self-contained context but small enough to be efficiently retrieved by the vector database and to fit within the context windows of the LLMs used in the RAG system.10  
* **Embedding Generation and Metadata Association:**  
  * For each text chunk identified, a vector embedding must be generated using a chosen embedding model (e.g., models from the Sentence Transformers library, OpenAI's Ada models, or other state-of-the-art embedding solutions).  
  * The original text content of the chunk and its corresponding embedding vector are then stored in ChromaDB.  
  * Critically, the rich metadata derived from the YAML frontmatter of the source Markdown file (and any inferred metadata, such as the file\_path, section\_header from which the chunk originated, or the page\_number if page\_chunks=True was used in PDF conversion) must be associated with each chunk in ChromaDB's metadata store.10 This metadata is vital for enabling filtered queries and providing context to the LLM.  
* **Indexing and Querying Considerations for ChromaDB:**  
  * ChromaDB automatically handles the indexing of the embedding vectors, allowing for efficient similarity searches.  
  * The metadata filtering capabilities of ChromaDB should be leveraged extensively. Effective RAG queries will typically combine semantic search (finding chunks with embeddings similar to the query embedding) with precise metadata filtering. For example, a query for "dragon's breath weapon" could be refined to search only within chunks where game\_system='dnd5e' AND entity\_type='monster' AND tags CONTAINS 'dragon'.  
  * Depending on the scale and diversity of the TTRPG content, consideration might be given to creating separate collections within ChromaDB for different game systems, major sourcebooks, or broad content types (e.g., "rules," "monsters," "adventures"). This can simplify management, optimize query performance for specific use cases, and allow for different indexing or embedding strategies per collection if needed.

## **VIII. Conclusion and Future Directions**

### **A. Recap of Best Practices for a Robust TTRPG Markdown Ecosystem**

This report has outlined a comprehensive framework for organizing TTRPG reference documents using Markdown, designed to serve both human users and LLM-powered RAG applications. The core tenets of this framework revolve around a synergistic approach:

1. **System-Agnostic Core Structure:** Employing a consistent, hierarchical Markdown structure (semantic headings, lists, emphasis) provides a universal foundation for all TTRPG content, ensuring clarity and basic machine readability.  
2. **Game-Specific Modularity:** Integrating system-specific elements (like stat blocks and unique mechanics) through dedicated sections within the agnostic framework, or more powerfully, via detailed YAML frontmatter schemas tailored to each game system. The game\_system metadata tag is key to enabling this modularity.  
3. **Granular File Organization:** Breaking content down into individual Markdown files for distinct entities (NPCs, locations, items, monsters, spells) promotes a single source of truth, enhances reusability, simplifies management, and is crucial for AIGM functionalities.  
4. **Rich YAML Frontmatter:** Utilizing YAML frontmatter as the canonical store for all structured data associated with an entity. This metadata is paramount for precise filtering in RAG systems and for enabling AIGMs to understand and manipulate game components.  
5. **RAG-Optimized Chunking:** Leveraging the semantic structure of the Markdown and the associated YAML metadata to create meaningful, context-rich chunks for ingestion into vector databases.  
6. **Consistent Linking:** Building a dense network of internal links between related entities and concepts transforms the document collection into a navigable knowledge graph, benefiting both human exploration and LLM contextual understanding.

Adherence to consistency in formatting, naming conventions, and metadata application across the entire TTRPG Markdown ecosystem is paramount for realizing the full benefits of this approach.

### **B. Potential Advancements in AI-Driven TTRPG Content Creation and Management**

The structured Markdown and YAML framework proposed here not only addresses current needs but also paves the way for exciting future developments in AI-driven TTRPG content creation and management:

* **LLM-Assisted PDF Conversion:** As LLMs become more adept at understanding complex document layouts and even visual information, they could play a more significant role in the initial, challenging step of converting legacy TTRPG PDFs (especially scanned ones or those with highly irregular layouts) into well-structured Markdown and YAML. This could involve LLMs identifying semantic regions (like stat blocks or callout boxes) and directly outputting them in the desired structured format.  
* **AIGM Content Generation and Adaptation:** With a rich, structured knowledge base of TTRPG content in Markdown and YAML, AIGMs could move beyond simple information retrieval. They could leverage this data to:  
  * Generate new, plausible content (e.g., new NPCs based on existing templates, new encounters for a specific location, new adventure hooks).  
  * Dynamically adapt existing adventures (e.g., scaling encounter difficulty based on party composition by modifying monster YAML, or re-populating a dungeon with different creatures suitable for the environment defined in location YAML).  
  * Even assist in or fully run game sessions by interpreting player actions, querying the knowledge base for relevant rules and lore, and generating narrative responses or NPC dialogue.  
* **Community-Driven Standardization:** The TTRPG community could collaboratively develop and adopt standardized Markdown dialects or extensions specifically for game content (building on efforts like rpg-markdown 51). This would foster interoperability between different tools, platforms, and user-created content. Standardized YAML schemas for various game systems would also be immensely beneficial.  
* **Multimodal RAG Systems:** Future RAG systems may more effectively incorporate multimodal information. Instead of relying solely on text extracted from PDFs, they might directly process and understand visual elements like maps (identifying rooms, paths, and icons), complex diagrams (like flowcharts for adventure progression), or even illustrations of monsters and characters. This would reduce the dependency on perfect text and Markdown conversion for these visual elements, allowing the AI to draw insights from the original PDF layout more directly.

The journey towards a fully AI-integrated TTRPG ecosystem is still in its early stages, but a foundation of well-structured, machine-readable, and human-friendly Markdown documentation is a critical enabler for the innovations to come. This framework provides a robust starting point for GMs, players, designers, and developers looking to harness the power of both Markdown and AI in the world of tabletop role-playing games.

#### **Works cited**

1. LLMs Love Structure: Using Markdown for Better PDF Analysis \- APPGAMBiT, accessed May 6, 2025, [https://www.appgambit.com/blog/llms-love-structure-using-markdown-for-pdf-analysis](https://www.appgambit.com/blog/llms-love-structure-using-markdown-for-pdf-analysis)  
2. MarkItDown utility and LLMs are great match \- Kalle Marjokorpi, accessed May 6, 2025, [https://www.kallemarjokorpi.fi/blog/markitdown-utility-and-llms-are-great-match/](https://www.kallemarjokorpi.fi/blog/markitdown-utility-and-llms-are-great-match/)  
3. Boosting AI Performance: The Power of LLM-Friendly Content in ..., accessed May 7, 2025, [https://developer.webex.com/blog/boosting-ai-performance-the-power-of-llm-friendly-content-in-markdown](https://developer.webex.com/blog/boosting-ai-performance-the-power-of-llm-friendly-content-in-markdown)  
4. How to Optimize Files for RAG | Structuring Data for RAG | Botpress ..., accessed May 7, 2025, [https://botpress.com/academy-lesson/rag-data-structuring](https://botpress.com/academy-lesson/rag-data-structuring)  
5. Form and Structure: The DNA of Adventure Modules – Loot The Room, accessed May 6, 2025, [https://loottheroom.uk/form-and-structure-the-dna-of-adventure-modules](https://loottheroom.uk/form-and-structure-the-dna-of-adventure-modules)  
6. Is There A Way to make complex Markdown Table? \- Stack Overflow, accessed May 6, 2025, [https://stackoverflow.com/questions/60751150/is-there-a-way-to-make-complex-markdown-table](https://stackoverflow.com/questions/60751150/is-there-a-way-to-make-complex-markdown-table)  
7. Markdown style guide | styleguide \- Google, accessed May 6, 2025, [https://google.github.io/styleguide/docguide/style.html](https://google.github.io/styleguide/docguide/style.html)  
8. pixley/pf2e-statblock-for-obsidian: Allows Obsidian to ... \- GitHub, accessed May 7, 2025, [https://github.com/pixley/pf2e-statblock-for-obsidian](https://github.com/pixley/pf2e-statblock-for-obsidian)  
9. How do you layout your ttrpg book? : r/RPGdesign \- Reddit, accessed May 7, 2025, [https://www.reddit.com/r/RPGdesign/comments/1hc1tke/how\_do\_you\_layout\_your\_ttrpg\_book/](https://www.reddit.com/r/RPGdesign/comments/1hc1tke/how_do_you_layout_your_ttrpg_book/)  
10. Breaking up is hard to do: Chunking in RAG applications \- Stack Overflow, accessed May 6, 2025, [https://stackoverflow.blog/2024/12/27/breaking-up-is-hard-to-do-chunking-in-rag-applications/](https://stackoverflow.blog/2024/12/27/breaking-up-is-hard-to-do-chunking-in-rag-applications/)  
11. Practical Guide to LLM Chunking: Context, RAG, Vectors \- Mindee, accessed May 6, 2025, [https://www.mindee.com/fr/blog/llm-chunking-strategies?utm\_medium=cpc\&utm\_source=bing\&utm\_campaign=ACQ\_OCR\_Generic\&utm\_content=OCR\_Generic\_Broad\&msclkid=1829371eeed8150a915a51f24e95eda4](https://www.mindee.com/fr/blog/llm-chunking-strategies?utm_medium=cpc&utm_source=bing&utm_campaign=ACQ_OCR_Generic&utm_content=OCR_Generic_Broad&msclkid=1829371eeed8150a915a51f24e95eda4)  
12. MoC: Mixtures of Text Chunking Learners for Retrieval-Augmented Generation System, accessed May 6, 2025, [https://arxiv.org/html/2503.09600v1](https://arxiv.org/html/2503.09600v1)  
13. Enhancing RAG performance with smart chunking strategies \- IBM Developer, accessed May 6, 2025, [https://developer.ibm.com/articles/awb-enhancing-rag-performance-chunking-strategies/](https://developer.ibm.com/articles/awb-enhancing-rag-performance-chunking-strategies/)  
14. Maximizing LLM Performance with Effective Chunking Strategies for ..., accessed May 6, 2025, [https://vectorshift.ai/blog/maximizing-llm-performance-with-effective-chunking-strategies-for-vector-embeddings](https://vectorshift.ai/blog/maximizing-llm-performance-with-effective-chunking-strategies-for-vector-embeddings)  
15. 7 Chunking Strategies in RAG You Need To Know \- F22 Labs, accessed May 6, 2025, [https://www.f22labs.com/blogs/7-chunking-strategies-in-rag-you-need-to-know/](https://www.f22labs.com/blogs/7-chunking-strategies-in-rag-you-need-to-know/)  
16. Optimizing RAG Context: Chunking and Summarization for Technical Docs, accessed May 6, 2025, [https://dev.to/oleh-halytskyi/optimizing-rag-context-chunking-and-summarization-for-technical-docs-3pel](https://dev.to/oleh-halytskyi/optimizing-rag-context-chunking-and-summarization-for-technical-docs-3pel)  
17. PyMuPDF, LLM & RAG \- Read the Docs, accessed May 6, 2025, [https://pymupdf.readthedocs.io/en/latest/rag.html](https://pymupdf.readthedocs.io/en/latest/rag.html)  
18. Vault Structure \- Obsidian TTRPG Tutorials, accessed May 7, 2025, [https://obsidianttrpgtutorials.com/Obsidian+TTRPG+Tutorials/Getting+Started/Vault+Structure](https://obsidianttrpgtutorials.com/Obsidian+TTRPG+Tutorials/Getting+Started/Vault+Structure)  
19. Categorizing Markdown Files for a Scalable Knowledge Base \- DEV ..., accessed May 7, 2025, [https://dev.to/hexshift/categorizing-markdown-files-for-a-scalable-knowledge-base-2g4m](https://dev.to/hexshift/categorizing-markdown-files-for-a-scalable-knowledge-base-2g4m)  
20. Knowledge Base \- the docs \- Voiceflow, accessed May 7, 2025, [https://docs.voiceflow.com/docs/knowledge-base-best-practices](https://docs.voiceflow.com/docs/knowledge-base-best-practices)  
21. dnd-5e-srd/markdown/11 monsters.md at master \- GitHub, accessed May 7, 2025, [https://github.com/BTMorton/dnd-5e-srd/blob/master/markdown/11%20monsters.md](https://github.com/BTMorton/dnd-5e-srd/blob/master/markdown/11%20monsters.md)  
22. Basic formatting syntax \- Obsidian Help, accessed May 6, 2025, [https://help.obsidian.md/syntax](https://help.obsidian.md/syntax)  
23. Creating Links in Markdown \- AnVIL Portal, accessed May 7, 2025, [https://anvilproject.org/guides/content/creating-links](https://anvilproject.org/guides/content/creating-links)  
24. Markdown links | Docusaurus, accessed May 7, 2025, [https://docusaurus.io/docs/next/markdown-features/links](https://docusaurus.io/docs/next/markdown-features/links)  
25. ebullient/ttrpg-convert-cli: Utility to convert JSON data (for ... \- GitHub, accessed May 6, 2025, [https://github.com/ebullient/ttrpg-convert-cli](https://github.com/ebullient/ttrpg-convert-cli)  
26. TTRPG-Convert-CLI 5e \- Obsidian TTRPG Tutorials, accessed May 7, 2025, [https://obsidianttrpgtutorials.com/Obsidian+TTRPG+Tutorials/Plugin+Tutorials/TTRPG-Convert-CLI/TTRPG-Convert-CLI+5e](https://obsidianttrpgtutorials.com/Obsidian+TTRPG+Tutorials/Plugin+Tutorials/TTRPG-Convert-CLI/TTRPG-Convert-CLI+5e)  
27. DUNGEONS & DRAGONS (D\&D) or any TTRPG question \- Page 2 \- Obsidian Forum, accessed May 7, 2025, [https://forum.obsidian.md/t/dungeons-dragons-d-d-or-any-ttrpg-question/15625?page=2](https://forum.obsidian.md/t/dungeons-dragons-d-d-or-any-ttrpg-question/15625?page=2)  
28. The Bestiary \-, accessed May 7, 2025, [https://plugins.javalent.com/statblocks/readme/bestiary](https://plugins.javalent.com/statblocks/readme/bestiary)  
29. Fantasy Statblocks Plugin : r/ObsidianMD \- Reddit, accessed May 7, 2025, [https://www.reddit.com/r/ObsidianMD/comments/11n9g6o/fantasy\_statblocks\_plugin/](https://www.reddit.com/r/ObsidianMD/comments/11n9g6o/fantasy_statblocks_plugin/)  
30. Fantasy Statblocks \-, accessed May 6, 2025, [https://plugins.javalent.com/statblocks](https://plugins.javalent.com/statblocks)  
31. Stat Block Editor » Dungeons & Dragons \- D\&D 5 \- AideDD, accessed May 6, 2025, [https://www.aidedd.org/dnd-statblock/index-5e.php](https://www.aidedd.org/dnd-statblock/index-5e.php)  
32. Goblin » Monster Stat Block \- DnD 5e \- AideDD, accessed May 6, 2025, [https://www.aidedd.org/dnd/monstres.php?vo=goblin](https://www.aidedd.org/dnd/monstres.php?vo=goblin)  
33. Homebrewery (Markdown) SRD Monsters \[Resource\] : r/UnearthedArcana \- Reddit, accessed May 6, 2025, [https://www.reddit.com/r/UnearthedArcana/comments/5pfbfe/homebrewery\_markdown\_srd\_monsters\_resource/](https://www.reddit.com/r/UnearthedArcana/comments/5pfbfe/homebrewery_markdown_srd_monsters_resource/)  
34. fantasy-statblocks/CHANGELOG.md at main \- GitHub, accessed May 6, 2025, [https://github.com/javalent/fantasy-statblocks/blob/master/CHANGELOG.md](https://github.com/javalent/fantasy-statblocks/blob/master/CHANGELOG.md)  
35. Markdown monsters \- mdneuzerling, accessed May 6, 2025, [https://mdneuzerling.com/post/markdown-monsters/](https://mdneuzerling.com/post/markdown-monsters/)  
36. BTMorton/dnd-5e-srd: The Dungeons and Dragons 5th Edition SRD converted to markdown, json and yaml \- GitHub, accessed May 6, 2025, [https://github.com/BTMorton/dnd-5e-srd](https://github.com/BTMorton/dnd-5e-srd)  
37. accessed December 31, 1969, [https://raw.githubusercontent.com/BTMorton/dnd-5e-srd/master/markdown/11%20monsters.md](https://raw.githubusercontent.com/BTMorton/dnd-5e-srd/master/markdown/11%20monsters.md)  
38. Need help with ttrpgrelated dataview query : r/ObsidianMD \- Reddit, accessed May 7, 2025, [https://www.reddit.com/r/ObsidianMD/comments/1j6p5tz/need\_help\_with\_ttrpgrelated\_dataview\_query/](https://www.reddit.com/r/ObsidianMD/comments/1j6p5tz/need_help_with_ttrpgrelated_dataview_query/)  
39. PF2e Statblocks \- Allows Obsidian to render Pathfinder 2e statblocks cleanly, using only Markdown-based syntax. \- Obsidian Stats, accessed May 7, 2025, [https://www.obsidianstats.com/plugins/pf2e-statblocks](https://www.obsidianstats.com/plugins/pf2e-statblocks)  
40. How to Make a Table in Markdown \- Tiiny.host, accessed May 6, 2025, [https://tiiny.host/blog/how-to-make-a-table-in-markdown/](https://tiiny.host/blog/how-to-make-a-table-in-markdown/)  
41. Building a Multimodal LLM Application with PyMuPDF4LLM | Artifex, accessed May 7, 2025, [https://artifex.com/blog/building-a-multimodal-llm-application-with-pymupdf4llm](https://artifex.com/blog/building-a-multimodal-llm-application-with-pymupdf4llm)  
42. API \- PyMuPDF 1.25.5 documentation \- Read the Docs, accessed May 6, 2025, [https://pymupdf.readthedocs.io/en/latest/pymupdf4llm/api.html](https://pymupdf.readthedocs.io/en/latest/pymupdf4llm/api.html)  
43. RAG/README.md at main · pymupdf/RAG · GitHub, accessed May 6, 2025, [https://github.com/pymupdf/RAG/blob/main/README.md](https://github.com/pymupdf/RAG/blob/main/README.md)  
44. RAG/LLM and PDF: Conversion to Markdown Text with PyMuPDF ..., accessed May 6, 2025, [https://artifex.com/blog/rag-llm-and-pdf-conversion-to-markdown-text-with-pymupdf](https://artifex.com/blog/rag-llm-and-pdf-conversion-to-markdown-text-with-pymupdf)  
45. PyMuPDF4LLM \- PyMuPDF 1.25.5 documentation, accessed May 7, 2025, [https://pymupdf.readthedocs.io/en/latest/pymupdf4llm](https://pymupdf.readthedocs.io/en/latest/pymupdf4llm)  
46. pymupdf4llm \- PyPI, accessed May 7, 2025, [https://pypi.org/project/pymupdf4llm/](https://pypi.org/project/pymupdf4llm/)  
47. How do I convert a PDF to Markdown with Table Detection? \- Hyland Document Filters v25.1 Docs \- GitHub Pages, accessed May 7, 2025, [https://hyland.github.io/DocumentFilters-Docs/latest/tutorials/how\_do\_i\_convert\_a\_document\_to\_a\_markdown\_file\_with\_table\_detection.html](https://hyland.github.io/DocumentFilters-Docs/latest/tutorials/how_do_i_convert_a_document_to_a_markdown_file_with_table_detection.html)  
48. PDF Extraction: Retrieving Text and Tables together using Python \- DEV Community, accessed May 7, 2025, [https://dev.to/rishabdugar/pdf-extraction-retrieving-text-and-tables-together-using-python-14c2](https://dev.to/rishabdugar/pdf-extraction-retrieving-text-and-tables-together-using-python-14c2)  
49. Do LLMs understand markdown tables? \- YouTube, accessed May 6, 2025, [https://www.youtube.com/watch?v=KGz-w6DrBIk](https://www.youtube.com/watch?v=KGz-w6DrBIk)  
50. The Best Way To Manage Your TTRPG Campaigns\! \- YouTube, accessed May 7, 2025, [https://www.youtube.com/watch?v=DFBG3N68LKQ](https://www.youtube.com/watch?v=DFBG3N68LKQ)  
51. fictzio/rpg-markdown: Markdown dialect for game content ... \- GitHub, accessed May 7, 2025, [https://github.com/fictzio/rpg-markdown](https://github.com/fictzio/rpg-markdown)  
52. How to correctly use the YAML Front Matter : r/ObsidianMD \- Reddit, accessed May 7, 2025, [https://www.reddit.com/r/ObsidianMD/comments/znctl7/how\_to\_correctly\_use\_the\_yaml\_front\_matter/](https://www.reddit.com/r/ObsidianMD/comments/znctl7/how_to_correctly_use_the_yaml_front_matter/)  
53. markdig/src/Markdig.Tests/Specs/YamlSpecs.md at master · xoofx/markdig · GitHub, accessed May 7, 2025, [https://github.com/xoofx/markdig/blob/master/src/Markdig.Tests/Specs/YamlSpecs.md](https://github.com/xoofx/markdig/blob/master/src/Markdig.Tests/Specs/YamlSpecs.md)  
54. Front Matter (YAML) and Tags \- Obsidian TTRPG Tutorials, accessed May 7, 2025, [https://obsidianttrpgtutorials.com/Obsidian+TTRPG+Tutorials/Tutorials/YouTube+Series/Front+Matter+(YAML)+and+Tags/Front+Matter+(YAML)+and+Tags](https://obsidianttrpgtutorials.com/Obsidian+TTRPG+Tutorials/Tutorials/YouTube+Series/Front+Matter+\(YAML\)+and+Tags/Front+Matter+\(YAML\)+and+Tags)  
55. YAML Frontmatter \- Zettlr Docs, accessed May 7, 2025, [https://docs.zettlr.com/en/core/yaml-frontmatter/](https://docs.zettlr.com/en/core/yaml-frontmatter/)  
56. YAML Frontmatter \- Zettlr User Manual, accessed May 7, 2025, [https://docs.zettlr.com/en/advanced/yaml-frontmatter/](https://docs.zettlr.com/en/advanced/yaml-frontmatter/)  
57. YAML Frontmatter \- Markdown Confluence Tools, accessed May 7, 2025, [https://markdown-confluence.com/features/yaml-frontmatter.html](https://markdown-confluence.com/features/yaml-frontmatter.html)  
58. Your favorite source of Magic Items for Narrative RPGs : r/rpg \- Reddit, accessed May 7, 2025, [https://www.reddit.com/r/rpg/comments/wfso3v/your\_favorite\_source\_of\_magic\_items\_for\_narrative/](https://www.reddit.com/r/rpg/comments/wfso3v/your_favorite_source_of_magic_items_for_narrative/)  
59. D\&D 5e Statblock Generator \- Tetra Cube, accessed May 7, 2025, [https://tetra-cube.com/dnd/dnd-statblock.html](https://tetra-cube.com/dnd/dnd-statblock.html)  
60. \[Experiment\] Good chunking will lead you to the better RAG performance \- Reddit, accessed May 6, 2025, [https://www.reddit.com/r/LangChain/comments/1e3q2lb/experiment\_good\_chunking\_will\_lead\_you\_to\_the/](https://www.reddit.com/r/LangChain/comments/1e3q2lb/experiment_good_chunking_will_lead_you_to_the/)  
61. PDF to Markdown Conversion: Open Source Tools for LLMs \- Toolify.ai, accessed May 6, 2025, [https://www.toolify.ai/ai-news/pdf-to-markdown-conversion-open-source-tools-for-llms-3402355](https://www.toolify.ai/ai-news/pdf-to-markdown-conversion-open-source-tools-for-llms-3402355)  
62. Converting PDF Files to Markdown Made Easy: A Comprehensive ..., accessed May 6, 2025, [https://pdf.wondershare.com/convert-pdf/pdf-to-markdown.html](https://pdf.wondershare.com/convert-pdf/pdf-to-markdown.html)  
63. Support Markdown parsing inside collapsible blocks … · Issue \#5 · CrossNox/m2r2 \- GitHub, accessed May 7, 2025, [https://github.com/CrossNox/m2r2/issues/35/linked\_closing\_reference?reference\_location=REPO\_ISSUES\_INDEX](https://github.com/CrossNox/m2r2/issues/35/linked_closing_reference?reference_location=REPO_ISSUES_INDEX)  
64. From PDFs to AI-ready structured data: a deep dive \- Explosion AI, accessed May 7, 2025, [https://explosion.ai/blog/pdfs-nlp-structured-data](https://explosion.ai/blog/pdfs-nlp-structured-data)  
65. Challenges You Will Face When Parsing PDFs With Python \- Seattle Data Guy, accessed May 7, 2025, [https://www.theseattledataguy.com/challenges-you-will-face-when-parsing-pdfs-with-python-how-to-parse-pdfs-with-python/](https://www.theseattledataguy.com/challenges-you-will-face-when-parsing-pdfs-with-python-how-to-parse-pdfs-with-python/)  
66. Build Your Own Markdown To PDF Editor \- Coding Challenges, accessed May 7, 2025, [https://codingchallenges.fyi/challenges/challenge-md-to-pdf/](https://codingchallenges.fyi/challenges/challenge-md-to-pdf/)  
67. The Ultimate PDF to Markdown Converter Guide: Effortless Conversion Methods \- TextIn, accessed May 7, 2025, [https://www.textin.ai/news/199](https://www.textin.ai/news/199)  
68. Convert entire PDFs to Markdown (New Mistral OCR) : r/ObsidianMD \- Reddit, accessed May 7, 2025, [https://www.reddit.com/r/ObsidianMD/comments/1j77tbd/convert\_entire\_pdfs\_to\_markdown\_new\_mistral\_ocr/](https://www.reddit.com/r/ObsidianMD/comments/1j77tbd/convert_entire_pdfs_to_markdown_new_mistral_ocr/)  
69. iamarunbrahma/pdf-to-markdown: Conversion of PDF documents to structured Markdown, optimized for Retrieval Augmented Generation (RAG) and other NLP tasks. Extract text, tables, and images with preserved formatting for enhanced information retrieval and processing. \- GitHub, accessed May 7, 2025, [https://github.com/iamarunbrahma/pdf-to-markdown](https://github.com/iamarunbrahma/pdf-to-markdown)  
70. Extract Text From a Multi-Column Document Using PyMuPDF in Python | Artifex, accessed May 7, 2025, [https://artifex.com/blog/extract-text-from-a-multi-column-document-using-pymupdf-inpython](https://artifex.com/blog/extract-text-from-a-multi-column-document-using-pymupdf-inpython)  
71. python \- Extract text from pdf in correct visual order from PDF \- Stack Overflow, accessed May 7, 2025, [https://stackoverflow.com/questions/78330021/extract-text-from-pdf-in-correct-visual-order-from-pdf](https://stackoverflow.com/questions/78330021/extract-text-from-pdf-in-correct-visual-order-from-pdf)  
72. Top 10 libraries in 2025 for generating PDFs in Python | Nutrient \- PSPDFKit, accessed May 7, 2025, [https://www.nutrient.io/blog/top-10-ways-to-generate-pdfs-in-python/](https://www.nutrient.io/blog/top-10-ways-to-generate-pdfs-in-python/)  
73. PyMuPDF Python API | Advanced PDF Parsing and Extraction, accessed May 7, 2025, [https://products.documentprocessing.com/parser/python/pymupdf/](https://products.documentprocessing.com/parser/python/pymupdf/)  
74. From documents to insights: Advanced PDF parsing for RAG \- KX, accessed May 7, 2025, [https://kx.com/blog/advanced-pdf-parsing-for-rag/](https://kx.com/blog/advanced-pdf-parsing-for-rag/)  
75. Parsing Mathematical PDFs for Enhanced RAG Applications \- Chitika, accessed May 7, 2025, [https://www.chitika.com/mathematical-pdf-parsing-rag/](https://www.chitika.com/mathematical-pdf-parsing-rag/)  
76. Appendix 1: Details on Text Extraction \- PyMuPDF 1.25.5 documentation, accessed May 7, 2025, [https://pymupdf.readthedocs.io/en/latest/app1.html](https://pymupdf.readthedocs.io/en/latest/app1.html)  
77. Looking for Advice to improve RAG perf: PDF Parser, Eval Frameworks : r/LLMDevs \- Reddit, accessed May 7, 2025, [https://www.reddit.com/r/LLMDevs/comments/1gv2lsb/looking\_for\_advice\_to\_improve\_rag\_perf\_pdf\_parser/](https://www.reddit.com/r/LLMDevs/comments/1gv2lsb/looking_for_advice_to_improve_rag_perf_pdf_parser/)  
78. RAG/CHANGES.md at main · pymupdf/RAG · GitHub, accessed May 6, 2025, [https://github.com/pymupdf/RAG/blob/main/CHANGES.md](https://github.com/pymupdf/RAG/blob/main/CHANGES.md)  
79. shaadclt/PDF-Data-Extraction-PyMuPDF4LLM: This ... \- GitHub, accessed May 6, 2025, [https://github.com/shaadclt/PDF-Data-Extraction-PyMuPDF4LLM](https://github.com/shaadclt/PDF-Data-Extraction-PyMuPDF4LLM)  
80. An integration package connecting PyMuPDF4LLM to LangChain \- GitHub, accessed May 6, 2025, [https://github.com/lakinduboteju/langchain-pymupdf4llm](https://github.com/lakinduboteju/langchain-pymupdf4llm)  
81. langchain-pymupdf4llm \- PyPI, accessed May 7, 2025, [https://pypi.org/project/langchain-pymupdf4llm/](https://pypi.org/project/langchain-pymupdf4llm/)  
82. accessed December 31, 1969, [https://github.com/pymupdf/pymupdf4llm](https://github.com/pymupdf/pymupdf4llm)  
83. pymupdf PyMuPDF · Discussions · GitHub, accessed May 7, 2025, [https://github.com/pymupdf/PyMuPDF/discussions](https://github.com/pymupdf/PyMuPDF/discussions)  
84. marker-pdf \- PyPI, accessed May 7, 2025, [https://pypi.org/project/marker-pdf/0.3.2/](https://pypi.org/project/marker-pdf/0.3.2/)  
85. marker-pdf \- PyPI, accessed May 7, 2025, [https://pypi.org/project/marker-pdf/0.2.4/](https://pypi.org/project/marker-pdf/0.2.4/)  
86. Marker PDF to MD \- Make use of different AI models to convert your pdfs into markdown with perfect ocr, latex formulas, tables, images and more\! Supports Mistral AI OCR (free) and self hosted variants\! \- Obsidian Stats, accessed May 7, 2025, [https://www.obsidianstats.com/plugins/marker-api](https://www.obsidianstats.com/plugins/marker-api)  
87. VikParuchuri/marker: Convert PDF to markdown \+ JSON quickly with high accuracy \- GitHub, accessed May 7, 2025, [https://github.com/VikParuchuri/marker](https://github.com/VikParuchuri/marker)  
88. adithya-s-k/marker-api: Easily deployable API to convert PDF to markdown quickly with high accuracy. \- GitHub, accessed May 7, 2025, [https://github.com/adithya-s-k/marker-api](https://github.com/adithya-s-k/marker-api)  
89. ramanathanlab/pdfwf: PDF to Text extraction workflow. \- GitHub, accessed May 7, 2025, [https://github.com/ramanathanlab/pdfwf](https://github.com/ramanathanlab/pdfwf)  
90. Inside Marker: A Guided Source Code Tour for an AI-powered PDF Layout Detection Engine, accessed May 7, 2025, [https://journal.hexmos.com/marker-pdf-document-ai/](https://journal.hexmos.com/marker-pdf-document-ai/)  
91. Nougat Documentation \- Swarms Docs, accessed May 7, 2025, [https://docs.swarms.world/en/latest/swarms/models/nougat/](https://docs.swarms.world/en/latest/swarms/models/nougat/)  
92. Nougat OCR loader \- Llama Hub, accessed May 7, 2025, [https://llamahub.ai/l/readers/llama-index-readers-nougat-ocr?from=](https://llamahub.ai/l/readers/llama-index-readers-nougat-ocr?from)  
93. Extracting Data From PDFs Using AI: Claude 3, Donut, and Nougat \- Parsio, accessed May 7, 2025, [https://parsio.io/blog/extracting-data-from-pdfs-using-ai-claude-3-donut-and-nougat/](https://parsio.io/blog/extracting-data-from-pdfs-using-ai-claude-3-donut-and-nougat/)  
94. Nougat \- Hugging Face, accessed May 7, 2025, [https://huggingface.co/docs/transformers/model\_doc/nougat](https://huggingface.co/docs/transformers/model_doc/nougat)  
95. Best PDF parser for academic papers : r/Rag \- Reddit, accessed May 7, 2025, [https://www.reddit.com/r/Rag/comments/1ilxf1i/best\_pdf\_parser\_for\_academic\_papers/](https://www.reddit.com/r/Rag/comments/1ilxf1i/best_pdf_parser_for_academic_papers/)  
96. Nougat, accessed May 7, 2025, [https://facebookresearch.github.io/nougat/](https://facebookresearch.github.io/nougat/)  
97. facebookresearch/nougat: Implementation of Nougat ... \- GitHub, accessed May 7, 2025, [https://github.com/facebookresearch/nougat](https://github.com/facebookresearch/nougat)  
98. ttrpg-convert-cli/README-WINDOWS.md at main \- GitHub, accessed May 6, 2025, [https://github.com/ebullient/ttrpg-convert-cli/blob/main/README-WINDOWS.md](https://github.com/ebullient/ttrpg-convert-cli/blob/main/README-WINDOWS.md)  
99. ttrpg-convert-cli/docs/configuration.md at main \- GitHub, accessed May 6, 2025, [https://github.com/ebullient/ttrpg-convert-cli/blob/main/docs/configuration.md](https://github.com/ebullient/ttrpg-convert-cli/blob/main/docs/configuration.md)  
100. HawkClaws/pdf2markdown4llm: A library to convert PDF files to Markdown format. \- GitHub, accessed May 7, 2025, [https://github.com/HawkClaws/pdf2markdown4llm](https://github.com/HawkClaws/pdf2markdown4llm)  
101. Extract Text From PDF Files With Python For Use In Generative AI And RAG Solutions, accessed May 7, 2025, [https://build5nines.com/extract-text-from-pdf-files-with-python-for-use-in-generative-ai-and-rag-solutions/](https://build5nines.com/extract-text-from-pdf-files-with-python-for-use-in-generative-ai-and-rag-solutions/)  
102. Convert entire PDFs to Markdown (New Mistral OCR) \- Share & showcase \- Obsidian Forum, accessed May 7, 2025, [https://forum.obsidian.md/t/convert-entire-pdfs-to-markdown-new-mistral-ocr/97924](https://forum.obsidian.md/t/convert-entire-pdfs-to-markdown-new-mistral-ocr/97924)  
103. Parsing PDFs with LlamaParse: a how-to guide — LlamaIndex \- Build Knowledge Assistants over your Enterprise Data, accessed May 7, 2025, [https://www.llamaindex.ai/blog/pdf-parsing-llamaparse](https://www.llamaindex.ai/blog/pdf-parsing-llamaparse)  
104. An Evaluation of Python PDF to Text Parser Libraries \- Unstract, accessed May 7, 2025, [https://unstract.com/blog/evaluating-python-pdf-to-text-libraries/](https://unstract.com/blog/evaluating-python-pdf-to-text-libraries/)  
105. Regex for markdown editing and manuscript clean-up \- GitHub Gist, accessed May 7, 2025, [https://gist.github.com/budparr/112f08a3033dd878d0e271e2af61faef](https://gist.github.com/budparr/112f08a3033dd878d0e271e2af61faef)  
106. Python regex \- cleaning markdown html \- Stack Overflow, accessed May 7, 2025, [https://stackoverflow.com/questions/17119390/python-regex-cleaning-markdown-html](https://stackoverflow.com/questions/17119390/python-regex-cleaning-markdown-html)  
107. Evaluating Your Parsing Solution: A Comparative Look ... \- CambioML, accessed May 6, 2025, [https://www.cambioml.com/blog/evaluate-document-parsing-accuracy](https://www.cambioml.com/blog/evaluate-document-parsing-accuracy)  
108. BobLd/DocumentLayoutAnalysis: Document Layout Analysis resources repos for development with PdfPig. \- GitHub, accessed May 7, 2025, [https://github.com/BobLd/DocumentLayoutAnalysis](https://github.com/BobLd/DocumentLayoutAnalysis)  
109. Image Segmentation Using Color Spaces in OpenCV \+ Python, accessed May 7, 2025, [https://realpython.com/python-opencv-color-spaces/](https://realpython.com/python-opencv-color-spaces/)  
110. Scale pdf to DINA4 and then add a 3mm black margin around it (python) \- Stack Overflow, accessed May 7, 2025, [https://stackoverflow.com/questions/75879451/scale-pdf-to-dina4-and-then-add-a-3mm-black-margin-around-it-python](https://stackoverflow.com/questions/75879451/scale-pdf-to-dina4-and-then-add-a-3mm-black-margin-around-it-python)  
111. Find the bounds of a text string in a PDF using Python | YellowDuck.be, accessed May 7, 2025, [https://www.yellowduck.be/posts/find-the-bounds-of-a-text-string-in-a-pdf-using-python](https://www.yellowduck.be/posts/find-the-bounds-of-a-text-string-in-a-pdf-using-python)  
112. Data Extraction from Unstructured PDFs \- Analytics Vidhya, accessed May 7, 2025, [https://www.analyticsvidhya.com/blog/2021/06/data-extraction-from-unstructured-pdfs/](https://www.analyticsvidhya.com/blog/2021/06/data-extraction-from-unstructured-pdfs/)  
113. accessed December 31, 1969, [https://towardsdatascience.com/extracting-text-from-pdf-files-with-python-a-comprehensive-guide-93289faf022d](https://towardsdatascience.com/extracting-text-from-pdf-files-with-python-a-comprehensive-guide-93289faf022d)  
114. accessed December 31, 1969, [https://www.researchgate.net/publication/344501006\_A\_Method\_for\_Semantic\_Segmentation\_of\_Document\_Layout\_Based\_on\_Visual\_Cues\_and\_Textual\_Features](https://www.researchgate.net/publication/344501006_A_Method_for_Semantic_Segmentation_of_Document_Layout_Based_on_Visual_Cues_and_Textual_Features)  
115. D\&D 5e Statblock Importer. \- RPTools.net, accessed May 7, 2025, [https://forums.rptools.net/viewtopic.php?t=25442](https://forums.rptools.net/viewtopic.php?t=25442)  
116. How to properly extract blocks of data from a file using my RegEx string? \- Stack Overflow, accessed May 7, 2025, [https://stackoverflow.com/questions/72777943/how-to-properly-extract-blocks-of-data-from-a-file-using-my-regex-string](https://stackoverflow.com/questions/72777943/how-to-properly-extract-blocks-of-data-from-a-file-using-my-regex-string)  
117. In Python, how do I extract multiple blocks of text that begin with same pattern, but no distinct end? \- Stack Overflow, accessed May 7, 2025, [https://stackoverflow.com/questions/60660151/in-python-how-do-i-extract-multiple-blocks-of-text-that-begin-with-same-pattern](https://stackoverflow.com/questions/60660151/in-python-how-do-i-extract-multiple-blocks-of-text-that-begin-with-same-pattern)  
118. How to find out if a block of text exists in a file with Python (Python, regex, configuration, development) \- Quora, accessed May 7, 2025, [https://www.quora.com/How-can-you-find-out-if-a-block-of-text-exists-in-a-file-with-Python-Python-regex-configuration-development](https://www.quora.com/How-can-you-find-out-if-a-block-of-text-exists-in-a-file-with-Python-Python-regex-configuration-development)  
119. Clean PDFs: Best Practices for Secure Document Processing \- Mindee, accessed May 7, 2025, [https://www.mindee.com/blog/clean-pdfs-best-practices](https://www.mindee.com/blog/clean-pdfs-best-practices)  
120. Docling for PDF to Markdown Conversion \- Mindfire Technology, accessed May 7, 2025, [https://www.mindfiretechnology.com/blog/archive/docling-for-pdf-to-markdown-conversion/](https://www.mindfiretechnology.com/blog/archive/docling-for-pdf-to-markdown-conversion/)  
121. How Does One Extract Sequentially From a PDF \- API \- OpenAI Developer Community, accessed May 7, 2025, [https://community.openai.com/t/how-does-one-extract-sequentially-from-a-pdf/940756](https://community.openai.com/t/how-does-one-extract-sequentially-from-a-pdf/940756)  
122. \[Script\] CreatureGen, Pathfinder Monster Statblock \-\> Combat ready Token/Journal \- Roll20, accessed May 7, 2025, [https://app.roll20.net/forum/permalink/1669505/](https://app.roll20.net/forum/permalink/1669505/)  
123. accessed December 31, 1969, [https://www.reddit.com/r/LanguageTechnology/comments/10b1xnr/best\_way\_to\_extract\_monster\_stat\_blocks\_from\_pdfs/](https://www.reddit.com/r/LanguageTechnology/comments/10b1xnr/best_way_to_extract_monster_stat_blocks_from_pdfs/)