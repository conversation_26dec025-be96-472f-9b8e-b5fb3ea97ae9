Below is a consolidated look at the **6-attribute** arrangement you're aiming for, alongside **6 derived actions**, each using **two attributes** in a symmetrical manner—except for the “doubling” concept, which you’re **reserving for resources** (like Carry) or skill-based “double down” scenarios (e.g., heavy melee letting you add Strength twice). This ensures a leaner set of derived actions (six total instead of twelve) while still giving each attribute a clear place to shine.

---

## 1. The Final Attributes

- **Strength** (STR)
- **Agility** (AGI)
- **Dexterity** (DEX)
- **Will** (WIL)
- **Intellect** (INT)
- **Acuity** (ACU)

You plan to keep “doubling” purely for resources or specialized skill usage rather than an explicit “Might” or “Mobility” action.

---

## 2. The Six Derived Actions

1. **Fortitude = Will + Strength**
    
    - Used for enduring hardship or resisting injury (mental grit + raw muscle).
    - Example tasks: pushing through pain, resisting poison, carrying on in extreme cold.
2. **Physique = Strength + Agility**
    
    - Used for feats of raw power + speed, or brute-force athleticism.
    - Example tasks: climbing a rope, jumping a ravine, busting down a door.
3. **Finesse = Agility + Dexterity**
    
    - Nimble movement plus fine control—coordinated and graceful.
    - Example tasks: balancing on a beam, diving through a window, freerunning a narrow ledge.
4. **Precision = Dexterity + Acuity**
    
    - Fine motor skills plus sharp perception or quick mental feedback loop.
    - Example tasks: picking a lock, disarming a trap, flicking a precise projectile.
5. **Focus = Acuity + Intellect**
    
    - Speed of noticing info plus depth of reasoning—used for puzzle-solving, analyzing clues.
    - Example tasks: searching a crime scene, scanning documents, synthesizing data swiftly.
6. **Insight = Intellect + Will**
    
    - Cognitive power plus mental fortitude—applies to reading people, applying logic in social contexts, or holding your ground in negotiations.
    - Example tasks: reading a room, influencing a guard, resisting mental manipulations.

_(Names can vary, but these combos line up neatly and are relatively easy to parse.)_

---

### Why This Is Lean and Effective

1. **Only Six**: A nice, small set of derived actions that cover a broad range of physical and mental tasks without overwhelming the table.
2. **Each Attribute Appears Twice**: Strength appears in **Fortitude** and **Physique**, Agility in **Physique** and **Finesse**, Dexterity in **Finesse** and **Precision**, Acuity in **Precision** and **Focus**, Intellect in **Focus** and **Insight**, Will in **Fortitude** and **Insight**. Everyone gets balanced usage.
3. **Skill-Based “Double Down”**: A “heavy melee” skill might let a high-STR fighter add **Strength** again to a **Physique** roll when attacking. Similarly, a “sniper skill” might let you add **Dex** again to a **Precision** roll. This preserves the possibility of “specialized doubling” without spamming permanent 2× combos that inflate your derived action list.

---

## 3. Resource Calculations

You’re still using _2× Attribute_ for certain resources, e.g.:

- **Carry** = 2× Strength (or `base + 2× Strength`)
- **Speed** = 2× Agility (or `base + 2× Agility`)
- **Willpower** = 2× Will + a baseline, or something akin to that (for powering spells, etc.).
- **Knowledge** = 2× Intellect if you want an expanded “Lore” capacity.

These aren’t “rolled” actions but straight calculations that help define a character’s base stats and resource pools.

---

## 4. Using Skills To “Double Down”

- A skill like **Heavy Melee** could say: “When making a Physique roll for heavy-weapon attacks, you may add your STR again (capped at +X),” effectively letting a specialized barbarian out-muscle foes.
- A skill like **Sharpshooter** might add your DEX again to Precision rolls beyond a certain range limit.
- This ensures specialized characters get that big synergy in specific, narrower contexts.

**Pros** of That Approach:

- **Flexibility**: You can keep the default derived actions moderate while letting certain builds “excel” via skill.
- **Simplicity**: The GM or system text can say “Add your Strength again if you have the skill that justifies it,” rather than listing all possible “double” derived actions in the core set.

---

## 5. Conclusion & Feasibility

This design is **both feasible and elegant**:

- **Six** derived actions is a **manageable** set, each with a crisp identity.
- **Doubling** for resource stats (carry, speed, knowledge, willpower) or special skill usage is a neat method for letting specialists overshadow generalists in certain fields.
- **Overlap** between these six is minimal: tasks that obviously rely on raw force/speed go to **Physique**; tasks that rely on dexterity/perception go to **Precision**; etc. **Fortitude** vs. **Insight** also stays distinct (physical endurance vs. mental/emotional resilience and reading others).

If I were presenting this system to a group, I’d keep a **short explanation** of each derived action and **clear examples** so players always know which to roll. The skill-based “Double Down” concept is a fantastic way to let advanced characters outdo generalists in a narrower slice of tasks.

**In short**: You’ve landed on a sweet spot with minimal derived actions (6) that remain broad enough to cover the full spectrum of physical/mental tasks, plus a neat skill-based mechanism to replicate “doubling down” when it matters.