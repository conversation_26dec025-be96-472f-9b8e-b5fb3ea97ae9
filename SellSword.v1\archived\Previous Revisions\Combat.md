## [[Action Dice]]
[[Action Dice]] are the primary resource of combat and refer to the die you can roll to perform any and all actions on your turn. Every player has a maximum of (4) die that they can spend to perform various [[Maneuvers]] and once spent they are no longer able to engage in combat in a meaningful way. These dice are replenished at the END of your turn so as to encourage spending them and not worry about waste.

Some example turns using [[Action Dice|AD]]:

[[Shield Block]] a [[Melee Attack]] from opponent 1 (1 [[Action Dice|AD]]) -> Move inside the [[Sphere of Influence]] of opponent 2 (1 [[Action Dice|AD]]) -> Attack 2nd opponent (2 [[Action Dice|AD]])

[[Ranged Attack]] opponent 1 with your bow (2 [[Action Dice|AD]]) -> Move outside the [[Sphere of Influence]] of opponent 2 (1 [[Action Dice|AD]]) -> [[Dodge]] [[Melee Attack]] from opponent 2 (1 [[Action Dice|AD]])

## Attacks and [[Sphere of Influence]]
Every character has a [[Sphere of Influence]] governed by the [[Reach]] of their melee weapon. Whenever an opponent enters or exits this area they are able to Melee Attack it so long as they have the [[Action Dice|AD]] to do so.

On your turn, if you have an opponent within your [[Sphere of Influence|SOP]] and want to [[Melee Attack]] you spend 2 [[Action Dice|AD]] and add your [[Combat 240117]] modifier depending on if it is [[Physique]] or [[Finesse]]. That number then either applies against the targets [[Passive Defense]], which is the difficulty to hit the opponent when they are not defending themselves, or they may choose to spend 2 [[Action Dice|AD]] in [[Active Defense]] in contest against your roll. The winner of this contest is then able to either hit their opponent or even perform another [[Maneuvers]] based on the [[SUCCESS]] or difference between their two rolls.

An example [[Melee Attack]]

Elven thief chooses to [[Finesse]] attack opponent 1 spending 2 [[Action Dice|AD]] -> opponent 1 chooses to actively defend using [[Physique]]

Elven thief's [[Finesse]] modifier is +4 so their attack is '2d6 + 4' -> opponent 1's [[Physique]] modifier is +2 so their Defense is '2d6 + 2'

Elven thief rolls (8) + 4 for a total of 12
Opponent 1 rolls (9) + 2 for a total of 11

Elven thief succeeds by (1) and hits

In this scenario when opponent 1's turn comes around they would have already spent 2 [[Action Dice|AD]] in [[Active Defense]]. Depending on the challenge of the opponent this may be the total they get for that turn, but if they had remaining [[Action Dice|AD]] they would then have the choice to spend them as they see fit.

The main reason for differentiating between [[Finesse]] and [[Physique]] attacks and defenses is they have varying outcomes from success and also have access to different attacking and defending [[Maneuvers]].

To illustrate lets say in the previous example the Elven thief is dual wielding daggers with a reach of 1, and opponent 1 is using a spear with a reach of 3. In this scenario the thief would not want to stay at range where they are at a disadvantage but rather close with the enemy to the enemies disadvantage. To do this the elven thief used the attack [[Maneuvers|maneuver]] [[Slide In]], which on a success does not reward damage, but rather allows them to bypass the enemies [[Sphere of Influence|SOP]] without being hit. Since the opponent lost it doesn't matter what [[Maneuvers|maneuver]] they were doing as it was unsuccessful and the elf was able to close to 1 hex away accomplishing its goal.

I will go over [[Maneuvers]] writ large later but for the time being there will be many standard [[Maneuvers]] that everyone has access to and then there will specific ones that are gained by either, equipment, racial traits, job features etc.

## Initiative
At first I considered going without initiative, something akin to the Viking campaign, whereas all the players would "declare" their intention and we would then negotiate the rolls in the order of something like Melee Attacks w/o Moving - Ranged Attacks - Movements - Melee Attacks w/ Moving, or really whatever makes the most sense for that specific round.

I am now currently thinking we will likely do something more akin to popcorn initiative, with the GM and players taking turns, letting the players decide who goes when but with every character still having discrete turns. There wont be initiative rolls but rather the GM will just decide if it makes more sense that the players would go first or the monsters would, erring mostly on the side of the players if trying to make a heroic game and otherwise if they face major villain or something where it makes sense for the bad guy to have the upper hand.
## Resources
### Strain
On a players turn when they are doing a [[Finesse]] test, if they choose so they may take on [[Strain]] in order to add '1d6' to the test.

For example if a player is doing a [[Finesse]] [[Melee Attack]] which would normally be '2d6 + [[Finesse|FIN]]' they could choose to [[Strain]] themselves and make it a '3d6 + [[Finesse|FIN]]'

[[Strain]] lowers the characters [[Focus]] by one for each level, allowing players to power up abilities at the risk of losing concentration. If [[Focus]] is lowered below zero all [[Finesse]] tests are done at DisADV.

You also gain [[Strain]] for each spell level that is currently being concentrated on.

### Fatigue
On a players turn when they are doing a [[Physique]] test, if they choose so they may take on [[Fatigue]] in order to add 1d6 to their test.

For example if a player is doing a [[Physique]] [[Melee Attack]] which would normally be '2d6 + [[Physique|PHY]]' they could choose to [[Fatigue]] themselves and make it a '3d6 + [[Physique|PHY]]'

[[Fatigue]] lowers the characters [[Fortitude]] by one for each level. This allows players to power up abilities at the risk of taking more damage in the event they are wounded.
## Wounds
When a character is hit, they do not lose HP but rather are inflicted with a wound. The severity of the wound is dependent on rolling on a wound table that ranges from 1-12, 1 being the worst ie. U DEAD, and 12 being the best, you suffer no injuries.

To determine the outcome you take the character's [[Fortitude]] - (damage received from the hit after all relevant deductions) and that is now the modifier added to '2d6'

What this should allow is tankier characters with high [[Fortitude]] should be capable of sustaining more wounds prior to receiving permanent debilitating damage.

For ex.

Our Dwarf Tank gets hit fairly hard by 6 points of dmg. He is wearing half plate which gives him a [[Damage Reduction]] of 4, equaling 2 points of damage going through.

He has a [[Fortitude]] of 3, so when rolls on the wound table the outcome would be '2d6 + 3 - 2 '

The table will be structured such a way that instant DEATH would only be possible with an overall negative modifer of probably 2 or so, coupled with an extremely bad roll. But is still a possibility. What makes DEATH more likely is from taking sustained wounds with stacking [[Fortitude]] penalties, coupled possibly with [[Fatigue]] and some bad rolls.

## Sample Combat

Elven Striker vs (3) goblins

| ATR | Value |
| ---- | ---- |
| STR | -1 |
| SPD | 2 |
| WIT | 1 |
| WIL | 0 |
| PHY | 1 |
| FIN | 2 |
| FRT | -1 |
| FCS | 1 |
