# Tabletop RPG System Overview

## Stats
- Stats range from -10 to +10, directly corresponding to modifiers.
- Level progression may grant 1 stat boost per level.
- At character creation, players can choose positive stats at the expense of acquiring negative ones.

## Attributes
- Four prime attributes assigned at character creation:
  - **Strength**: Affects equipment usage and damage dealt.
  - **Agility**: Influences combat effectiveness and avoidance.
  - **Intelligence**: Governs spell power or possibly focus points.
  - **Presence/Wisdom/Wit**: Represents force of character, influencing social dynamics.
- Derived attributes:
  - **HP or Stamina**: Sum of Strength and Agility.
  - **Will**: Sum of Intelligence and Presence.
- Point Buy System:
  - Start with 6 points, no attribute over 3.

## Features
- Tailored traits from ancestry or background.
- Ancestry and background provide separate points for feature selection.
- Features allow specialization without being tied to class.

## Combat
- Combat is round-based without individual player turns.
- Players have 4 focus points per round for action allocation.
- Spheres of Influence determine the range of impact around the player.
- Movement occurs between rounds, influenced by Agility and focus points.
- Maneuvers cost focus points, enabling tactical actions.
- Stamina and Will are the main combat resources, with HP/Stamina being a temporary pool.

## Equipment
### Armor
- Adds Damage Reduction (DR), not affecting hit chance.
- Armor is additive and may reduce max stamina by its DR value.
- Strength requirement to equip armor.

### Weapons
- No damage die; damage is consistent except for force multipliers.
- Weapons grant specific traits and maneuvers.

#### Weapons Table
- **Reach**: Denotes the effective range of the weapon in combat.
- **Damage Multiplier**: Increases damage based on the type (Pierce, Slash, Bludgeon).
- **Traits**: MultiAtk allows multiple attacks; Sweeping affects a wider area.

#### Armor Table
- **Strength Requirement (Str R)**: Indicates the strength needed to wear the armor.
- **Damage Reduction**: Shows how much damage is mitigated by the armor.

# Thoughts, Critiques, and Questions

The streamlined stat system is a fresh take that simplifies calculations and could make the game more accessible. The focus point mechanism for combat is particularly innovative, potentially allowing for dynamic and fluid battle scenes. The emphasis on strategic equipment management adds depth to character customization and combat tactics.

Some areas that might need further consideration or clarification:
- How will negative stats affect gameplay and character progression, especially at higher levels?
- The attributes system is compact but may need more definition, particularly regarding Presence/Wisdom/Wit.
- For the combat system, how does simultaneous action resolve in practice? Will there be rules to handle conflicts or actions that affect multiple characters?
- The movement mechanics between rounds could potentially disrupt the flow of combat; how will this be addressed?
- Maneuvers add tactical variety but could become complex. How will balance be maintained between different maneuver options?
- Considering armor affects stamina, is there a risk of heavily armored characters becoming too sluggish to be effective in combat?
- The weapons system is innovative, but might require extensive playtesting to ensure that each weapon feels distinct and useful in its intended context.
- Will there be any special rules for critical successes or failures in combat?
- How will healing and recovery be managed within and outside of combat scenarios?

Overall, the system shows promise and seems to cater to players who enjoy tactical depth and character customization. It might benefit from focused playtesting to refine the mechanics and ensure they interact as intended.
