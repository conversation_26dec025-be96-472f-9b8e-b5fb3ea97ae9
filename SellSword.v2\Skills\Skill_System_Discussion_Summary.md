# Skill System Design Discussion Summary (April 23, 2025)

This document summarizes the key points, decisions, and considerations discussed regarding the SellSword TTRPG skill system.

## 1. Initial Philosophy & Goals

* **Core Goal:** Create a skill system that supports the game's design pillars (verisimilitude, equipment focus, manageable crunch, gritty tone).
* **Initial Structure:** A 3-tier system (Category, Style, Focus/Gear) was proposed in `_Skills_README.md`.
  * Tier 1 (Category): Broad competence, adds dice.
  * Tier 2 (Style): Specific approach, grants unique abilities/Stunts, reduces Complexity, alters resource use.
  * Tier 3 (Focus/Gear): Niche mastery, reduces Difficulty, unlocks advanced uses, enhances effects.
* **Key Principle:** Avoid simple dice stacking beyond Tier 1; higher tiers should grant distinct mechanical advantages.

## 2. Refined 3-Tier Structure

* **Tier 1: Category Skills:** Unchanged. Represents broad competence.
* **Tier 2: Style Skills:** Unchanged. Represents a specific approach or methodology.
* **Tier 3: Specialization Skills:** Renamed from "Focus/Gear" to avoid confusion with the FCS Derived Action. Represents deep mastery of a specific technique, tool, or piece of equipment.
* **Leveling:** Tiers 2 (Style) and 3 (Specialization) were decided to be **level-less** (single purchase) to maintain their distinct identity and avoid incrementalism. Tier 1 (Category) retains its 3 levels.

## 3. Refined SP Costs

* The initial costs (T1: 2/3/4 per level; T2: 3 flat; T3: 2 flat) were revised to better reflect progression and value.
* **Final Agreed Costs:**
  * **Tier 1 (Category):** 1 SP (L1), +2 SP (L2, 3 total), +3 SP (L3, 6 total). *Makes basic competence cheaper.*
  * **Tier 2 (Style):** 2 SP flat. *Makes adopting a style more accessible.*
  * **Tier 3 (Specialization):** 3 SP flat. *Positions deep mastery as a significant investment, costing more than a Style.*

## 4. Refined Prerequisites

* Initial thoughts involved Tier 3 requiring a specific Tier 2 Style. This was deemed too restrictive.
* **Final Agreed Prerequisites:**
  * **Tier 1 (Category):** None.
  * **Tier 2 (Style):** Requires relevant Tier 1 Category **Level 1**.
  * **Tier 3 (Specialization):** Requires relevant Tier 1 Category **Level 2**. *(Allows taking Specializations without being locked into a specific Style first).*

## 5. Benefit Philosophy per Tier

* **Tier 1 (Category):** Primary benefit is **adding dice** (+1/+2/+3) to relevant Derived Action rolls. Simple, broad competence boost.
* **Tier 2 (Style):** Benefits focus on **how** actions are performed: granting unique Stunts/abilities, reducing **Complexity** for specific actions, offering novel resource use. Explicitly **does not** add dice.
* **Tier 3 (Specialization):** Benefits focus on **mastery** within a niche: significantly reducing **Difficulty**, unlocking advanced uses/recipes, enhancing effects beyond Stunts. Explicitly **does not** add dice.

## 6. Discussion on Tier 3 Impact (Difficulty Reduction vs. Dice)

* **Concern:** Does Difficulty Reduction feel as impactful or easy to track as adding dice?
* **Argument for Difficulty Reduction:**
  * Mechanically distinct from Tier 1.
  * Models expertise overcoming external challenges.
  * Controls dice pool inflation.
  * A significant reduction (e.g., -2 Difficulty) is mathematically potent against hard tasks.
* **Argument for Adding Dice:**
  * Simpler calculation for players.
  * Directly contributes to reaching auto-success threshold.
* **Resolution:** Maintain **Difficulty Reduction** as the primary benefit for most Tier 3 Specializations to preserve the tiered philosophy.
* **Refinement:** Standardize the reduction at **-2 Difficulty** for significant impact. Consider adding a minor, secondary non-Difficulty benefit to some Specializations to further enhance their value proposition.

## 7. Stunt Gating

* **Conclusion:** Skills should **not** gate basic maneuvers or Stunts. Anyone can attempt an action (like a "pinning shot"). Skills make these actions **easier** (reduce Difficulty/Complexity) or **more effective** (enhance Stunts), representing mastery rather than permission.

## 8. Starting SP Budget & Progression

* Initial budget (7 SP) felt potentially too low with original costs.
* Testing with revised costs (Cat 1/2/3, Style 2, Spec 3) showed that 7 SP allowed a focused build (e.g., Ranged L1 + Style + Spec).
* A budget of **8 SP** allows a similar build plus one Category L1 -> L2 upgrade, providing slightly more initial competence. This seems like a reasonable starting point.
* Assuming **4 SP gain per level** allows for meaningful choices each level (e.g., a Specialization + Category L1, or a Style + Category L2 upgrade, or significant Category advancement).

## 9. Open Questions / Potential Issues

* **Finalizing Starting SP:** Is 8 SP the right number with the new costs? Requires playtesting.
* **SP Gain Rate:** Is 4 SP per level appropriate for the desired progression speed? Requires playtesting.
* **Benefit Magnitude:** Ensure Tier 2/3 benefits (Complexity/Difficulty reduction, unique abilities) are impactful enough in play to feel worth the SP cost compared to just more Tier 1 dice.
* **Skill List Completeness:** The `Master_Skill_List.md` needs further population and refinement of specific benefits for all skills.
* **Clarity of Application:** Ensure clear guidelines on when specific skills apply and how their benefits interact with core mechanics (Difficulty, Complexity, Stunts, AP).

This summary reflects the current state of the skill system design based on our conversation.
