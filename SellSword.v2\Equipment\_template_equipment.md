---
name: # e.g., Longsword
type: # Weapon | Armor | Shield | Ammunition | Utility
category: # e.g., Bladed, Hafted, Light Armor, Small Shield, Arrow/Bolt
summary: # Concise, technical description for MCP context (e.g., "A large wooden shield with steel boss, provides moderate block and cover.")
description: # Player-facing, evocative description (e.g., "A sturdy round shield made of layered wood, reinforced with a steel boss and rim. Favored by mercenaries for its balance of protection and maneuverability.")
carry: # Number (0-3)
str_req: # Number (Minimum STR)
durability: # Number (0-3)
# Weapon/Shield Specific
offense: # Number (0-3, dice bonus for attacking)
damage: # Number (0-3, base damage)
pierce: # Number (0-3, armor penetration)
force: # Number (0-3, momentum/impact mechanics TBD)
block: # Number (0-3, dice bonus for blocking)
parry: # Number (0-3, dice bonus for parrying)
hook: # Number (0-3, dice bonus for hooking maneuvers)
reach: # Number (0-3, Melee distance added to base)
range: # Number (0-12, Range Rating multiplier of PRC)
# Shield Specific
cover: # Number (0-3, Difficulty dice added to attackers)
# Armor Specific
body_dr: # Number (0-3, Damage Reduction for Body hits)
voids_dr: # Number (0-3, Damage Reduction for Voids hits)
encumbrance: # Number (0-3, mobility limitations of rigid armor, affects FIN and PRC actions)
fatigue: # Number (0-3, stamina draining nature of heavier armor, affects FRT and PHY actions)
# General
stunts: [] # List of associated Stunt names (e.g., ["Disarm", "Target Weakness"])
notes: # String (Description, usage notes)
references: # String/URL (Source/inspiration)
tags: [equipment] # Auto-generated based on type/category
---

# {{name}}

{{description}}
