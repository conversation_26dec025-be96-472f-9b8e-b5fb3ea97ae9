# Sellsword TTRPG Development TODO List

## Combat System

- [ ] Create a comprehensive "Maneuvers" list of mechanically distinct actions for combat/exploration:
  - [ ] **Attack Maneuvers**
    - [ ] Attack Body (baseline attack)
    - [ ] Attack Voids (higher difficulty, bypasses some armor)
    - [ ] Attack Vitals (highest difficulty, potentially lethal)
  - [ ] **Defense Maneuvers**
    - [ ] Dodge (FIN-based, full negation)
    - [ ] Block (PHY/FIN-based with shield/weapon, potential for counter-stunts)
    - [ ] <PERSON> (FIN-based with weapon, potential for counter-stunts)
  - [ ] **Recovery Maneuvers**
    - [ ] Take a Breather (recover Stamina/Will)
    - [ ] Tend Wounds (emergency first aid)
  - [ ] **Movement Maneuvers**
    - [ ] Sprint (increased movement at cost)
    - [ ] Disengage (safely move away from engaged enemies)
  - [ ] **Tactical Maneuvers**
    - [ ] Assess Threat (gain information about enemies)
    - [ ] Take Cover (improve defensive position)
    - [ ] Set for Charge (prepare for incoming enemies)
  - [ ] **Special Maneuvers**
    - [ ] Grapple (control enemy movement)
    - [ ] Disarm (remove enemy weapon)
    - [ ] Feint (set up for future attack)

- [ ] Add these maneuvers to the Rules document with clear AP costs and mechanical effects
- [ ] Ensure each maneuver has clear interaction with the Derived Actions system
- [ ] Review combat mechanics involving Force and AP costs:
  - [ ] Balance slower, high-impact attacks (higher AP, higher Force/Damage) against quicker attacks (lower AP, lower Force/Damage)
  - [ ] Ensure meaningful tactical choices between speed and power
  - [ ] Consider how the Apply Force stunt interacts with this speed/power dynamic
  - [ ] Test different weapon types to ensure they have distinct tactical niches

## Skills System

- [ ] Move Husbandry under Survival as a Style skill (rename to "Animal Handler" or "Beast Master")
- [ ] Review all Category/Style/Specialization relationships for consistency
- [ ] Ensure each skill has clear mechanical benefits beyond just adding dice

## Documentation

- [ ] Update Rules_README.md with the complete maneuvers list
- [ ] Create a quick-reference sheet for common actions and their costs
- [ ] Develop a combat flowchart to visualize the Declarative/Active Phase system

## Playtesting

- [ ] Create sample combat scenarios to test the maneuver system
- [ ] Develop character templates at different skill levels to test progression
- [ ] Run test combats focusing on different tactical approaches

