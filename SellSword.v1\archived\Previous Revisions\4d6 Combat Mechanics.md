Every round your character has 4 AD (d6) representing their available actions that can be spent as they so choose. Whether that is (4) single AD actions, (2) double AD actions, or any combination there of.

Average die roll by d6 number
1 - 3.5
2 - 7
3 - 10.5
4 - 14

Average modifier by skill lvl
No skill - 0
apprentice - 3
Journeyman - 5
Expert - 7
Masterful - 9
## Target Number
Target Number is the value needed to beat or accomplish any given task and is determined by relative difficulty.

| Difficulty | Number |
| ---------- | ------ |
| Easy       | 5      |
| Moderate   | 10     |
| Dificult   | 15     |
| Extreme    | 20     |
Most outside of combat tasks will fall within this scale. The idea being that for someone with no modifiers (+0)

Target number for attacking an opponent is determined by the following:

**Base Value** - 6 This is predicated on the idea that someone not skilled at something would hit an average target 3/4 of the time, based on standard 2AD. An average person throwing a rock at another similarly sized target within their range of effectiveness would have a 72.23% chance of hitting them if they were standing still

**Size** - The smaller something is, the higher the target number, and vice versa. This is calculated on an exponential scale. For instance something with a size difference of -1, slightly smaller than a standard humanoid like a dwarf or halfling, would raise their target value by just 1 point, 6 to 7. Something with a size difference of -2, like a large cat, would raise their target number by 3, -3 +6 and so on.

The opposite is also true. A larger opponent of size +1 would lower their target number by 1. +2 by -3, +3 by -6

Small bird or rodent - Tiny (-3) + 6
Large Cat or Dog - Small (-2) + 3
Dwarf/Halfling/Goblin - Short (-1) + 1
Average (0)
Jotkin (Giantborn) or Bear - Large (+1) - 1
Elephant - Huge (+2) - 3
Gigantic (+3) - 6

**Speed** - The target value of an opponent is always under the assumption they are moving "fighting" thus speed is taken into account. This also means that if they are somehow "restrained" this value no longer applies. This modifier also scales based on size with the idea that something small and fast would be much more difficult to hit than just fast or small alone.

An average sized target, standard humanoid, would receive 1 point per point of speed to their Target Number. The inverse is true for larger sized opponents. Meaning they receive less bonuses for speed per additional point of size.

Gigantic (+3) - 0.25 points per point of speed
Huge (+2) - 0.5 point per point of speed
Large (+1) - 1 point per point of speed
Average (0) - 1 point per point of speed
Short (-1) - 2 points per point of speed to their Target Number
Small (-2) - 3 points per speed
Tiny (-3) - 4 points per speed

**Cover** - Last consideration is any obstructions between the attacker and the target. This can be obstacles in the world but is also granted by equipment such as shields and armor. This is largely based on a scale of 1 to 10 based on body coverage. In regards to armor this value is applied to a different Target Number "Vitals".

Examples
Average opponent with average speed would have a TN of 6 (6 + 0 + 0)
Average opponent with a speed of 2 would have a TN of 8 (6 + 0 + 2 )
Short opponent with average speed would have a TN of 7 (6 + 1 + 0)
Short opponent with a speed of 1 would have a TN of 9 (6 + 1 + (2 * 1))
Small opponent with a speed of 2 would have a TN of 15 (6 +3 + (3 * 2))
## Resolution Style 1 -  Roll vs TN
AD roll + modifier vs TN (Target Number)

Ex 1 Ranger character spends 1AD, shoots an arrow at a goblin. Body shot, lowest TN

1d6 + 5 (Finesse) vs Short Goblin with speed of 1 or (6 + 1 (2 * 1)) = TN 9 roughly 50% chance of success
2d6 + 5 (Finesse) vs Short Goblin TN 9 roughly 90% chance

## Resolution Style 2 - Contested Rolls

AD roll + modifiers vs Opposed AD roll + modifiers

# Damage
Attack rolls and damage rolls are combined. Every character or monster has a base damage that is determined by their weapon. When rolling an attack use one of the Resolution Styles listed above. The amount of damage done is the base value + the difference of the Successful Resolution

Ex 1. Ranger character spends 1AD, shoots an arrow at a goblin that spends 1AD to dodge.

Ranger: 1d6 + 5 (Finesse) vs Short Goblin with speed of 1 (TN 9) + 1d6 + 2 (Reflexes)

Ranger AVG 9 vs Goblin AVG 15

This means that it is definitely suboptimal to only do a 1AD attack against any creatures that has even a modest reflex modifier

Ranger 2AD attack vs Short/Fast Goblin spends 1AD to dodge

Ranger: 2d6 + 5 (Finesse) vs Short Goblin with speed of 1 (TN 9) + 1d6 + 2 (Reflexes)

Ranger AVG 12 vs Goblin AVG 14