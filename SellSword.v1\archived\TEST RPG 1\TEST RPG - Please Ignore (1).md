# Overview
TEST is a d6 based rpg system relying on 2d6 to evaluate most events in the game. 2d6 plus some relevant modifier vs either a target difficulty number or opposed roll. The reason for 2d6 is to make the math relatively easy while still having a curved distribution, meaning in the case of 2d6 it is relatively easy to plan most challenges around an average roll of 7.

In a meets it beats it scenario a target number of 7 is successful more than half the time, or 58%, and because of the uneven distribution, small modifiers or changes in target numbers have outsized effect.
## Using 2d6 and setting Test Numbers
| Target Number | Chance of Success |
| ------------- | ----------------- |
| 3             | 97%               |
| 4             | 92%               |
| 5             | 83%               |
| 6             | 72%               |
| 7             | 58%               |
| 8             | 42%               |
| 9             | 28%               |
| 10            | 17%               |
| 11            | 8%                |
| 12            | 3%                |

This makes it relatively easy to plan target numbers around modifiers a character may have at a specific level as the modifiers shift the curve one way or another.

Level 1: +2-3 for something the character is good at
T7 - 83% 
T9 - 58% 
T11 - 28%
T13 - 8%

This also makes it relatively easy to adjust monster modifiers depending on what you want their chance of success to be against a player character.

For example if we are looking at a minion type character that would face off against lvl 1 PC who has a modifier of 2 to their combat rolls. A minion with no modifier would essentially succeed against the player character 28% of the time. This works out as essentially what we can state is that the level of a monster corresponds to their primary attribute modifier. And as long as this modifier is within 2 deviations of the player character's it should remain balanced similarly.

# Character Creation
## Attributes
When making a character the first step is establishing the (4) prime attributes. This goes off of a point buy system where the max you can have in any one attribute at level 1 is 2 and the overall points must equal 2. Alternatively you can use a point array of:

{2, 1, 0, -1}
### Prime Attributes
[[Strength]] - hit hard and carry heavy things - governs tests to lift a boulder, shield block a swing from a troll
[[Speed]] - fast feet, nimble fingers, sense of balance - governs tests to pickpocket, escape a fireball, dodge a swing from a troll
[[Wit]] - swift of thought and problem solving - governs tests to remember a maze, insult a town guard, learn a spell
[[Will]] - strength of character, presence of mind - governs tests to resist fear, give a rousing speech

### Secondary Attributes
From the (4) prime attributes we combine them in different ways to come to our (4) derived or secondary attributes.

[[Physique]]: Strength + Speed - represents the general physical fitness of a character and is the primary combat modifier of a fighter. Governs all physical tests that do not rely on strength or speed alone. For example scaling a cliff, jumping over a ravine.

[[Finesse]]: Speed + Wit - represents the precision and coordination of a character and is the primary combat modifier of a rogue. Governs all tests requiring precision of mind and body. For example disarming a trap, picking a lock, avoiding surprise.

[[Focus]]: Wit + Will - represents the ability of a character to maintain concentration on a task and is the primary combat modifier when using spells. Governs all tests requiring both intelligence and intuition. For example discerning your surroundings, determining if you are being lied to, investigate a clue, track a bear.

[[Focus]] is also a combat resource that allows a character to increase their finesse roll by 1d6 at the expense of gaining [[Strain]] till the end of that combat.

[[Fortitude]]: Will + Strength - represents a characters resilience against harm. Governs tests to resist physical ailments and positively modifies when rolling on the wounds table when a character is hurt.

[[Fortitude]] is also a combat resource that allows a character to increase their physique roll by 1d6 at the expense of gaining [[Fatigue]] till the end of that combat.

### Example Distribution for a Rogue type character

[[Strength]]: -1
[[Speed]]: 2
[[Wit]]: 1
[[Will]]: 0
Total: 2

[[Physique]]: 1
[[Finesse]]: 3
[[Focus]]: 1
[[Fortitude]]: -1
Total: 4

The goal primarily for characters would be to have one of their combat modifiers at a 3, [[Physique]], [[Finesse]], or [[Focus]].
## Ancestries
Ancestries will work on a point buy system allowing you to "purchase" specific traits from the race that you want excluding the cornerstone trait from that race which is free. 

Example Ancestry: Elf (Faekin)

Fae Senses: Cornerstone Trait - 0 points
Your Fae heritage gives you unparalleled sight and hearing.
ADV on all [[Focus]] tests to perceive your environment that rely on sight or sound and ADV on all [[Wit]] tests to discern magical illusions.

Fae Nimbleness: 2 points
Your Fae blood grows stronger in you and your tether to this world weakens.
Your [[Finesse]] increases by 1 and your [[Fortitude]] decreases by 1 

Ethereal Body and Mind: 2 points
Your [[Focus]] increases by 1 and your [[Physique]] decreases by 1

Child of Nature: 1 point
ADV on all exploration tests while within a forest.

Ancient Knowledge: 1 point
Your people are long lived and their knowledge endures where others would fail.
ADV on [[Wit]] tests on your choice of lore.

I am thinking the point budget would start with (3) and I would want up to 5 or 6 options for each ancestry. A couple more powerful (2) point options and 3-4 (1) point or flavor options.

## Background/Job/Class
Your background or [[Job]] will provide you with additional features and traits further customizing your character into a specific archetype. Similar to ancestries Jobs will have cornerstone features that define them that are free and then offer additional a la carte features you can buy with points. As you level you will gain more points to purchase additional features. 

Example Job: [[Thief]]

You were never there: Cornerstone Feature - 0 points
Whenever you use [[Focus]] on a [[Finesse]] test to disengage or dodge, if successful you do not take on any [[Strain]].

Wet work: 2 points
ADV on [[Finesse]] tests when using bladed weapons with 0 [[Encumbrance]].

A Plan for Everything: 2 points
ADV on [[Finesse]] tests when crafting, placing, or disarming traps.

Shadow's Solace: 1 point
ADV on [[Speed]] tests when sneaking.

What's yours is mine: 1 point
ADV on [[Finesse]] tests when lockpicking.

Five Finger Discount: 1 point
ADV on [[Speed]] tests when pickpocketing

Urban Explorer: 1 point
ADV on all exploration tests while within a settlement of town or larger.

Second Story Work: 1 point
You now have a climb speed equal to half your [[Speed]] and have ADV on [[Speed]] tests when balancing or navigating heights.

Similar to Ancestries I am thinking a budget of 3  points and a similar amount of options. I am currently thinking of giving more points on odd levels along with more ancestry and Job choices. As far as multiclassing goes I think it could work to allow a player to spend their Job points to buy features from a different job at a lower tier, for example a level 3 [[Thief]] could spend points on a level 1 Soldier feature.

Excluding the cornerstone features, many of the Job features will be shared. The Thief and Assassin jobs for example will share many of them but will have distinguishing qualities from each other. In some senses this is akin to subclasses of a type "Rogue" but I am wanting to avoid that nomenclature as there is currently no difference between a Thief taking an Assassin feature or a say a Wizard feature. Though this of course may change.
# Combat
## [[Action Dice]]
[[Action Dice]] are the primary resource of combat and refer to the die you can roll to perform any and all actions on your turn. Every player has a maximum of (4) die that they can spend to perform various [[Maneuvers]] and once spent they are no longer able to engage in combat in a meaningful way. These dice are replenished at the END of your turn so as to encourage spending them and not worry about waste.

Some example turns using [[Action Dice|AD]]:

[[Shield Block]] a [[Melee Attack]] from opponent 1 (1 [[Action Dice|AD]]) -> Move inside the [[Sphere of Influence]] of opponent 2 (1 [[Action Dice|AD]]) -> Attack 2nd opponent (2 [[Action Dice|AD]])

[[Ranged Attack]] opponent 1 with your bow (2 [[Action Dice|AD]]) -> Move outside the [[Sphere of Influence]] of opponent 2 (1 [[Action Dice|AD]]) -> [[Dodge]] [[Melee Attack]] from opponent 2 (1 [[Action Dice|AD]])

## Attacks and [[Sphere of Influence]]
Every character has a [[Sphere of Influence]] governed by the [[Reach]] of their melee weapon. Whenever an opponent enters or exits this area they are able to Melee Attack it so long as they have the [[Action Dice|AD]] to do so.

On your turn, if you have an opponent within your [[Sphere of Influence|SOP]] and want to [[Melee Attack]] you spend 2 [[Action Dice|AD]] and add your [[Combat 240117]] modifier depending on if it is [[Physique]] or [[Finesse]]. That number then either applies against the targets [[Passive Defense]], which is the difficulty to hit the opponent when they are not defending themselves, or they may choose to spend 2 [[Action Dice|AD]] in [[Active Defense]] in contest against your roll. The winner of this contest is then able to either hit their opponent or even perform another [[Maneuvers]] based on the [[SUCCESS]] or difference between their two rolls.

An example [[Melee Attack]]

Elven thief chooses to [[Finesse]] attack opponent 1 spending 2 [[Action Dice|AD]] -> opponent 1 chooses to actively defend using [[Physique]]

Elven thief's [[Finesse]] modifier is +4 so their attack is '2d6 + 4' -> opponent 1's [[Physique]] modifier is +2 so their Defense is '2d6 + 2'

Elven thief rolls (8) + 4 for a total of 12
Opponent 1 rolls (9) + 2 for a total of 11

Elven thief succeeds by (1) and hits

In this scenario when opponent 1's turn comes around they would have already spent 2 [[Action Dice|AD]] in [[Active Defense]]. Depending on the challenge of the opponent this may be the total they get for that turn, but if they had remaining [[Action Dice|AD]] they would then have the choice to spend them as they see fit.

The main reason for differentiating between [[Finesse]] and [[Physique]] attacks and defenses is they have varying outcomes from success and also have access to different attacking and defending [[Maneuvers]].

To illustrate lets say in the previous example the Elven thief is dual wielding daggers with a reach of 1, and opponent 1 is using a spear with a reach of 3. In this scenario the thief would not want to stay at range where they are at a disadvantage but rather close with the enemy to the enemies disadvantage. To do this the elven thief used the attack [[Maneuvers|maneuver]] [[Slide In]], which on a success does not reward damage, but rather allows them to bypass the enemies [[Sphere of Influence|SOP]] without being hit. Since the opponent lost it doesn't matter what [[Maneuvers|maneuver]] they were doing as it was unsuccessful and the elf was able to close to 1 hex away accomplishing its goal.

I will go over [[Maneuvers]] writ large later but for the time being there will be many standard [[Maneuvers]] that everyone has access to and then there will specific ones that are gained by either, equipment, racial traits, job features etc.

## Initiative
At first I considered going without initiative, something akin to the Viking campaign, whereas all the players would "declare" their intention and we would then negotiate the rolls in the order of something like Melee Attacks w/o Moving - Ranged Attacks - Movements - Melee Attacks w/ Moving, or really whatever makes the most sense for that specific round.

I am now currently thinking we will likely do something more akin to popcorn initiative, with the GM and players taking turns, letting the players decide who goes when but with every character still having discrete turns. There wont be initiative rolls but rather the GM will just decide if it makes more sense that the players would go first or the monsters would, erring mostly on the side of the players if trying to make a heroic game and otherwise if they face major villain or something where it makes sense for the bad guy to have the upper hand.
## Resources
### Strain
On a players turn when they are doing a [[Finesse]] test, if they choose so they may take on [[Strain]] in order to add '1d6' to the test.

For example if a player is doing a [[Finesse]] [[Melee Attack]] which would normally be '2d6 + [[Finesse|FIN]]' they could choose to [[Strain]] themselves and make it a '3d6 + [[Finesse|FIN]]'

[[Strain]] lowers the characters [[Focus]] by one for each level, allowing players to power up abilities at the risk of losing concentration. If [[Focus]] is lowered below zero all [[Finesse]] tests are done at DisADV.

You also gain [[Strain]] for each spell level that is currently being concentrated on.

### Fatigue
On a players turn when they are doing a [[Physique]] test, if they choose so they may take on [[Fatigue]] in order to add 1d6 to their test.

For example if a player is doing a [[Physique]] [[Melee Attack]] which would normally be '2d6 + [[Physique|PHY]]' they could choose to [[Fatigue]] themselves and make it a '3d6 + [[Physique|PHY]]'

[[Fatigue]] lowers the characters [[Fortitude]] by one for each level. This allows players to power up abilities at the risk of taking more damage in the event they are wounded.
## Wounds
When a character is hit, they do not lose HP but rather are inflicted with a wound. The severity of the wound is dependent on rolling on a wound table that ranges from 1-12, 1 being the worst ie. U DEAD, and 12 being the best, you suffer no injuries.

To determine the outcome you take the character's [[Fortitude]] - (damage received from the hit after all relevant deductions) and that is now the modifier added to '2d6'

What this should allow is tankier characters with high [[Fortitude]] should be capable of sustaining more wounds prior to receiving permanent debilitating damage.

For ex.

Our Dwarf Tank gets hit fairly hard by 6 points of dmg. He is wearing half plate which gives him a [[Damage Reduction]] of 4, equaling 2 points of damage going through.

He has a [[Fortitude]] of 3, so when rolls on the wound table the outcome would be '2d6 + 3 - 2 '

The table will be structured such a way that instant DEATH would only be possible with an overall negative modifer of probably 2 or so, coupled with an extremely bad roll. But is still a possibility. What makes DEATH more likely is from taking sustained wounds with stacking [[Fortitude]] penalties, coupled possibly with [[Fatigue]] and some bad rolls.