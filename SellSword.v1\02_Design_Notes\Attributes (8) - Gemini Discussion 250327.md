Okay, this looks like a solid foundation for a TTRPG aiming for verisimilitude without excessive crunch. The _Alien RPG_ / _Year Zero Engine_ style dice pool is a good fit for that – it's intuitive, handles degrees of success (though you're currently just counting 6s), and the push/wager mechanic adds tension.

Let's break down your components with a critical eye:

**1. Prime Attributes:**

- **Concept & Scale:** The -3 to +3 scale representing percentiles is excellent. It grounds the numbers nicely and provides a clear baseline ("0 is average"). Leaving room above +3 for the extraordinary is smart design. The rationale that starting Sellswords are already competent makes sense for the gritty, professional feel the name implies.
- **Physical Attributes (END, STR, AGI, DEX):** This is a classic, well-understood split.
    - _Endurance:_ Clear function (capacity, stamina).
    - _Strength:_ Clear function (force, carry).
    - _Agility vs. Dexterity:_ This split (gross motor vs. fine motor) is common and works well. Agility for movement/speed, Dexterity for manipulation/coordination. Thumbs up here.
- **Mental Attributes (CON, INT, ACU, EMP):** This is where things get a bit more crowded and potentially overlapping.
    - _Concentration (CON):_ "Capacity for extended mental activity." This pairs well with Endurance on the physical side. It relates directly to the Will derived stat. Seems solid.
    - _Intellect (INT):_ "Mental prowess, depth of thought." Standard intelligence measure, tied to Knowledge/Lore. Clear.
    - _Acuity (ACU):_ "Mental speed and awareness, take in new information." This feels like it overlaps significantly with Perception-type stats in other games. It's about noticing things and reacting mentally.
    - _Empathy (EMP):_ "Mental control, emotional intelligence and sway." This attribute seems to be doing several jobs: social aptitude (EI, sway) and potentially self-discipline/willpower ("mental control"). This overlaps slightly with the function of Resolve (RES).
    - **Potential Issue:** Is there enough clear mechanical and narrative distinction between Concentration, Intellect, and Acuity? Could Acuity and Intellect potentially be combined, or perhaps Acuity be focused _purely_ on perception/awareness speed? Similarly, is Empathy purely social, or does it also govern internal mental fortitude (which seems covered by Resolve)? You might want to refine the definitions or consider if four distinct mental stats are pulling their weight compared to the clearer physical split. For instance, could "Acuity" be renamed "Perception" or "Awareness" for clarity? Could "Empathy" be renamed "Presence" or "Influence" if it's primarily social, separating it from internal "mental control"?
- **Point Buy:** "5 points with highest being 2, 4 points with highest being 3" - This needs clarification. Do players _choose_ one of these two methods?
    - Method A: Spend 5 points, max attribute score is +2. (Allows broader competence).
    - Method B: Spend 4 points, max attribute score is +3. (Allows higher specialization, but less overall boost).
    - If this is the choice, it's an interesting one for players. Make sure it's presented clearly. Also, can attributes start at negative values using these points, or are points only used to raise attributes from a base of 0? Assuming the latter.

**2. Derived Stats:**

- **Speed (3 + AGI):** Simple, intuitive. Hex-based movement implies tactical grid combat, which fits the Sellsword theme.
- **Carry (3 + STR):** Simple, intuitive. Item slots are a good abstraction instead of tracking weight.
- **Stamina (3 + END*2):** Good resource tied to Endurance. The `*2` multiplier makes Endurance significantly impactful on this pool. Refreshing after combat keeps the action flowing. The "wager" mechanic is interesting – adding dice at the risk of losing the resource on a '1' is a classic push-your-luck element from YZE.
- **Will (3 + CON):** Similar resource for mental actions, tied to Concentration. Note the lack of `*2`. This makes Will pools potentially smaller/tighter than Stamina pools, relative to their governing attribute. Is this intended? "Only refreshes under special circumstances" makes Will _very_ valuable and magic potentially costly or requiring specific actions (rest, meditation, consumables?). This fits a lower-magic or more demanding magic system. The wager mechanic mirrors Stamina.
- **Influence (3 + EMP):** "Resource slot for networking, social connections." How do these slots work? Are they abstract representations of favor/contacts you can 'spend'? Or slots you fill with specific named contacts during creation/play? Needs more definition but is a very interesting concept for representing social capital. The `+ EMP` link is logical.
- **Knowledge/Lore (3 + INT*2):** "Resource slots for various domains of knowledge." Similar question as Influence – what fills these slots? Specific areas of expertise (History, Arcana, Local Geography)? How are they used mechanically – to justify knowing something, or perhaps spent to recall information? The `*2` makes Intellect very potent here.

**3. Derived Actions:**

- **Concept:** Anchoring rolls to a limited set of core actions derived from attributes is a great streamlining approach. Good for GM arbitration.
- **Formulas (Attribute Pairing):** This is elegant. It ensures attributes contribute broadly. Let's check attribute usage:
    - END: FRT (1 use)
    - STR: FRT, PHY (2 uses)
    - AGI: PHY, FIN (2 uses)
    - DEX: FIN, PRC (2 uses)
    - CON: FCS, RES (2 uses)
    - INT: INS (1 use)
    - ACU: PRC, FCS (2 uses)
    - EMP: RES, INS (2 uses)
    - **Observation:** Endurance and Intellect only contribute to _one_ Action each. All others contribute to _two_. This might make END and INT feel less mechanically valuable for direct actions compared to the others, although they have significant impact via Derived Stats (Stamina/Knowledge). Is this imbalance intentional? You might consider if END or INT could logically contribute to another Action to balance their utility, or if their strong impact on Derived Stats is sufficient.
- **Action Definitions:**
    - FRT (END+STR): Physical resistance. Makes sense.
    - PHY (STR+AGI): Broad physical tasks, force and movement. Good. Body Attack mapping is clear.
    - FIN (AGI+DEX): Agility and coordination tasks. Good. Voids Attack mapping fits.
    - PRC (DEX+ACU): Precision, fine motor control, awareness. Good. Vitals Attack mapping is excellent, high risk/reward implication. Scribe (Magic) fits.
    - FCS (ACU+CON): Awareness, perception, sustained mental effort. Good. Weave (Magic) fits.
    - RES (CON+EMP): Mental fortitude, resisting influence, channeling. Good. Evoke (Magic) fits.
    - INS (EMP+INT): Social understanding, problem-solving, deduction. Good. Scry (Magic) fits.
- **Overall:** The Actions seem well-defined and cover a broad range of typical RPG tasks. The mapping to combat and magic types is clear and provides a good structure.

**4. Skills:**

- **Concept:** Layering skills on top of Actions for customization is standard and effective.
- **Example (Archery):** This is a great example. Tiered skills, specialization choices (Marksman/Quickshot), adding dice, alternative resource use (wager Stamina on Finesse), ignoring penalties. This shows good depth potential.

**5. Anatomy of a Dice Pool:**

- **Formula:** Action + Skill + Resource + Gear - Difficulty = Dice Pool. Clear and concise.
- **Gear Dice = Damage:** "The Gear dice representing the bow would be added together to equal damage done." This part needs careful consideration and clarity.
    - Does this mean if a Bow adds +2 dice to the _pool_, it inherently deals 2 damage? What if it's a magical bow that adds +4 dice? Does it do 4 damage?
    - What about melee weapons? Greatsword +3 dice = 3 damage?
    - How does this interact with successes? Is damage fixed by the gear's dice value, regardless of how many successes are rolled? Or do successes _on those specific gear dice_ count towards damage? The former is simpler but less dynamic; the latter adds complexity.
    - How does this work for non-weapon gear? If Masterwork Lockpicks add +2 dice to a Precision roll, do they deal "damage"? _Assumption:_ This damage rule likely applies only to _weapons_.
    - **Critique:** Tying the number of dice Gear adds _directly_ and _only_ to its damage value might be _too_ simple and create scaling issues or limit design space. Consider alternatives:
        - Gear adds dice to the pool for success, and weapons have a separate Damage rating (e.g., Bow +2 dice, Damage 2).
        - Gear adds dice, and the _number of successes_ rolled determines damage (perhaps capped by weapon type).
        - Gear adds dice, and maybe _successes rolled on the Gear Dice specifically_ determine damage.
        - Your current method _can_ work, but ensure it feels right and scales properly. Does a dagger (+1 die?) feel threatening enough if it only does 1 damage? How do you represent exceptionally well-made but not inherently more damaging items (e.g., a perfectly balanced sword might add dice to Finesse but not necessarily do more base damage)?
- **Difficulty:** Subtracting dice from the pool _before_ rolling is standard YZE practice. It works well – makes difficult tasks feel challenging as you have fewer chances for success. How is Difficulty determined? GM fiat based on circumstance? Opposed rolls? Environmental factors?

**Overall Feedback:**

- **Strengths:**
    - Clear design goals (verisimilitude, equipment focus, manageable crunch).
    - Solid core mechanic (dice pool, wager/push).
    - Good attribute scale and physical attribute split.
    - Derived Actions system is elegant and streamlines gameplay.
    - Skill system shows good potential for customization.
    - Interesting concepts for derived resources (Stamina, Will, Influence, Knowledge).
- **Areas for Consideration/Refinement:**
    - **Mental Attributes:** Review for potential overlap (CON/INT/ACU/EMP) and clarify definitions/roles. Ensure all four feel distinct and necessary.
    - **Point Buy:** Clarify the two options.
    - **Derived Stat Calculations:** Double-check the `*2` multipliers (END_2, INT_2) versus `*1` (CON_1, EMP_1) – ensure this asymmetry is intended.
    - **Influence/Knowledge Slots:** Define how these slots function mechanically.
    - **Will Refresh:** Define the "special circumstances" – this heavily impacts magic users.
    - **Attribute Balance in Actions:** Note that END and INT contribute to fewer Actions than other attributes. Decide if this is acceptable or needs tweaking.
    - **Gear Dice = Damage:** This is the biggest area needing clarification and potential refinement. Ensure it works intuitively and scales well for different weapon types and qualities. How does armor interact (subtract dice from pool, reduce damage taken, etc.)?
    - **Wager Mechanic:** Playtest the 'lose resource on 1' mechanic. How often does it trigger? Is it too punishing, or just right for tension?

This is a promising start! The core structure is logical and aligns with your goals. Keep refining the definitions, especially around the mental attributes and the gear/damage interaction, and definitely get it to the table for playtesting. Good luck with Sellsword!

## My Reply
- **Areas for Consideration/Refinement:**
    - **Mental Attributes:** Review for potential overlap (CON/INT/ACU/EMP) and clarify definitions/roles. Ensure all four feel distinct and necessary.
      
      Yes this is something that i have been working on continually and have been going back and forth on. I like the idea of symmetry between physical and mental attributes but as you have pointed out putting mental characteristics into neat boxes is difficult and there is overlap. The main goal is to enable players to best realize the idea they have in their head of what their character is and acts.
      
      I have had previous incarnations where Empathy was just Presence. My juxtaposition comes from wanting players to be able to have a strong willed character, that isn't necessarily charismatic. Or say a very perceptive character at picking up clues, but doesn't mean they're wise.
      
	 If you have a better mapping of mental characteristics that still allows me to get the same or similar derived actions I'd love to hear them out?
      
    - **Point Buy:** Clarify the two options. 
      
      They are as you assumed. If the player wants to have one of their attributes be a 3 (peak), then they only have 4 points to spend. Otherwise they get 5. They are allowed, even encouraged to take -1's in attributes if they so desire. The total must equal 4 or 5 at the end overall.
      
    - **Derived Stat Calculations:** Double-check the `*2` multipliers (END_2, INT_2) versus `*1` (CON_1, EMP_1) – ensure this asymmetry is intended.
      
      Yes this asymmetry is intended. as you pointed out since those attributes only contribute to 1 action I doubled their contribution to the derived stats. I am not in love with this solution
      
    - **Influence/Knowledge Slots:** Define how these slots function mechanically.
      
      Influence and Knowledge slots act in many ways just like gear slots. They provide situational bonuses or allow for ignoring difficulties. They vary in slot requirement and have increasing benefits like the other skills. Herbalism (1), 1 slot, Herbalism (2), 2 slots. and so on. where as the depth of knowledge grows the benefit increases also. The main difference between Knowledge and skills is, Skills are DO and Knowledge is KNOW. So you could be using the focus action to track a deer, but because you have Woodcraft (2) Knowledge, you gain X benefit.
      
      Ex. Influence - Blackmail (2) - Enter name here, takes up 2 slots
      The player has kompromat on some person or organization that allows them to leverage them for some bonus.
      
      This is largely a downtime thing, which we haven't gotten into. but there will be a downtime portion of the game that is largely focused on preparing for the oncoming mission. Think planning for the heist, a la oceans 11.
      
    - **Will Refresh:** Define the "special circumstances" – this heavily impacts magic users.
      
      Will in effect represents the overall 'health' or staying power of the party. Their ability to carry on with their mission. There will be general ways characters regain Will, like an important Victory in battle, or a hearty meal by the fire. But players will also be able to gain special traits that allow them to personally renew will depending on various outcomes. A hunter type regains will when they track down their prey sort of thing. A defender type regains a will if they keep an ally from going down. That kind of thing. Will is the primary lever by which the GM is able to punish or reward the players for good play
      
    - **Attribute Balance in Actions:** Note that END and INT contribute to fewer Actions than other attributes. Decide if this is acceptable or needs tweaking.
      
      As mentioned above
      
    - **Gear Dice = Damage:** This is the biggest area needing clarification and potential refinement. Ensure it works intuitively and scales well for different weapon types and qualities. How does armor interact (subtract dice from pool, reduce damage taken, etc.)?
      
      So gear, weapons in particular, range from bonuses of 1 to 3 in various categories. As you assumed the only time we sum the gear dice for damage is if it is useful, ie a weapon.
      
      Weapons all have characteristics such as Damage, Pierce, Block, Parry, etc. and they are all rated along those characteristics from 1 to 3. So depending on what the player is doing they may add that many gear dice. If a player is 'blocking' with their sword and it has block (1) they get 1 dice, but if they blocked with their shield that had block (3) they get 3
      
      The intent is to give many varied bonuses and encourage using the 'right tool for the job' but also not overly differentiate in lethality between a longsword and a dagger. Both will easily kill you if you get stabbed.
      
      In this way the gear dice do double duty, they add to your pool and thus your chance for success, but the sum of them also equates to the damage done. When attacking with a dagger, Damage(1), you may only get to add 1 dice to your pool and do max 1-6, damage, but in the hands of a high Finesse character it will be as or more deadly than a greatsword if they are, "attacking vitals" or bypassing armor with their Pierce(2). Armor values are also from 1-3 and have categories, body and voids. So a gambeson could be Body (1) Voids (1) meaning it has good coverage where a Breastplate could be Body (3) Voids (0)
      
      This also brings up the question of health points or lack there of but we can get into that later. Long story short there are no health points. players get wounds of various levels of debilitation.
      
    - **Wager Mechanic:** Playtest the 'lose resource on 1' mechanic. How often does it trigger? Is it too punishing, or just right for tension?
      
      I really like the wager mechanic and it is the main reason why i am using a d6 pool system at all. Previously i was doing a roll under system with 3d6 a la GURPS as i think statistically it is much better with the curve but I fell in love with the stress mechanic from Alien RPG


## Gemini 2
Thanks for the detailed clarifications! That helps refine the picture significantly. Let's revisit those points:

1. **Mental Attributes:**
    
    - Your goal of enabling specific character concepts (high Willpower/low Charisma, high Perception/low Wisdom) is key. The symmetry is nice conceptually, but clarity and enabling player expression should take priority if they conflict.
    - Given your goals and the previous discussion, how about this potential mapping? It keeps four stats but aims for clearer roles based on your examples:
        - **Intellect (INT):** Stays the same. Logic, knowledge, analysis, problem-solving depth. ("Book smarts", puzzle-solving).
        - **Awareness (AWA):** Replaces Acuity. Focused purely on perception, noticing details, situational alertness, reading the environment/immediate cues. ("Street smarts", finding clues, spotting ambushes).
        - **Resolve (RES):** Takes the internal fortitude aspect. Mental grit, self-discipline, concentration, resisting fear/pain/coercion, sustained mental effort. (This is your "strong-willed" stat, separate from social ability. Takes over CON's role).
        - **Presence (PRE):** Replaces Empathy/old Presence. Focused purely on social interaction, charisma, persuasion, command, understanding/influencing others. (Your "charismatic" stat).
    - **Mapping to Derived Actions:**
        - **PRC (Precision):** DEX + **AWA** (Hand-eye coordination needs sharp awareness). _Magic: Scribe_
        - **FCS (Focus):** **AWA** + **RES** (Sustained awareness/tracking needs perception + mental fortitude/concentration). _Magic: Weave_
        - **RES (Resolve Action):** **RES** + **END**? (Resisting hardship uses mental grit + physical endurance). Or maybe just **RES x 2**? Or keep **RES + PRE** (Resisting mental _influence_ relies on inner grit + social adeptness/reading intent?). Let's assume **RES + PRE** for now, as it maintains the original structure well. _Magic: Evoke_
        - **INS (Insight):** **PRE** + **INT** (Reading people/social puzzles needs social awareness + analytical ability). _Magic: Scry_
    - **Attribute Usage Check (with RES+PRE):**
        - INT: INS (1 use)
        - AWA: PRC, FCS (2 uses)
        - RES: FCS, RES (action) (2 uses)
        - PRE: RES (action), INS (2 uses)
        - DEX: PRC (1 use) - _Wait, DEX was used in FIN too._ FIN was AGI+DEX. PRC was DEX+ACU.
        - Let's re-check the original Actions:
            - FRT (END+STR)
            - PHY (STR+AGI)
            - FIN (AGI+DEX)
            - PRC (DEX+ACU) -> DEX + **AWA**
            - FCS (ACU+CON) -> **AWA** + **RES**
            - RES (CON+EMP) -> **RES** + **PRE**
            - INS (EMP+INT) -> **PRE** + **INT**
        - **New Count:** END(1), STR(2), AGI(2), DEX(2), AWA(2), RES(2), PRE(2), INT(1).
        - This configuration (INT, AWA, RES, PRE) seems to offer clearer definitions while mapping cleanly onto your existing Derived Action structure. It explicitly separates internal willpower (Resolve) from social skill (Presence) and perception (Awareness) from deep thought (Intellect). It still leaves END and INT contributing to only one Action each.
2. **Point Buy:** Understood. 5 points (max 2) OR 4 points (max 3), negatives allowed, sum must match the starting points (4 or 5). This offers a meaningful choice between specialization and breadth. Good.
    
3. **Derived Stat Calculations / END & INT Balance:**
    
    - Okay, the `*2` multiplier was intentional compensation. Since you're not fully sold on it, consider these alternatives:
        - **Option A (Revisit Actions):** Could END logically contribute elsewhere? Perhaps Fortitude (FRT) is just END x 2? Or could it add to Physique rolls related to _lasting_ effort (PHY = STR+AGI+END feels clunky though). Could Intellect add to Precision (PRC = DEX+AWA+INT for calculated shots?) or Focus (FCS = AWA+RES+INT for analytical observation?) - again, adding a third stat is clunky. Maybe a specific magic action relies heavily on INT?
        - **Option B (Enhance via Skills/Features):** Instead of just bigger pools, give END and INT more active roles through the Skill system or unique Talents.
            - _Example (END):_ A "Tireless" Talent might let you spend Stamina to ignore penalties from minor wounds for a scene. Or a skill could let you use END instead of AGI for climbing checks.
            - _Example (INT):_ An "Analysis" Talent might let you spend a moment (an action?) to use INT to gain bonus dice (+INT?) on your _next_ single action against a specific target by identifying a weakness. A skill could let you use INT instead of AWA for investigation-based Focus checks.
        - Option B feels potentially more engaging than just larger passive pools via the `*2` multiplier.
4. **Influence/Knowledge Slots:** Understood. Functioning like 'social/mental equipment' slots that grant situational bonuses or capabilities, acquired and potentially improved like skills. The "Know vs. Do" distinction is excellent. Tying Influence into downtime/preparation phases makes perfect sense. This sounds like a really flavorful and mechanically interesting way to handle non-physical assets and expertise.
    
5. **Will Refresh:** Understood. It's a party/narrative resource reflecting morale, endurance, and success, regained through significant moments and specific character triggers. Using it as a GM lever for rewarding good play and punishing poor choices/bad luck makes it very central to the game's flow. The scarcity and specific refresh conditions fit the gritty tone.
    
6. **Gear Dice = Damage:**
    
    - Okay, this is much clearer. Weapons have _ratings_ (1-3) in characteristics (Damage, Pierce, Block, Parry, etc.). When you perform an action where that characteristic applies, you add dice equal to that _rating_ to your pool.
    - **The Ambiguity:** Your phrasing "the sum of them also equates to the damage done" and "Damage(1), you may only get to add 1 dice to your pool and do max 1-6, damage" is still a bit confusing regarding the _final damage calculation_. Let's clarify the possibilities:
        - **Possibility A: Damage = Base Rating.** (A weapon with Damage 3 always deals 3 'points' of damage on a hit, which is then applied against armor/wounds). Simple, predictable.
        - **Possibility B: Damage = Number of Successes Rolled.** (More successes = more damage, perhaps capped by weapon type?). More dynamic, swingy.
        - **Possibility C: Damage = Base Rating + Extra Successes.** (Damage 3 weapon, 2 successes rolled = 3 + (2-1) = 4 damage?). Rewards skillful hits.
        - **Possibility D: You roll dice equal to the Damage Rating for damage.** (Damage 3 = roll 3d6 for damage?). Common in some systems. Your "max 1-6 damage" for Damage(1) hints at maybe rolling 1d6?
        - _Clarification Needed:_ Which of these (or another method) is it? How is the final damage number determined _after_ you succeed on the attack roll?
    - **Armor & Wounds:** The Body/Voids armor rating system (1-3) sounds great for interacting with different attack types (PHY vs FIN/PRC). Tying this directly into a Wound system (instead of HP) strongly reinforces the verisimilitude and lethality goals. Excellent fit.
    - **Overall Gear:** This approach strongly emphasizes 'right tool for the job' and equipment choice, hitting your design goal perfectly. Giving different bonuses (Block, Parry, Pierce) makes gear choices tactical. Keeping base Damage ratings tight (1-3) while differentiating through Pierce etc. achieves the "dagger can be deadly" feel.
7. **Wager Mechanic:** It's a fantastic mechanic from YZE, glad you're embracing it. It adds significant tension and meaningful resource management choices.
    

**Summary of Recommendations:**

- Consider adopting the refined Mental Attributes (INT, AWA, RES, PRE) for clarity and better support of character concepts.
- Decide if the `*2` Derived Stat multiplier for END/INT is sufficient, or if you want to explore integrating them more into Actions or enhancing their impact via Skills/Talents (Option B seems promising).
- Clearly define the _exact_ calculation for weapon damage after a successful hit roll based on the weapon's Damage rating and the roll's outcome.
- Keep the Influence/Knowledge slots, Will refresh, Armor/Wound system, and Wager mechanic – they sound like strong pillars supporting your design goals.

You're definitely on the right track. The system has a clear identity emerging. Keep refining, and playtest is king!