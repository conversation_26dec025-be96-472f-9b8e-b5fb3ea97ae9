---
# METADATA - Replace placeholder values with actual monster data.
name: "Monster Name"
description_short: "A brief, evocative description (1-2 sentences)."
description_long: |
  A longer, player-facing description. 
  This section should convey tone, atmosphere, physical appearance, typical behaviors, 
  and any relevant lore or setting information that players might observe or know.
  Use vivid language to bring the monster to life.
size: "SizeCategory (e.g., <PERSON>, <PERSON>, <PERSON>, Large, Huge, Gargantuan)"
type: "Monster Type (e.g., Beast, Humanoid, Fae, Aetherborn, Primordial, Undead, Construct)"
tags: [tag1, tag2, "key descriptive word"] # e.g., [monster, beast, pack_hunter, nocturnal]

# CORE COMBAT STATS - Refer to SellSword.v4/Monsters_README.md for guidelines.
combat_pool: "Xd6 (e.g., 4d6)" # Base dice pool for most actions.
action_points: "X (e.g., 3)"    # AP per round.
speed: "X (SpecialMovement X)" # e.g., 4 (<PERSON><PERSON><PERSON> 3, <PERSON> 5)
wounds: # Tiered wound threshold pools. Refer to v4 Wounds System for effect details.
  minor: X # Number of minor wounds monster can take.
  major: X # Number of major wounds.
  grievous: X # Number of grievous wounds.
  deadly: X # Number of deadly wounds.

# DEFENSE - Refer to SellSword.v4/Monsters_README.md for guidelines.
target_difficulty: "TD X" # Base Target Difficulty. Traits may modify this.
dr: # Damage Reduction
  body: X
  voids: X
# Optional: Add specific resistances or immunities here if they are simple DR modifications.
# Complex resistances/immunities should be detailed in Monstrous Traits.
# Example: resist_fire: half (takes half damage from fire)
# Example: immune_poison: true

# WEAKNESSES & VULNERABILITIES
# List v4 Player Character Action Scores where the monster is disadvantaged.
# See Monsters_README.md Section III for details.
# Action Scores: Physique, Finesse, Precision, Focus, Insight
weaknesses: [] # e.g., [Physique, Finesse] - roll combat_pool minus 2 dice.
vulnerabilities: [] # e.g., [Focus] - roll combat_pool minus 4 dice.

# ACTIONS
# Define all AP-costing actions the monster can perform.
# Refer to Monsters_README.md Section II and IV for action structure and damage scale.
actions:
  - name: "Action Name (e.g., Bite, Claw, Special Ability)"
    ap_cost: X # (default 1 if unspecified)
    reach: X   # (0 for self/touch, 1+ for melee range)
    damage: X  # (Base damage, see monster damage scale 1-6 in README)
    pierce: X  # (Reduces target's DR)
    force: X   # (Impact/momentum, interacts with effects)
    notes: "Brief mechanical or flavor notes for the action."
    extra_success_effects: # Optional: Effects purchasable with extra successes.
      - cost: X # Number of extra successes required.
        effect: "Description of the effect."
      - cost: Y
        effect: "Another effect."
  - name: "Another Action"
    ap_cost: X
    # ... other fields ...

# MONSTROUS TRAITS
# Passive abilities or unique mechanics that don't cost AP.
# Provide full mechanical descriptions.
monstrous_traits:
  - name: "Trait Name"
    description: |
      Full mechanical description of the trait. 
      Explain how it interacts with game rules, its triggers, and its effects.
      If it's a common trait, you might optionally reference a central `Monstrous_Traits_v4.md`
      (e.g., "See [[Monstrous_Traits_v4#TraitName|TraitName]]"), but always include the full
      description here for GM ease of use during play.
  - name: "Another Trait"
    description: |
      Description of another trait.

# BEHAVIOR & OTHER
tactics: |
  Describe typical combat behavior, strategies, and how it uses its abilities.
  What does it prioritize? When might it flee?
loot: |
  Potential resources, items, or crafting materials gained from defeating the monster.
  Follow loot guidelines from Monsters_README.md (e.g., "Hide (craftable), 1d6 teeth").
environment: |
  Typical habitats, lairs, or regions where the monster is found.
---

<!-- 
GM NOTES (Optional - anything below the '---' is not part of the structured data but can be for GM eyes only)

- Concept: 
- Role: (e.g., Striker, Brute, Controller, Ambusher, Swarm, Support, Elite, Titan)
- Synergies: (How do its abilities work together?)
- Counterplay: (What are common ways for players to deal with this monster?)
-->
