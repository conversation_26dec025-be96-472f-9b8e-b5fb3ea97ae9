# Equipment

This folder contains details on various items characters can use, including weapons, armor, shields, ammunition, and utility items. The design philosophy emphasizes that **Gear Matters**.

## Subdirectories

* [[Weapons/]]: Melee and Ranged weapons.
* [[Armor/]]: Protective gear worn on the body.
* [[Shields/]]: Items held for defense.
* [[Ammunition/]]: Projectiles for ranged weapons.
* [[Utility/]]: Miscellaneous gear like light sources, tools, clothing.

## File Structure

* Each piece of equipment has its own Markdown file (e.g., `Longsword.md`, `Gambeson.md`).
* Files use the `_template_equipment.md` structure, detailing stats in YAML frontmatter.

## Key Mechanics (Summary - see [[_Rules_README]] or specific files for full details)

* **Ratings:** Most equipment stats (Offense, Damage, Pierce, Force, Block, Parry, Hook, Cover, DR) are intended to be on a 1-3 scale, representing their effectiveness.
* **Offense:** Adds dice to attack rolls, increasing the chance of success in combat.
* **Damage:** Fixed base damage value dealt on a successful hit, reduced by the target's Armor Value (Damage Reduction).
* **Pierce:** Represents the weapon's ability to bypass or reduce the target's Armor Value. Higher Pierce values reduce the effective DR applied to incoming damage.
* **Force:** Reflects the momentum or impact of a weapon, potentially interacting with special mechanics such as knockback or armor sundering (to be detailed in future rules updates).
* **Block/Parry/Hook:** Provide dice bonuses to defensive maneuvers, improving chances to avoid or counter attacks.
* **Reach:** Indicates the effective melee distance added to the base reach of the character or weapon, influencing engagement range.
* **Range:** Determines the optimal distance for projectile weapons, affecting accuracy and damage falloff. Range is a multiplier of the character's Precision (PRC) derived action.
* **Cover (Shields):** Adds Difficulty dice to attackers targeting the shield bearer, representing the shield's protective coverage.
* **Damage Reduction (DR - Body/Voids):** Provides Damage Reduction against hits to specific body locations. Armor reduces damage but does not affect the chance to be hit.
* **Carry:** Represents the bulk and weight of the item, affecting encumbrance and the Action Point cost to use the item in combat. Higher Carry values increase AP cost and may contribute to Fatigue.
* **Encumbrance:** Represents how much the item restricts movement and agility. The Encumbrance value itself is typically calculated as the item's Voids Damage Reduction (VoidsDR) minus 1. This value imposes a -1 die penalty on Physique (PHY), Finesse (FIN), and Precision (PRC) action rolls on a 1:1 basis and also reduces Speed (SPD) by the same amount. This reflects the binding nature of more effective armor in gaps, slowing movement.
* **Fatigue:** Represents the stamina drain caused by wearing or using the item, reducing the character's maximum Stamina. This value is typically calculated as the item's Carry minus 1, reflecting the taxing nature of heavy equipment.
* **Two-Handed Use:** Holding a weapon in both hands reduces its Action Point cost by 1 (minimum 1 AP) for attacks, blocks, parries, and other maneuvers. For example, a longsword with Carry 2 normally costs 2 AP to use, but only 1 AP when wielded two-handed. This trade-off encourages tactical choices between speed and control.
* **Durability:** Measures the item's resilience and how much wear it can sustain before becoming less effective or broken. Durability affects repair needs and item longevity.
* **Ammunition:** Provides Damage and Pierce values for ranged weapons, influencing their effectiveness.
* **Stunts:** Some equipment grants access to specific Stunts, allowing special maneuvers or effects during combat.

Refer to individual equipment files for specific stats and the [[../Weapons and Gear.md]] document (or its future equivalent in the `Rules/` folder) for detailed mechanical explanations.

## Armor Conditions – Sunder

This section defines the Sunder condition that armor can suffer when subjected to concentrated Force effects (e.g., via the Apply Force stunt):

* **Level 1 (Minor Sunder):** Reduce DR at the affected location by 1. Repair: 1 Action Point (e.g., tightening straps or clearing debris).
* **Level 2 (Damaged Sunder):** Reduce DR at the affected location by 2. Repair: 1 Exploration Action Point plus appropriate supplies.
* **Level 3 (Broken Sunder):** Armor at the affected location provides 0 DR. Repair: Requires downtime and access to workshop facilities.

Monsters simplify Sunder by suffering a flat –1 DR penalty per Force applied (no further tracking). Players track Sunder levels per armor location.

## Cross-References to Rules and Related Documents

* For detailed combat mechanics, see [[_Rules_README]].
* For wound system and damage resolution, see [[_Wounds_README]].
* For Stunts and their effects, see [[_Stunts_README]].
* For character attributes and derived stats, see [[_Attributes_README]].
* For skills and their impact on equipment use, see [[_Skills_README]].
* For conditions affecting equipment use (Fatigue, Encumbrance, Strain), see [[_Rules_README#Conditions]].
* For magic-related equipment and effects, see [[Magic_Guide]] and [[_Lore_README]].

## Equipment Design Guide: Step-by-Step Justification Using a Standard Item

This guide provides a detailed walkthrough of designing a standard equipment item for Sellsword TTRPG. It explains each stat and value choice, serving as a reference standard for future equipment creation.

### Example Item: Longsword

---

#### 1. Basic Metadata

* **name:** Longsword  
  The item's common name, recognizable and descriptive.

* **type:** Weapon  
  Categorized as a weapon for combat use.

* **category:** Bladed  
  Specifies the weapon type, influencing style and potential interactions.

* **summary:**  
  A versatile, balanced bladed weapon designed for cutting and thrusting, with moderate weight and reach. Provides solid offensive and defensive capabilities.  
  A concise, setting-agnostic overview of the item’s physical properties and basic mechanics.

* **description:**  
  A well-crafted longsword, favored by experienced fighters for its balance of cutting and thrusting capabilities. Its weight and design require skill and strength to wield effectively, offering both offensive power and defensive versatility.  
  A practical description as might be given by a quartermaster or weapons expert advising a user.

---

#### 2. Core Stats

* **carry:** 2  
  Represents moderate bulk and weight. Affects encumbrance and Action Point (AP) cost.  
  Justification: Longswords require some strength to wield and slow down actions slightly.

* **str_req:** 1  
  Minimum Strength needed to wield effectively.  
  Justification: Reflects the physical demand of handling a longsword.

* **durability:** 3  
  High resilience due to quality steel construction.  
  Justification: Longswords are durable and maintain effectiveness over time.

* **encumbrance:** 0  
  Does not significantly hinder movement.  
  Justification: Balanced design allows for mobility.

* **fatigue:** 1  
  Does not cause stamina drain beyond normal exertion.  
  Justification: Reasonable weight and balance.

---

#### 3. Combat Stats

* **offense:** 2  
  Adds dice to attack rolls, representing weapon effectiveness.  
  Justification: Longswords are reliable offensive weapons.

* **damage:** 2  
  Base damage dealt on a successful hit.  
  Justification: Moderate damage consistent with a versatile sword.

* **pierce:** 1  
  Ability to bypass some armor.  
  Justification: Thrusting capability allows partial armor penetration.

* **force:** 1  
  Represents impact momentum, potential for knockback or armor sundering.  
  Justification: Heavier than light blades, can deliver impactful blows.

* **block:** 1  
  Dice bonus to blocking maneuvers.  
  Justification: Can be used defensively to block attacks.

* **parry:** 2  
  Strong parrying capability due to balance and design.  
  Justification: Commonly used to deflect blows.

* **hook:** 0  
  Does not provide significant ability to manipulate opponent’s weapon or body.  
  Justification: Longswords are not particularly effective for hooking or disarming.

* **reach:** 1  
  Effective melee distance added to base reach.  
  Justification: Standard reach for a one-handed sword.

* **range:** 0  
  Not applicable for melee weapons.  
  Justification: No ranged capability.
  
* **cover:** 0  
  Not applicable.  
  Justification: Not a shield or cover-providing item.

* **body_dr:** 0  
  No damage reduction provided.  
  Justification: Offensive weapon, not armor.

* **voids_dr:** 0  
  No damage reduction provided.  
  Justification: Offensive weapon, not armor.

---

#### 4. Special Properties

* **stunts:** ["Target Weakness"]  
  Allows use of the "Target Weakness" stunt on PHY actions, representing the longsword's capacity to pierce armor effectively when thrust forcefully.  
  Justification: Items typically enable standard stunts in novel ways rather than granting unique stunts.

* **notes:**  
  Holding the longsword in both hands reduces its AP cost by 1 (minimum 1 AP) for attacks, blocks, parries, and other maneuvers.  
  Justification: Encourages tactical choice between speed and control.

* **references:**  
  Traditional European longsword, widely used in the late medieval period.

* **tags:**  
  * equipment  
  * weapon  
  * bladed

---

## Summary

This step-by-step guide explains the rationale behind each stat and value for a standard longsword. Use this as a reference to maintain consistency, balance, and thematic coherence when designing new equipment.

---

*For other equipment types, adjust stats and justifications accordingly, considering historical and fantasy inspirations, game mechanics, and design philosophy.*
