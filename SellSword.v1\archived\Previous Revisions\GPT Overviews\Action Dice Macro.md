# Current - 241012
(async () => {
  // The arguments passed to the macro are available as global variables:
  // 'entity', 'modifier', 'skillLevel', 'boostType'

  if (!entity) {
    ui.notifications.error('Actor not found.');
    return;
  }

  // Get the actor using fromUuid
  let actor;
  try {
    actor = await fromUuid(entity.uuid);
  } catch (err) {
    console.error(err);
    ui.notifications.error('Failed to retrieve actor from UUID.');
    return;
  }

  if (!actor) {
    ui.notifications.error('Actor not found.');
    return;
  }

  // Ensure modifier and skillLevel are numbers
  modifier = Number(modifier) || 0;
  skillLevel = Number(skillLevel) || 0;
  boostType = boostType || "None"; // "Stamina", "Will", or "None"

  // Retrieve resource pools from the actor's data
  let actionDice = Number(actor.system.props.actionDice) || 0;
  let staminaDice = Number(actor.system.props.staminaDice) || 0;
  let willDice = Number(actor.system.props.willDice) || 0;

  if (actionDice < 1) {
    ui.notifications.warn(`You have no Action Dice left.`);
    return;
  }

  // Determine maximum Action Dice based on skill level
  let maxActionDice = 2; // Default maximum
  if (skillLevel >= 4) {
    maxActionDice = 4;
  } else if (skillLevel >= 2) {
    maxActionDice = 3;
  }

  // Function to handle the action after selecting actionCost and boost
  async function proceedWithAction(actionCost, boost) {
    // Validate actionCost
    if (actionCost < 1) actionCost = 1;
    if (actionCost > maxActionDice) {
      ui.notifications.warn(`Your skill level limits you to ${maxActionDice} Action Dice. Adjusting action cost to ${maxActionDice}.`);
      actionCost = maxActionDice;
    }
    if (actionCost > actionDice) {
      ui.notifications.warn(`You only have ${actionDice} Action Dice. Adjusting action cost to ${actionDice}.`);
      actionCost = actionDice;
    }

    // Decrement player's actionDice
    let newActionDice = Math.max(0, actionDice - actionCost);
    await actor.update({'system.props.actionDice': newActionDice});
    console.log(`Updated actor's actionDice to ${newActionDice}`);

    // Handle boosting
    let boostDice = 0;
    if (boost && boostType !== "None") {
      if (boostType === "Stamina") {
        if (staminaDice < 1) {
          ui.notifications.warn(`You have no Stamina Dice left.`);
        } else {
          boostDice = 1;
          staminaDice -= 1;
          await actor.update({'system.props.staminaDice': staminaDice});
          console.log(`Used 1 Stamina Die. Remaining Stamina Dice: ${staminaDice}`);
        }
      } else if (boostType === "Will") {
        if (willDice < 1) {
          ui.notifications.warn(`You have no Will Dice left.`);
        } else {
          boostDice = 1;
          willDice -= 1;
          await actor.update({'system.props.willDice': willDice});
          console.log(`Used 1 Will Die. Remaining Will Dice: ${willDice}`);
        }
      }
    }

    // Refresh the player's token(s)
    if (canvas && canvas.tokens) {
      let playerTokens = canvas.tokens.placeables.filter(t => t.actor?.id === actor.id);
      for (let token of playerTokens) {
        await token.draw();
        console.log(`Refreshed player token: ${token.name}`);
      }
    } else {
      console.warn('Canvas not available; cannot refresh player tokens.');
    }

    // **Update the Encounter's monsterDice**

    // Find the encounter token by name
    const encounterTokenName = "Encounter"; // Ensure your encounter token is named "Encounter"
    let encounterToken = canvas.tokens.placeables.find(t => t.name === encounterTokenName);
    if (!encounterToken) {
      ui.notifications.error('Encounter token not found.');
      console.error('Encounter token not found.');
      return;
    }

    let encounterActor = encounterToken.actor;
    if (!encounterActor) {
      ui.notifications.error('Encounter actor not found.');
      console.error('Encounter actor not found.');
      return;
    }

    // Get the encounter's difficulty rating
    let difficulty = Number(encounterActor.system.props.difficulty) || 0;
    console.log('Encounter difficulty:', difficulty);

    // Calculate monster dice gained
    let monsterDiceGained = actionCost + difficulty;
    console.log('Monster dice gained:', monsterDiceGained);

    // Update the monster dice pool on the encounter actor
    let currentMonsterDice = Number(encounterActor.system.props.monsterDice) || 0;
    let newMonsterDice = currentMonsterDice + monsterDiceGained;
    console.log(`Updating monsterDice on encounter actor from ${currentMonsterDice} to ${newMonsterDice}`);
    await encounterActor.update({'system.props.monsterDice': newMonsterDice});
    console.log('Encounter actor updated successfully.');

    // Refresh the encounter token
    await encounterToken.draw();
    console.log(`Refreshed encounter token: ${encounterToken.name}`);

    // Proceed with rolling the dice and outputting the result
    // Roll the action dice and boost dice
    let rollFormula = `${actionCost}d6x6[Action]`;
    if (boostDice > 0) {
      if (boostType === "Stamina") {
        rollFormula += ` + ${boostDice}d6x6[Stamina]`;
      } else if (boostType === "Will") {
        rollFormula += ` + ${boostDice}d6x6[Will]`;
      }
    }
    if (modifier !== 0) {
      rollFormula += ` + ${modifier}`;
    }
    let roll = new Roll(rollFormula);
    await roll.evaluate();

    // If Dice So Nice is active, set the dice colors
    if (game.modules.get('dice-so-nice')?.active) {
      for (let term of roll.terms) {
        if (term instanceof foundry.dice.terms.Die) {
          if (term.options.flavor === 'Action') {
            term.options.colorset = 'red';
          } else if (term.options.flavor === 'Stamina') {
            term.options.colorset = 'green';
          } else if (term.options.flavor === 'Will') {
            term.options.colorset = 'blue';
          }
        }
      }
    }

    // Send the roll result to chat
    roll.toMessage({
      speaker: ChatMessage.getSpeaker({actor: actor}),
      flavor: `Rolling ${actionCost} Action Dice${boostDice > 0 ? ` with 1 ${boostType} Die` : ''}${modifier !== 0 ? ` and modifier ${modifier}` : ''}`
    });
  }

  // Build the buttons object dynamically based on allowed action dice
  let buttons = {};
  let maxDice = Math.min(maxActionDice, actionDice, 4); // Limit to 4 buttons
  for (let i = 1; i <= maxDice; i++) {
    buttons[`die${i}`] = {
      icon: `<i class="fas fa-dice-${['one', 'two', 'three', 'four'][i - 1]}"></i>`,
      label: `${i}`,
      callback: async (html) => {
        let boost = html.find('[name="boost"]')[0]?.checked || false;
        await proceedWithAction(i, boost);
      }
    };
  }

  // Dialog content for the boost option if applicable
  let dialogContent = `${boostType !== "None" ? `
    <form>
      <div class="form-group">
        <label for="boost">Boost with ${boostType} Die?</label>
        <input type="checkbox" id="boost" name="boost"/>
      </div>
    </form>
    ` : ''}`;

  let d = new Dialog({
    title: "Action Dice Roll",
    content: dialogContent,
    buttons: buttons,
    default: "die1",
    close: () => {}
  });
  d.render(true);

})();


# ChatGPT work on adding hit mechanics to Macro

## Prompt 1
I now want to look at making a wound macro to apply damage to characters and monsters after they roll an attack with an action die Both monsters and players will have the same variables on their sheets to facilitate this. Defenses: toHit = number the attacker needs to reach to hit them bodyArmor = armor covering primary body parts voidsArmor = armor covering moving parts Offense: slashing = cutting damage they do piercing = armor penetration force = additional striking force available (determined by how much they roll over target) The way I want it to work is modify the Action Dice Macro to look for skills that start with atk, like atkBody, and if it sees that look for a target. It will then do all of the normal it stuff it already does but will also compare the values of the attacker vs the target and determine the outcome based on the roll. I only want it to determine if an attack is succesfull or not and not actually apply any damage. That will be in another macro we will make next. But I do want it to provide the damage info the chat card if it hits and prompt me to apply the damage. If I hit the apply damage button in the chat card it will then run the damage macro.

## Reply 1

- **Target Selection**:
    
    - **How should the macro identify the target?**
        - Should it use the target selected in the Foundry VTT interface (i.e., the token targeted by the attacker)?
	        If a target is selected use that target, if not prompt for one.
        
        - Or do you prefer to select the target in a different way, such as prompting for selection within the macro?
- **Attack Resolution Mechanics**:
    
    - **What is the exact mechanic for determining if an attack hits?**
        - Is it a matter of comparing the attack roll result to the target's `toHit` value?
	    -I am handling all of the various modifiers in the roll message so you should just be able to compare the result of the dice formula as is vs the toHit number of the target. The value that the dice beats the toHit number is important though as that will determine if there is any additional 'force' added.
	    
        - Are there any modifiers, conditions, or additional calculations to consider (e.g., attacker's `force`, target's defenses, situational modifiers)?
        -Yes but I am currently handling those prior to sending the final modifier to the macro
        
- **Damage Information**:
    
    - **What specific damage details do you want displayed in the chat card if the attack hits?**
        - Do you want to include calculations for `slashing`, `piercing`, and `force`?
        -Yes. I want to see the base slashing and piercing vs the armor of whatever they attacked depending on if it was an atkBody, atkVoids, or atkVitals. For ex they roll atkBody and have a base slashing of 1, piercing of 1, and force of 1 and the enemy has a toHit of 6, bodyArmor 2, and voidsArmor 1. The attacker rolls an 8, so they hit the body which has an armor value of 2. So the result would be 1 slashing - 1 bodyArmor(1 less from piercing) for 0 dmg.

		The force equation works like this. For every (5) they roll over the toHit number you can (1) point of force damage. So if we look at the previous example and the attacker now rolls a 12, they beat toHit by 6 and thus can add (1) force damage. So now the equation would be (2) slashing - 1 bodyArmor for 1 slashing dmg. Because the armor was 'defeated' in this case their armor would also take durability damage but that will also be handled in the called damage macro. In this instance the 'force' damage was used to overcome the armor and applies to its durability and not the player. but for instances where the force goes through we would apply that damage also so the target would receive slashing and force damage.
		
        - Should the chat card show the potential damage breakdown or just the total damage?
        -See above
- **Damage Macro Integration**:
    
    - **Do you already have a damage macro prepared, or should the "Apply Damage" button trigger a placeholder for now?**
    -Just a placeholder for the time being but we want to make sure that we have this macro prepared to pass to the damage macro the name of the atk, atkBody, atkVoids etc. The amount of damage, slashing or force, and if the armor was 'defeated' to apply this damage and by how much. 
    
    - How do you envision the communication between the attack macro and the damage macro? I imagine if i hit the apply damage button in the chat card it will pass the relevant info then?
        - Should the damage macro receive parameters from the attack macro (e.g., damage amounts, target information)?
- **Multiple Targets**:
    
    - **Should the macro handle attacks against multiple targets**, such as area-of-effect attacks?
        - If so, how should targets be identified and processed?
    -I would like it to but currently am not sure of the mechanics behind it. I want a player to be able to swing a greatsword per se at 3 goblins if they're all in reach, and be able to pump a bunch of dice into it but as am yet unsure how to resolve that mechanically. I like the idea of having the attacker being able to hit as many targets as they have rolled force points over the target value but then it gets complicated if the targets dodge, or block etc. Because if say the first target chooses to spend an action dice to dodge that would essentially mean no force should be decremented and the swing continues on undeterred or if say one of the target chooses to block, it would then 'stop' the attack from hitting any other targets.
- **Additional Effects and Conditions**:
    
    - **Are there any status effects, buffs, or debuffs that need to be considered** when resolving attacks?
        - For example, conditions that modify the attacker's roll or the target's defenses.
        -Possibly but that will happen in the damage macro if needed.
- **Modifiers and Bonuses**:
    
    - **Should the attack roll include any additional modifiers or bonuses** beyond what's currently handled in the macro?
        - For instance, equipment bonuses, temporary effects, or abilities.
        -No thats all handled ahead of calling the macro currently, excepting if the attacker chooses to boost or not.
- **Defenses and Armor Interaction**:
    
    - **How should the `bodyArmor` and `voidsArmor` values be used in the attack resolution?**
        - Are they considered when determining if an attack hits, or are they used later during damage calculation?
        -During damage calculation as seen above. Armor doesn't have any bearing on being hit but rather reduces damage when hit.
- **Roll Visualization**:
    
    - **Do you have any preferences for how the roll results and damage info are presented in the chat card?**
        - Should the players see all the details, or should some information be hidden? I would prefer for the math that determines the results to be mostly hidden but I want the results to be very clear if that makes sense.
- **Testing and Validation**:
    
    - **Do you have any specific test cases or examples** you'd like to use to validate the macro's functionality once it's updated?
    -Nothing Specific