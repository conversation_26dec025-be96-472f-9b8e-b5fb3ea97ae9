# Standing

This folder contains entries for **Standing**, which represents a character's social capital, connections, reputation, favors owed, or other forms of influence within different social spheres or groups. This system was previously referred to as "Influences".

## System Overview

*   **Function:** Standing types provide narrative advantages, access to resources or information, reputational sway, or special favors based on the character's connections or status. They are distinct from skills (which represent trained abilities) and lore (which represents specific knowledge).
*   **Derived Stat:** A character's general capacity for leveraging these connections is represented by the [[../Attributes/Presence|Presence]]-derived stat `Standing` (`3 + PRE`), which likely determines how many or which Standing types a character can possess or utilize effectively (details TBD).
*   **Usage:** Standing is primarily leveraged during Downtime or Preparation phases but can have situational effects during active play.
*   **Levels:** Individual Standing types (e.g., [[Guild Affiliation]], [[Noble Favor]]) are often tiered (Level 1-3), offering progressively stronger benefits. The notations `(+2)`, `(+3)`, `(ADV)` in the original `Influences.md` likely relate to how these levels interact with checks or the Standing derived stat, but the exact mechanics need clarification.

## File Structure

*   Each distinct Standing type has its own Markdown file within this directory (e.g., `Guild_Affiliation.md`, `Noble_Favor.md`).
*   Files use the `_template_standing.md` structure, detailing:
    *   `name`: The name of the Standing type.
    *   `type`: Standing.
    *   `level_1/2/3_effect`: The narrative or mechanical benefits associated with each level.
    *   `tags`: Relevant keywords.

Refer to individual Standing files for specific details and the [[SellSword_v2_Overview]] or Character Creation rules for the full context.
