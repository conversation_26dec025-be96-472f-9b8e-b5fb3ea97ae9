---
name: Template Feature Name
type: [General | Ancestry | Quirk | Flaw] # Choose one
tags: [tag1, tag2, relevant_action_or_stat]
summary: "A brief one-sentence summary of the feature's effect."
prerequisites: "Optional: Any requirements (e.g., Requires Alfarkin Ancestry, Requires INT 1+)"
related_features: # Optional: Links to synergistic or mutually exclusive features
  - "[[Another_Feature]]"
---

## Description

[Provide flavorful text describing what this feature represents for the character. How does it manifest? What's the concept behind it?]

## Mechanics

[Detail the specific game rule effects. Be precise.]

*   **Effect 1:** [Explain the first mechanical effect. Use links like [[../Attributes/FIN|FIN]] or [[../../Rules/_Rules_README#WagerPush Mechanic|Wager Mechanic]] where appropriate.]
*   **Effect 2 (Optional):** [Explain any additional effects.]
*   **Limitations/Conditions (Optional):** [Specify any triggers or costs, such as resource expenditure (e.g., "Costs 1 Will to activate") or situational conditions (e.g., "Only applies under X").]
