# Equipment

A concise overview of the purpose and scope of the **Equipment** pillar.

## Prerequisites

- Read the [Project Overview](SellSword_v2_Overview.md).  
- Familiarize yourself with the core rules: [Core Rules](_Rules_README.md).

## Folder Structure

- `/Equipment/` – Main directory for this pillar’s entries.  
- `_template_equipment.md` – Template file for creating new equipment entries.  
- Subdirectories:  
  - `Weapons/`  
  - `Armor/`  
  - `Shields/`  
  - `Ammunition/`  
  - `Utility/`  

## Content Overview

Describe the types of files and data in this pillar, including:

- Melee and ranged weapons (`Weapons/`), armor pieces (`Armor/`), shields (`Shields/`), ammunition (`Ammunition/`), and utility items (`Utility/`).  
- Each entry file uses YAML frontmatter to define stats and metadata (e.g., `name`, `type`, `carry`, `offense`, `damage`, `pierce`, `force`, `block`, `parry`, `hook`, `reach`, `range`, `dr`, etc.).  
- Naming conventions: Title case with underscores (e.g., `Longsword.md`).

## Conventions

- **File Naming:** Title case, underscores for spaces (e.g., `My_Equipment_Item.md`).  
- **Frontmatter Fields:**  
  - `name`: Display name  
  - `type`: Category (e.g., Weapon, Armor)  
  - `tags`: List of relevant tags  
  - `summary`: Short description  
  - **Stats Fields:** offense, damage, pierce, force, block, parry, hook, reach, range, dr/body_dr/voids_dr, carry, encumbrance, fatigue, durability, etc.  
- **Links:** Use Obsidian-style `[[...]]` links for cross-references.

## How to Use

1. Navigate to the `Equipment/` directory.  
2. Browse entries by category or name.  
3. Open an entry file to review stats and descriptions.

## Related Links

- [[SellSword_v2_Overview|Project Overview]]  
- [[_Rules_README|Core Rules]]  
- [[_Attributes_README|Attributes & Actions]]  
- [[_Skills_README|Skills Overview]]  
- [[_Stunts_README|Stunts List]]  
- [[_Lore_README|Lore & Wyrds]]  
- [[_Standing_README|Standing System]]  
- [[_Monsters_README|Monster Design]]  
- [[_Wounds_README|Wounds System]]  
- [[_World_README|World Overview]]

## Adding New Entries

1. Copy `_template_equipment.md` into this folder.  
2. Rename it using Title case with underscores.  
3. Fill in the YAML frontmatter and content sections.  
4. Commit your changes with a descriptive message.

## Revision History

- v2 – YYYY‑MM‑DD – Initial template for Equipment pillar README.
