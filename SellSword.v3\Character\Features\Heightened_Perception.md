---
name: Heightened Perception
type: [General | Ancestry | Quirk | Flaw]
tags: [tag1, tag2, relevant_action_or_stat]
summary: "A brief one-sentence summary of the feature's effect."
prerequisites: ""
related_features:
  - "[[Another_Feature]]"
---

## Description

[Placeholder: Provide flavorful text describing how Heightened Perception manifests, such as noticing minute environmental cues and tracking subtle movements.]

## Mechanics

* **Effect Placeholder:** [Insert game mechanic, e.g., when making FCS (Focus) or PRC (Precision) actions to detect hidden details, gain +1 die; spend 1 Will to automatically notice traps once per scene.]

*   **Effect 1:** [Explain the first mechanical effect. Use links like [[../Attributes/FIN|FIN]] or [[../../Rules/_Rules_README#WagerPush Mechanic|Wager Mechanic]] where appropriate.]
*   **Effect 2 (Optional):** [Explain any additional effects.]
*   **Limitations/Conditions (Optional):** [Specify any restrictions, triggers, or costs (e.g., "Usable once per scene," "Only applies when X," "Costs 1 Will to activate").]
